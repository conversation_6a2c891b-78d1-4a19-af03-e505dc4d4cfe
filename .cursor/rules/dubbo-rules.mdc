---
description: 高端订单中心Dubbo接口定义和实现规范
globs:
  - "**/*Facade.java"
  - "**/*FacadeService.java"
  - "**/*Request.java"
  - "**/*Response.java"
alwaysApply: false
---

# 高端订单中心Dubbo接口生成规则
# 作者: hongdong.xie
# 日期: 2025-03-20 14:30:00
# 版本: 1.0.0

#==================== Dubbo接口生成规范 ====================

# Dubbo接口包名与命名规范
dubbo_interface_rules:
  - 查询类接口主包：com.howbuy.tms.high.orders.facade.search
  - 交易类接口主包：com.howbuy.tms.high.orders.facade.trade
  - 子包命名：按具体业务功能命名，如queryacctbalance、queryacctbalancedtl
  - 接口命名：业务功能+Facade (如QueryAcctBalanceFacade)
  - 接口继承：BaseFacade<请求类型, 响应类型>
  - 请求类型：业务功能+Request
  - 响应类型：业务功能+Response

# 接口修改/新增规则
interface_modify_rules:
  - 判断依据：
    - 设计文档中接口名后标注"——修改"的，表示在原有接口修改
    - 设计文档中接口名后标注"——新增"的，表示新增接口
    - 设计文档中接口名后无标注的，表示新增接口
  - 修改原则：
    - 修改接口时，保持原有的路径和方法名不变
    - 修改接口时，需要兼容原有的功能
    - 修改接口时，新增字段采用追加方式，不要删除或修改原有字段
    - 修改接口时，保持原有的注释格式，仅更新内容

#==================== Dubbo接口实现规范 ====================

# Dubbo接口实现类规范
dubbo_impl_rules:
  - 实现类包名规则：
    - 查询类主包：com.howbuy.tms.high.orders.service.facade.search
    - 交易类主包：com.howbuy.tms.high.orders.service.facade.trade
    - 子包结构：应与接口包结构对应，如search.queryacctbalance
  - 实现类命名：接口名+Service (如QueryAcctBalanceFacadeService)
  - 必须实现相应接口：implements XXXFacade
  - 注解使用：
    - @DubboService - 标识为Dubbo服务
    - 查询类使用@Component - 注册为Spring组件
    - 交易类使用@Service("beanName") - 注册为Spring服务
  - 日志记录：使用private static final Logger logger = LogManager.getLogger()
  - Service层交互：通过@Autowired注入所需的Service类

# Dubbo接口与实现同步生成规则
dubbo_sync_generate_rules:
  - 生成顺序：
    1. 根据接口文档在client模块中生成接口定义
    2. 在service模块中生成对应的接口实现类
    3. 实现类中主要负责参数校验和Service层调用
  - 同步原则：
    - 接口名变更时，实现类名也必须同步变更
    - 接口方法变更时，实现类方法也必须同步变更
    - 接口参数变更时，实现类参数也必须同步变更
  - 查询类实现模板：
    ```java
    @DubboService
    @Component
    public class XXXFacadeService implements XXXFacade {
        
        private static final Logger logger = LogManager.getLogger(XXXFacadeService.class);
        
        @Autowired
        private XXXService xxxService;
        
        @Override
        public XXXResponse execute(XXXRequest request) {
            logger.info("接口调用开始，请求参数：{}", JsonUtils.toJsonString(request));
            
            try {
                XXXResponse response = xxxService.process(request);
                logger.info("接口调用成功");
                return response;
            } catch (Exception e) {
                logger.error("接口调用异常：{}", e.getMessage(), e);
                throw e;
            }
        }
    }
    ```

# 请求对象规范
request_rules:
  - 查询类请求对象包名：com.howbuy.tms.high.orders.facade.search.{业务子包}
  - 交易类请求对象包名：com.howbuy.tms.high.orders.facade.trade.{业务子包}
  - 类命名：业务功能+Request
  - 查询类继承：OrderSearchBaseRequest
  - 交易类继承：OrderTradeBaseRequest
  - 注解使用：@Getter/@Setter (不使用@Data)
  - 构造函数：设置交易码setTxCode(TxCodes.XXX)
  - 参数验证：使用@MyValidation注解
  - @MyValidation注解类型：使用com.howbuy.commons.validator.MyValidation
  - ValidatorTypeEnum类型：使用com.howbuy.commons.validator.util.ValidatorTypeEnum
  - List、Map等集合对象类型参数，即使需求文档要求必填，也不要加@MyValidation，必填验证在实现类通过代码实现
  - 数值类型：使用BigDecimal表示金额
  - 日期时间：使用String类型，格式在注释中说明

# 响应对象规范
response_rules:
  - 查询类响应对象包名：com.howbuy.tms.high.orders.facade.search.{业务子包}
  - 交易类响应对象包名：com.howbuy.tms.high.orders.facade.trade.{业务子包}
  - 类命名：业务功能+Response
  - 查询类继承：OrderSearchBaseResponse
  - 交易类继承：OrderTradeBaseResponse
  - 注解使用：@Getter/@Setter
  - 序列化：实现Serializable接口，定义serialVersionUID
  - 字段说明：每个字段必须添加中文注释

#==================== APIDOC注释规范 ====================

# Dubbo接口APIDOC规范
dubbo_apidoc_rules:
  - 必须包含以下标签：
    - @api {DUBBO} 接口全类名.execute()
    - @apiVersion 版本号
    - @apiGroup 实现类名
    - @apiName execute
    - @apiDescription 接口功能描述
    - @apiParam (请求参数) {类型} 参数名 参数说明
    - @apiParamExample 请求参数示例
    - @apiSuccess (响应结果) {类型} 字段名 字段说明
    - @apiSuccessExample 响应结果示例
  - 特殊要求：
    - 请求和响应示例必须包含真实业务场景数据
    - 字段说明必须包含数据格式要求（如日期格式YYYYMMDD）
    - 枚举值必须说明所有可能的取值（如1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构）
    - 金额字段必须说明精度
    - 生成的apidoc在实现类的execute方法上面，不要放到接口定义上面

# 实现类注释规范
impl_comment_rules:
  ```
  /**
   * @description: 接口实现功能描述
   * @author: hongdong.xie
   * @date: 2025/3/20 14:30
   * @since JDK 1.8
   */
  ```

# 类注释规范
class_comment_rules:
  ```
  /**
   * @description: 类的功能描述
   * @author: hongdong.xie
   * @date: 2025/3/20 14:30
   * @since JDK 1.8
   */
  ```

# 字段注释规范
field_comment_rules:
  ```
  /**
   * 字段中文名称
   * 可选：附加说明（如：格式要求/取值范围）
   */
  ```

#==================== 参数验证规范 ====================

# 参数验证规则
validation_rules:
  - 必填字符串：@MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "中文名", isRequired = true)
  - 可选字符串：@MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "中文名", isRequired = false)
  - 必填金额：@MyValidation(validatorType = ValidatorTypeEnum.Money, fieldName = "中文名", isRequired = true)
  - 必填日期：@MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "日期", isRequired = true)

#==================== 代码示例 ====================

# Dubbo查询接口示例
dubbo_search_interface_example:
  ```java
  /**
   * @description: 查询客户持仓信息
   * @author: hongdong.xie
   * @date: 2025/3/20 14:30
   * @since JDK 1.8
   */
  public interface QueryAcctBalanceFacade extends BaseFacade<QueryAcctBalanceRequest, QueryAcctBalanceResponse> {

  }
  ```

# Dubbo查询接口实现类示例
dubbo_search_impl_example:
  ```java
  /**
   * @description: 查询客户持仓信息实现
   * @author: hongdong.xie
   * @date: 2025/3/20 14:30
   * @since JDK 1.8
   */
  @DubboService
  @Component
  public class QueryAcctBalanceFacadeService implements QueryAcctBalanceFacade {

      private static final Logger logger = LogManager.getLogger(QueryAcctBalanceFacadeService.class);

      @Autowired
      private QueryAcctBalanceService queryAcctBalanceService;

      /**
       * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceFacade.execute()
       * @apiVersion 1.0.0
       * @apiGroup QueryAcctBalanceFacadeService
       * @apiName execute
       * @apiDescription 查询产品持仓接口实现(整个高端持仓基础接口)
       * @apiParam (请求参数) {String} hkSaleFlag 好买香港代销标识
       * @apiParam (请求参数) {String} productCode 产品代码
       * @apiParam (请求参数) {String} productType 产品类型
       * @apiParam (请求参数) {String} productSubType 产品子类型
       * @apiParam (请求参数) {String} protocolType 协议类型，4-高端产品协议
       * @apiParam (请求参数) {Array} disCodeList 分销机构代码列表-股权直销改造
       * @apiParam (请求参数) {String} callType 1-新资产中心，2-老资产中心
       * @apiParam (请求参数) {String} balanceStatus 持仓状态,0:不持仓,1:持仓,2:全部，默认查持仓的
       * @apiParam (请求参数) {String} notFilterHkFund 不过滤香港产品,1:是,0:否
       * @apiParam (请求参数) {String} notFilterHzFund 不过滤好臻产品,1:是,0:否
       * @apiParam (请求参数) {String} txAcctNo 交易账号
       * @apiParam (请求参数) {String} hbOneNo 一账通账号
       * @apiParam (请求参数) {String} disCode 分销机构代码
       * @apiParam (请求参数) {String} outletCode 网点代码
       * @apiParam (请求参数) {String} appDt 申请日期
       * @apiParam (请求参数) {String} appTm 申请时间
       * @apiParam (请求参数) {String} operIp 交易IP
       * @apiParam (请求参数) {String} txCode 交易码
       * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
       * @apiParamExample 请求参数示例
       * {"hkSaleFlag":"1","productCode":"HM001","productType":"1","productSubType":"01","protocolType":"4","disCodeList":["HM","HZ"],"callType":"2","balanceStatus":"1","notFilterHkFund":"0","notFilterHzFund":"0","txAcctNo":"123456789","hbOneNo":"HB123456","disCode":"HM","outletCode":"001","appDt":"20250320","appTm":"143000","operIp":"***********","txCode":"HIGH_FUND_QUERY_ACCT_BALANCE","txChannel":"2"}
       * @apiSuccess (响应结果) {String} txAcctNo 交易账号
       * @apiSuccess (响应结果) {String} disCode 分销机构代码
       * @apiSuccess (响应结果) {Array} disCodeList 分销机构代码列表
       * @apiSuccess (响应结果) {Array} balanceList 持仓信息列表
       * @apiSuccess (响应结果) {String} balanceList.productCode 产品代码
       * @apiSuccess (响应结果) {String} balanceList.productName 产品名称
       * @apiSuccess (响应结果) {Number} balanceList.balanceVol 持仓份额
       * @apiSuccess (响应结果) {Number} balanceList.marketValue 市值
       * @apiSuccess (响应结果) {String} returnCode 返回码
       * @apiSuccess (响应结果) {String} description 描述信息
       * @apiSuccessExample 响应结果示例
       * {"txAcctNo":"123456789","disCode":"HM","disCodeList":["HM","HZ"],"balanceList":[{"productCode":"HM001","productName":"好买基金1号","balanceVol":"10000.00","marketValue":"10500.00"}],"returnCode":"0000","description":"成功"}
       */
      @Override
      public QueryAcctBalanceResponse execute(QueryAcctBalanceRequest request) {
          logger.info("查询客户持仓信息开始，请求参数：{}", JsonUtils.toJsonString(request));
          
          try {
              // 1.参数变更设置
              resetParam(request);
              
              // 2.返回结果构建
              QueryAcctBalanceResponse response = new QueryAcctBalanceResponse();
              response.setTxAcctNo(request.getTxAcctNo());
              response.setDisCode(request.getDisCode());
              response.setReturnCode(ExceptionCodes.SUCCESS);
              response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));
              
              // 3.业务逻辑处理
              // 具体业务逻辑实现...
              
              logger.info("查询客户持仓信息成功");
              return response;
          } catch (Exception e) {
              logger.error("查询客户持仓信息异常：{}", e.getMessage(), e);
              throw e;
          }
      }
  }
  ```

# 查询请求对象示例
search_request_example:
  ```java
  /**
   * @description: 查询客户持仓请求
   * @author: hongdong.xie
   * @date: 2025/3/20 14:30
   * @since JDK 1.8
   */
  @Getter
  @Setter
  public class QueryAcctBalanceRequest extends OrderSearchBaseRequest {

      private static final long serialVersionUID = 1L;

      public QueryAcctBalanceRequest() {
          setTxCode(TxCodes.HIGH_FUND_QUERY_ACCT_BALANCE);
          setDisCode(DisCodeEnum.HM.getCode());
      }

      /**
       * 好买香港代销标识
       */
      private String hkSaleFlag;

      /**
       * 产品代码
       */
      private String productCode;

      /**
       * 产品类型
       */
      private String productType;

      /**
       * 产品子类型
       */
      private String productSubType;

      /**
       * 协议类型
       * 4-高端产品协议
       */
      private String protocolType = "4";

      /**
       * 分销机构代码列表
       * 股权直销改造
       */
      private List<String> disCodeList;

      /**
       * 调用类型
       * 1-新资产中心，2-老资产中心
       */
      private String callType = "2";

      /**
       * 持仓状态
       * 0-不持仓，1-持仓，2-全部，默认查持仓
       */
      private String balanceStatus = "1";

      /**
       * 不过滤香港产品
       * 1-是，0-否
       */
      private String notFilterHkFund;

      /**
       * 不过滤好臻产品
       * 1-是，0-否
       */
      private String notFilterHzFund;
  }
  ```

# 查询响应对象示例
search_response_example:
  ```java
  /**
   * @description: 查询客户持仓响应
   * @author: hongdong.xie
   * @date: 2025/3/20 14:30
   * @since JDK 1.8
   */
  @Getter
  @Setter
  public class QueryAcctBalanceResponse extends OrderSearchBaseResponse {

      private static final long serialVersionUID = 1L;

      /**
       * 交易账号
       */
      private String txAcctNo;

      /**
       * 分销机构代码
       */
      private String disCode;

      /**
       * 分销机构代码列表
       */
      private List<String> disCodeList;

      /**
       * 持仓信息列表
       */
      private List<BalanceBean> balanceList;
  }
  ```

# 生成指南
generate_guide:
  1. 明确业务需求，确定接口名称和功能
  2. 判断是查询类还是交易类接口
  3. 在正确的包路径下创建接口和对应的请求/响应类
  4. 同步在service模块中创建对应的实现类
  5. 按规范编写APIDOC注释，确保包含所有必要标签
  6. 为请求对象的字段添加适当的验证注解
  7. 所有字段添加明确的中文注释，说明用途和格式要求
  8. 提供真实的请求和响应示例
  9. 对枚举值和特殊格式字段提供详细说明
  10. 添加完整的日志记录和异常处理

#==================== 查询类与交易类接口的主要区别 ====================

# 查询类与交易类接口差异
search_vs_trade_differences:
  1. 包路径：search.{功能} vs trade.{功能}
  2. Spring注解：查询类使用@Component，交易类使用@Service("beanName")
  3. 请求继承：查询类继承OrderSearchBaseRequest，交易类继承OrderTradeBaseRequest
  4. 响应继承：查询类继承OrderSearchBaseResponse，交易类继承OrderTradeBaseResponse
  5. 幂等性：交易类通常需要实现IdempotentSupport接口
  6. 事务控制：交易类需要事务管理，查询类通常只读
  7. 权限控制：交易类权限控制更严格
  8. 日志记录：交易类日志记录更详细


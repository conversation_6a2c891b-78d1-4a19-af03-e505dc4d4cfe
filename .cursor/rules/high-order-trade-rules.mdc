---
description: 创建交易接口定义和实现规则
globs:
alwaysApply: false
---
# 高端交易类Dubbo接口生成规则
#==================== 交易类Dubbo接口生成规范 ====================
# 交易类Dubbo接口包名与命名规范
trade_interface_rules:
  - 接口主包：com.howbuy.tms.high.orders.facade
  - 交易子包：com.howbuy.tms.high.orders.facade.trade.{具体业务功能}
  - 接口命名：业务功能+Facade (如ModifyRefundFacade)
  - 接口继承：BaseFacade<请求类型, 响应类型>
  - 请求类型：业务功能+Request
  - 响应类型：业务功能+Response

# 交易类接口实现类规范
trade_impl_rules:
  - 实现类包名规则：
    - 主包：com.howbuy.tms.high.orders.service.facade
    - 子包结构：应与接口包结构对应，如trade.{具体业务功能}
  - 实现类命名：接口名+Service (如ModifyRefundFacadeService)
  - 必须实现相应接口：implements XXXFacade
  - 注解使用：
    - @Slf4j 或 private static final Logger logger = LogManager.getLogger() - 用于日志记录
    - @DubboService - 标识为Dubbo服务
    - @Service("接口名首字母小写") - 注册为Spring服务，如@Service("modifyRefundFacade")

# 交易接口参数规范
trade_request_rules:
  - 请求对象包名：com.howbuy.tms.high.orders.facade.trade.{具体业务功能}
  - 类命名：业务功能+Request
  - 继承类：OrderTradeBaseRequest 或 BaseRequest
  - 实现接口：IdempotentSupport (需要保证幂等性的接口)
  - 构造函数：设置交易码 setTxCode(TxCodes.XXX)
  - 重写方法：toIdempotentString() - 生成幂等性标识字符串
  - 通用参数：
    - 交易账号：txAcctNo
    - 一账通号：hbOneNo
    - 外部订单号：externalDealNo
    - 分销机构号：disCode
    - outletCode：网点号
    - subOutletCode：子网点号
    - 交易日期时间：appDt, appTm
    - 交易IP：operIp
    - 交易码：txCode
    - 交易渠道：txChannel
  - 业务参数：根据具体业务功能设计，必须添加详细注释

# 交易接口响应规范
trade_response_rules:
  - 响应对象包名：com.howbuy.tms.high.orders.facade.trade.{具体业务功能}
  - 类命名：业务功能+Response
  - 继承类：OrderTradeBaseResponse 或 BaseResponse
  - 通用响应字段：
    - 返回码：returnCode
    - 描述信息：description
    - 订单号：dealNo (交易类接口特有)
  - 业务响应字段：根据业务需求定义，通常较少

#==================== 交易类接口APIDOC注释规范 ====================

# 交易类接口APIDOC规范
trade_apidoc_rules:
  - 接口描述格式：
    ```
    * @api {DUBBO} com.howbuy.tms.high.orders.facade.trade.具体业务功能.接口名称.execute()
    * @apiVersion 版本号
    * @apiGroup 实现类名
    * @apiName execute
    * @apiDescription 接口功能描述
    ```
  - 请求参数格式：
    ```
    * @apiParam (请求参数) {类型} 参数名 参数说明
    ```
  - 响应结果格式：
    ```
    * @apiSuccess (响应结果) {类型} 字段名 字段说明
    ```
  - 示例格式：
    ```
    * @apiParamExample 请求参数示例
    * 参数示例
    * @apiSuccessExample 响应结果示例
    * 响应示例
    ```

# 交易接口实现类注释规范
trade_impl_comment_rules:
  ```
  /**
   *
   * @description:(接口功能描述)
   * @reason:
   * <AUTHOR>
   * @date 日期
   * @since JDK 1.8
   */
  ```

# 交易接口业务方法注释规范
trade_business_method_comment_rules:
  ```
  /**
   * @description:方法功能描述
   * @param 参数名 参数说明
   * @return 返回类型 返回说明
   * @author: 作者
   * @date: 年/月/日 时:分
   * @since JDK 1.8
   */
  ```

#==================== 代码示例 ====================

# 交易类Dubbo接口示例
trade_interface_example:
  ```java
  /**
   * @description:修改回款信息
   * @author: chuanguang.tang
   * @date: 2021/7/30 13:44
   * @since JDK 1.8
   */
  public interface ModifyRefundFacade extends BaseFacade<ModifyRefundRequest, ModifyRefundResponse> {

  }
  ```

# 交易请求对象示例
trade_request_example:
  ```java
  /**
   * @description:修改回款信息
   * @author: chuanguang.tang
   * @date: 2021/7/30 13:44
   * @since JDK 1.8
   */
  public class ModifyRefundRequest extends OrderTradeBaseRequest implements IdempotentSupport {

      private static final long serialVersionUID = 2353501722836316016L;

      /**
       * 订单号
       */
      private String dealNo;

      /**
       * 回款金额
       */
      private BigDecimal refundAmt;

      /**
       * 回款备注
       */
      private String refundMemo;

      /**
       * 回款方向
       */
      private String refundDir;

      public ModifyRefundRequest() {
          setTxCode(TxCodes.HIGH_DEAL_ORDER_REFUND_MODIFY);
      }

      // getters and setters 省略

      @Override
      public String toIdempotentString() {
          StringBuilder idempotent = new StringBuilder(100);
          idempotent.append(getTxAcctNo());
          idempotent.append(getDealNo());
          idempotent.append(getTxCode());
          if (null != getExternalDealNo()) {
              idempotent.append(getExternalDealNo());
          } else {
              idempotent.append(getAppDt());
              idempotent.append(getShortAppTm());
          }

          return idempotent.toString();
      }
  }
  ```

# 交易响应对象示例
trade_response_example:
  ```java
  /**
   * @description:修改回款信息
   * @author: chuanguang.tang
   * @date: 2021/7/30 13:44
   * @since JDK 1.8
   */
  public class ModifyRefundResponse extends OrderTradeBaseResponse {

      private static final long serialVersionUID = -1923726897813510466L;

      // 业务响应字段 (如果有)
  }
  ```

# 交易接口实现类示例
trade_impl_example:
  ```java
  /**
   * @description:修改回款信息
   * @author: chuanguang.tang
   * @date: 2021/7/30 13:46
   * @since JDK 1.8
   */
  @DubboService
  @Service("modifyRefundFacade")
  public class ModifyRefundFacadeService implements ModifyRefundFacade {

      private static final Logger logger = LogManager.getLogger();

      @Autowired
      private HighDealOrderDtlRepository highDealOrderDtlRepository;

      @Autowired
      private DealCompositeRepository dealCompositeRepository;

      /**
       * @api {DUBBO} com.howbuy.tms.high.orders.facade.trade.modifyrefund.ModifyRefundFacade.execute()
       * @apiVersion 1.0.0
       * @apiGroup ModifyRefundFacadeService
       * @apiName execute
       * @apiParam (请求参数) {String} dealNo 订单号
       * @apiParam (请求参数) {Number} refundAmt 回款金额
       * @apiParam (请求参数) {String} refundMemo 回款备注
       * @apiParam (请求参数) {String} refundDir 回款方向
       * @apiParam (请求参数) {String} txAcctNo 交易账号
       * @apiParam (请求参数) {String} hbOneNo 一账通账号
       * @apiParam (请求参数) {String} externalDealNo 外部订单号
       * @apiParam (请求参数) {String} disCode 分销机构代码
       * @apiParam (请求参数) {String} outletCode 网点代码
       * @apiParam (请求参数) {String} appDt 申请日期
       * @apiParam (请求参数) {String} appTm 申请时间
       * @apiParam (请求参数) {String} operIp 交易IP
       * @apiParam (请求参数) {String} txCode 交易码
       * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
       * @apiParamExample 请求参数示例
       * hbOneNo=rA&externalDealNo=x6ZvDIAPIl&disCode=pHEF&dealNo=uE5&txChannel=NOoL&refundMemo=silPFc&appTm=3&subOutletCode=Ebj&operIp=JduheGvS&txAcctNo=zq&refundDir=ENk0pBjnz&appDt=b3vuQgaEk6&txCode=GBiLVi&outletCode=ETMm&refundAmt=2562.31
       * @apiSuccess (响应结果) {String} dealNo 订单号
       * @apiSuccess (响应结果) {String} returnCode 返回码
       * @apiSuccess (响应结果) {String} description 描述
       * @apiSuccessExample 响应结果示例
       * {"returnCode":"q49XwU","description":"7ZRFRf60e","dealNo":"CHa"}
       */
      @Override
      @Idempotent
      public Response<ModifyRefundResponse> execute(ModifyRefundRequest request) {
          logger.info("修改回款信息，请求参数：{}", JsonUtils.toJsonString(request));

          // 业务逻辑实现
          // ...

          ModifyRefundResponse response = new ModifyRefundResponse();
          response.setDealNo(request.getDealNo());
          response.setReturnCode(OutReturnCodes.SUCCESS);
          response.setDescription("修改回款信息成功");

          return Response.buildSuccessResponse(response);
      }

      /**
       * @description:业务方法示例
       * @param request 请求对象
       * @param highDealOrderDtlPo 订单明细对象
       * @return void
       * @author: hongdong.xie
       * @date: 2025/03/20 10:42:24
       * @since JDK 1.8
       */
      private void businessMethod(ModifyRefundRequest request, HighDealOrderDtlPo highDealOrderDtlPo) {
          // 业务处理逻辑
      }
  }
  ```

# 生成指南
trade_generate_guide:
  1. 明确交易业务需求，确定接口名称和功能
  2. 在正确的包路径下创建接口和对应的请求/响应类
  3. 请求类继承OrderTradeBaseRequest，实现IdempotentSupport接口
  4. 构造函数中设置交易码
  5. 实现toIdempotentString()方法，确保交易幂等性
  6. 响应类继承OrderTradeBaseResponse
  7. 在service模块中创建对应的实现类
  8. 实现类添加@DubboService和@Service注解
  9. 按规范编写APIDOC注释，确保包含所有必要标签
  10. 所有字段添加明确的中文注释
  11. 非简单方法添加详细的方法注释
  12. 添加@Idempotent注解在execute方法上确保幂等性
  13. 日志记录请求和响应信息

#==================== 交易类接口关键差异 ====================

# 交易类接口与查询类接口的主要区别
trade_vs_query_differences:
  1. 包路径：trade.{功能} vs search.{功能}
  2. 幂等性：交易类必须实现IdempotentSupport接口和toIdempotentString()方法
  3. 注解：交易类execute方法通常需添加@Idempotent注解
  4. Spring注解：交易类使用@Service("beanName")，查询类使用@Component
  5. 请求继承：交易类通常继承OrderTradeBaseRequest，查询类继承BaseRequest
  6. 响应继承：交易类通常继承OrderTradeBaseResponse，查询类直接实现Serializable
  7. 构造函数：交易类需在构造函数中设置对应的交易码
  8. 数据变更：交易类会修改数据，需要事务控制，查询类只读取数据
  9. 安全控制：交易类通常权限控制更严格
  10. 日志跟踪：交易类日志记录更详细，包括操作人、操作时间等信息
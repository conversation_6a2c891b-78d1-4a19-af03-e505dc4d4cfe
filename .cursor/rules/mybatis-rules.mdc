---
description: 高端订单中心MyBatis数据库操作规范
globs:
  - "**/*Po.java"
  - "**/*PoMapper.java"
  - "**/*PoAutoMapper.java"
  - "**/*PoMapper.xml"
  - "**/*PoAutoMapper.xml"
alwaysApply: false
---

# 高端订单中心MyBatis数据库操作规则
# 作者: hongdong.xie
# 日期: 2025-03-20 14:30:00
# 版本: 1.0.0

#==================== 目录结构规范 ====================

# PO类目录结构
po_directory_rules:
  - PO类路径：high-order-center-dao/src/main/java/com/howbuy/tms/high/orders/dao/po/
  - 命名规范：表名转驼峰命名法 + Po后缀
  - 示例：SubCustBooksPo.java、CustBooksPoExample.java

# 基础Mapper接口目录结构
base_mapper_directory_rules:
  - 基础Mapper路径：high-order-center-dao/src/main/java/com/howbuy/tms/high/orders/dao/mapper/
  - 命名规范：表名转驼峰命名法 + PoAutoMapper后缀
  - 示例：SubCustBooksPoAutoMapper.java

# 基础Mapper XML目录结构
base_mapper_xml_directory_rules:
  - 基础XML路径：high-order-center-dao/src/main/resources/com/howbuy/tms/high/orders/dao/mapper/
  - 命名规范：表名转驼峰命名法 + PoAutoMapper.xml
  - 示例：SubCustBooksPoAutoMapper.xml

# 自定义Mapper接口目录结构
custom_mapper_directory_rules:
  - 自定义Mapper路径：high-order-center-dao/src/main/java/com/howbuy/tms/high/orders/dao/mapper/customize/
  - 命名规范：表名转驼峰命名法 + PoMapper后缀
  - 示例：SubCustBooksPoMapper.java

# 自定义Mapper XML目录结构
custom_mapper_xml_directory_rules:
  - 自定义XML路径：high-order-center-dao/src/main/resources/com/howbuy/tms/high/orders/dao/mapper/customize/
  - 命名规范：表名转驼峰命名法 + PoMapper.xml
  - 示例：SubCustBooksPoMapper.xml

#==================== 命名规范 ====================

# PO类命名规范
po_naming_rules:
  - 类名：表名转驼峰命名法 + Po后缀
  - 示例：表名SUB_CUST_BOOKS对应SubCustBooksPo
  - Example类：表名转驼峰命名法 + PoExample后缀
  - 示例：SubCustBooksPoExample
  - 注解使用：不使用@Data，使用getter/setter方法

# Mapper接口命名规范
mapper_naming_rules:
  - 基础Mapper：表名转驼峰命名法 + PoAutoMapper后缀
  - 自定义Mapper：表名转驼峰命名法 + PoMapper后缀
  - 示例：SubCustBooksPoAutoMapper、SubCustBooksPoMapper

# Mapper XML命名规范
mapper_xml_naming_rules:
  - 基础XML：表名转驼峰命名法 + PoAutoMapper.xml
  - 自定义XML：表名转驼峰命名法 + PoMapper.xml
  - 示例：SubCustBooksPoAutoMapper.xml、SubCustBooksPoMapper.xml

#==================== PO类代码规范 ====================

# PO类基本结构
po_class_structure:
  ```java
  package com.howbuy.tms.high.orders.dao.po;

  import java.io.Serializable;
  import java.math.BigDecimal;
  import java.util.Date;

  /**
   * @description: 表功能描述
   * @author: hongdong.xie
   * @date: 2025/3/20 14:30
   * @since JDK 1.8
   */
  public class TableNamePo implements Serializable {
      
      private static final long serialVersionUID = 1L;

      /**
       * 字段注释
       */
      private FieldType fieldName;

      // getter和setter方法
      public FieldType getFieldName() {
          return fieldName;
      }

      public void setFieldName(FieldType fieldName) {
          this.fieldName = fieldName;
      }
  }
  ```

# PO类字段命名规范
po_field_naming_rules:
  - 字段名：数据库字段名转小驼峰命名法
  - 示例：EXT_VOL_DTL_NO -> extVolDtlNo
  - 示例：TX_ACCT_NO -> txAcctNo
  - 示例：FUND_CODE -> fundCode

# PO类字段类型映射规范
po_field_type_mapping:
  - BIGINT -> Long
  - INT -> Integer
  - DECIMAL -> BigDecimal
  - VARCHAR -> String
  - CHAR -> String
  - TIMESTAMP -> Date
  - DATE -> Date
  - TEXT -> String
  - LONGTEXT -> String

#==================== 基础Mapper接口规范 ====================

# 基础Mapper接口结构
base_mapper_interface_structure:
  ```java
  package com.howbuy.tms.high.orders.dao.mapper;

  import com.howbuy.tms.high.orders.dao.po.TableNamePo;
  import com.howbuy.tms.high.orders.dao.po.TableNamePoExample;
  import java.util.List;
  import org.apache.ibatis.annotations.Param;

  /**
   * @description: 表名基础Mapper
   * @author: hongdong.xie
   * @date: 2025/3/20 14:30
   * @since JDK 1.8
   */
  public interface TableNamePoAutoMapper {
      long countByExample(TableNamePoExample example);

      int deleteByExample(TableNamePoExample example);

      int deleteByPrimaryKey(PrimaryKeyType primaryKey);

      int insert(TableNamePo record);

      int insertSelective(TableNamePo record);

      List<TableNamePo> selectByExample(TableNamePoExample example);

      TableNamePo selectByPrimaryKey(PrimaryKeyType primaryKey);

      int updateByExampleSelective(@Param("record") TableNamePo record, @Param("example") TableNamePoExample example);

      int updateByExample(@Param("record") TableNamePo record, @Param("example") TableNamePoExample example);

      int updateByPrimaryKeySelective(TableNamePo record);

      int updateByPrimaryKey(TableNamePo record);
  }
  ```

#==================== 基础Mapper XML规范 ====================

# 基础Mapper XML结构
base_mapper_xml_structure:
  ```xml
  <?xml version="1.0" encoding="UTF-8"?>
  <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
  <mapper namespace="com.howbuy.tms.high.orders.dao.mapper.TableNamePoAutoMapper">
    <resultMap id="BaseResultMap" type="com.howbuy.tms.high.orders.dao.po.TableNamePo">
      <id column="主键列名" jdbcType="主键类型" property="主键属性名" />
      <result column="列名1" jdbcType="列类型1" property="属性名1" />
      <result column="列名2" jdbcType="列类型2" property="属性名2" />
    </resultMap>
    
    <sql id="Example_Where_Clause">
      <where>
        <foreach collection="oredCriteria" item="criteria" separator="or">
          <if test="criteria.valid">
            <trim prefix="(" prefixOverrides="and" suffix=")">
              <!-- 动态条件 -->
            </trim>
          </if>
        </foreach>
      </where>
    </sql>
    
    <sql id="Base_Column_List">
      列名1, 列名2, 列名3
    </sql>
    
    <select id="selectByExample" parameterType="com.howbuy.tms.high.orders.dao.po.TableNamePoExample" resultMap="BaseResultMap">
      select
      <if test="distinct">
        distinct
      </if>
      'true' as QUERYID,
      <include refid="Base_Column_List" />
      from 表名
      <if test="_parameter != null">
        <include refid="Example_Where_Clause" />
      </if>
      <if test="orderByClause != null">
        order by ${orderByClause}
      </if>
    </select>
    
    <select id="selectByPrimaryKey" parameterType="主键类型" resultMap="BaseResultMap">
      select 
      <include refid="Base_Column_List" />
      from 表名
      where 主键列名 = #{主键属性名,jdbcType=主键JDBC类型}
    </select>
    
    <delete id="deleteByPrimaryKey" parameterType="主键类型">
      delete from 表名
      where 主键列名 = #{主键属性名,jdbcType=主键JDBC类型}
    </delete>
    
    <insert id="insert" parameterType="com.howbuy.tms.high.orders.dao.po.TableNamePo">
      insert into 表名 (列名1, 列名2, 列名3)
      values (#{属性名1,jdbcType=列类型1}, #{属性名2,jdbcType=列类型2}, #{属性名3,jdbcType=列类型3})
    </insert>
    
    <insert id="insertSelective" parameterType="com.howbuy.tms.high.orders.dao.po.TableNamePo">
      insert into 表名
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="属性名1 != null">
          列名1,
        </if>
        <if test="属性名2 != null">
          列名2,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
        <if test="属性名1 != null">
          #{属性名1,jdbcType=列类型1},
        </if>
        <if test="属性名2 != null">
          #{属性名2,jdbcType=列类型2},
        </if>
      </trim>
    </insert>
    
    <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.tms.high.orders.dao.po.TableNamePo">
      update 表名
      <set>
        <if test="属性名1 != null">
          列名1 = #{属性名1,jdbcType=列类型1},
        </if>
        <if test="属性名2 != null">
          列名2 = #{属性名2,jdbcType=列类型2},
        </if>
      </set>
      where 主键列名 = #{主键属性名,jdbcType=主键JDBC类型}
    </update>
    
    <update id="updateByPrimaryKey" parameterType="com.howbuy.tms.high.orders.dao.po.TableNamePo">
      update 表名
      set 列名1 = #{属性名1,jdbcType=列类型1},
          列名2 = #{属性名2,jdbcType=列类型2}
      where 主键列名 = #{主键属性名,jdbcType=主键JDBC类型}
    </update>
  </mapper>
  ```

#==================== 自定义Mapper接口规范 ====================

# 自定义Mapper接口结构
custom_mapper_interface_structure:
  ```java
  package com.howbuy.tms.high.orders.dao.mapper.customize;

  import com.howbuy.tms.high.orders.dao.po.TableNamePo;
  import org.apache.ibatis.annotations.Param;
  import java.util.List;
  import java.util.Map;

  /**
   * @description: 表名自定义Mapper
   * @author: hongdong.xie
   * @date: 2025/3/20 14:30
   * @since JDK 1.8
   */
  public interface TableNamePoMapper {
      
      /**
       * @description: 根据业务条件查询记录
       * @param paramName1 参数1说明
       * @param paramName2 参数2说明
       * @return List<TableNamePo> 查询结果列表
       * @author: hongdong.xie
       * @date: 2025/3/20 14:30
       * @since JDK 1.8
       */
      List<TableNamePo> selectByBusinessCondition(@Param("paramName1") String paramName1, 
                                                   @Param("paramName2") String paramName2);
      
      /**
       * @description: 根据业务条件更新记录
       * @param record 更新对象
       * @param condition 更新条件
       * @return int 更新记录数
       * @author: hongdong.xie
       * @date: 2025/3/20 14:30
       * @since JDK 1.8
       */
      int updateByBusinessCondition(@Param("record") TableNamePo record, 
                                    @Param("condition") Map<String, Object> condition);
      
      /**
       * @description: 根据业务条件删除记录
       * @param condition 删除条件
       * @return int 删除记录数
       * @author: hongdong.xie
       * @date: 2025/3/20 14:30
       * @since JDK 1.8
       */
      int deleteByBusinessCondition(@Param("condition") Map<String, Object> condition);
  }
  ```

#==================== 自定义Mapper XML规范 ====================

# 自定义Mapper XML结构
custom_mapper_xml_structure:
  ```xml
  <?xml version="1.0" encoding="UTF-8"?>
  <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
  <mapper namespace="com.howbuy.tms.high.orders.dao.mapper.customize.TableNamePoMapper">
    
    <resultMap id="BaseResultMap" type="com.howbuy.tms.high.orders.dao.po.TableNamePo"
               extends="com.howbuy.tms.high.orders.dao.mapper.TableNamePoAutoMapper.BaseResultMap">
    </resultMap>
    
    <!-- 自定义查询 -->
    <select id="selectByBusinessCondition" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="com.howbuy.tms.high.orders.dao.mapper.TableNamePoAutoMapper.Base_Column_List"/>
        from 表名
        <where>
            <if test="paramName1 != null and paramName1 != ''">
                and 列名1 = #{paramName1,jdbcType=VARCHAR}
            </if>
            <if test="paramName2 != null and paramName2 != ''">
                and 列名2 = #{paramName2,jdbcType=VARCHAR}
            </if>
        </where>
        order by 排序字段
    </select>
    
    <!-- 自定义更新 -->
    <update id="updateByBusinessCondition" parameterType="map">
        update 表名
        <set>
            <if test="record.字段1 != null">
                列名1 = #{record.字段1,jdbcType=VARCHAR},
            </if>
            <if test="record.字段2 != null">
                列名2 = #{record.字段2,jdbcType=DECIMAL},
            </if>
        </set>
        <where>
            <if test="condition.conditionField1 != null">
                and 条件列名1 = #{condition.conditionField1,jdbcType=VARCHAR}
            </if>
            <if test="condition.conditionField2 != null">
                and 条件列名2 = #{condition.conditionField2,jdbcType=VARCHAR}
            </if>
        </where>
    </update>
    
    <!-- 自定义删除 -->
    <delete id="deleteByBusinessCondition" parameterType="map">
        delete from 表名
        <where>
            <if test="condition.field1 != null">
                and 列名1 = #{condition.field1,jdbcType=VARCHAR}
            </if>
            <if test="condition.field2 != null">
                and 列名2 = #{condition.field2,jdbcType=VARCHAR}
            </if>
        </where>
    </delete>
    
    <!-- 集合参数处理示例 -->
    <select id="selectByIdList" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="com.howbuy.tms.high.orders.dao.mapper.TableNamePoAutoMapper.Base_Column_List"/>
        from 表名
        <where>
            主键列名 in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>
    </select>
  </mapper>
  ```

#==================== JDBC类型映射规范 ====================

# JDBC类型映射
jdbc_type_mapping:
  - BIGINT -> BIGINT
  - INT -> INTEGER
  - DECIMAL -> DECIMAL
  - VARCHAR -> VARCHAR
  - CHAR -> CHAR
  - TIMESTAMP -> TIMESTAMP
  - DATE -> DATE
  - TEXT -> LONGVARCHAR
  - LONGTEXT -> LONGVARCHAR

#==================== MyBatis配置规范 ====================

# MyBatis全局配置
mybatis_config_rules:
  - 配置文件位置：high-order-center-dao/src/main/resources/mybatis-config.xml
  - 关键配置项：
    - mapUnderscoreToCamelCase: true (下划线转驼峰)
    - lazyLoadingEnabled: true (延迟加载)
    - aggressiveLazyLoading: false (按需加载)
    - defaultStatementTimeout: 300 (超时时间300秒)
    - jdbcTypeForNull: OTHER (空值处理)

#==================== 代码示例 ====================

# PO类完整示例
po_class_example:
  ```java
  package com.howbuy.tms.high.orders.dao.po;

  import java.io.Serializable;
  import java.math.BigDecimal;
  import java.util.Date;

  /**
   * @description: 子账本表
   * @author: hongdong.xie
   * @date: 2025/3/20 14:30
   * @since JDK 1.8
   */
  public class SubCustBooksPo implements Serializable {
      
      private static final long serialVersionUID = 1L;

      /**
       * 外部份额明细号
       */
      private String extVolDtlNo;

      /**
       * 账本明细号
       */
      private String booksDtlNo;

      /**
       * 交易账号
       */
      private String txAcctNo;

      /**
       * 基金代码
       */
      private String fundCode;

      /**
       * 协议号
       */
      private String protocolNo;

      /**
       * 对方账号
       */
      private String cpAcctNo;

      /**
       * 确认日期
       */
      private String ackDt;

      /**
       * 登记日期
       */
      private String regDt;

      /**
       * 开放赎回日期
       */
      private String openRedeDt;

      /**
       * 基金份额类别
       */
      private String fundShareClass;

      /**
       * 可用份额
       */
      private BigDecimal availVol;

      /**
       * 创建时间
       */
      private Date createDtm;

      /**
       * 更新时间
       */
      private Date updateDtm;

      // getter和setter方法
      public String getExtVolDtlNo() {
          return extVolDtlNo;
      }

      public void setExtVolDtlNo(String extVolDtlNo) {
          this.extVolDtlNo = extVolDtlNo == null ? null : extVolDtlNo.trim();
      }

      public String getBooksDtlNo() {
          return booksDtlNo;
      }

      public void setBooksDtlNo(String booksDtlNo) {
          this.booksDtlNo = booksDtlNo == null ? null : booksDtlNo.trim();
      }

      // 其他getter和setter方法...
  }
  ```

# 自定义Mapper接口示例
custom_mapper_interface_example:
  ```java
  package com.howbuy.tms.high.orders.dao.mapper.customize;

  import com.howbuy.tms.high.orders.dao.po.SubCustBooksPo;
  import org.apache.ibatis.annotations.Param;
  import java.util.List;
  import java.util.Map;

  /**
   * @description: 子账本表自定义Mapper
   * @author: hongdong.xie
   * @date: 2025/3/20 14:30
   * @since JDK 1.8
   */
  public interface SubCustBooksPoMapper {
      
      /**
       * @description: 根据开放赎回日期查询子账本
       * @param txAcctNo 交易账号
       * @param fundCode 基金代码
       * @param openRedeDt 开放赎回日期
       * @return List<SubCustBooksPo> 子账本列表
       * @author: hongdong.xie
       * @date: 2025/3/20 14:30
       * @since JDK 1.8
       */
      List<SubCustBooksPo> selectSubCustBooksByOpenRedeDt(@Param("txAcctNo") String txAcctNo,
                                                          @Param("fundCode") String fundCode,
                                                          @Param("openRedeDt") String openRedeDt);
      
      /**
       * @description: 更新子账本可用份额
       * @param extVolDtlNo 外部份额明细号
       * @param availVol 可用份额
       * @return int 更新记录数
       * @author: hongdong.xie
       * @date: 2025/3/20 14:30
       * @since JDK 1.8
       */
      int updateAvailVolByExtVolDtlNo(@Param("extVolDtlNo") String extVolDtlNo,
                                      @Param("availVol") BigDecimal availVol);
  }
  ```

# 自定义Mapper XML示例
custom_mapper_xml_example:
  ```xml
  <?xml version="1.0" encoding="UTF-8"?>
  <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
  <mapper namespace="com.howbuy.tms.high.orders.dao.mapper.customize.SubCustBooksPoMapper">
    
    <resultMap id="BaseResultMap" type="com.howbuy.tms.high.orders.dao.po.SubCustBooksPo"
               extends="com.howbuy.tms.high.orders.dao.mapper.SubCustBooksPoAutoMapper.BaseResultMap">
    </resultMap>
    
    <!-- 查询子账本表 (开放赎回日期维度) -->
    <select id="selectSubCustBooksByOpenRedeDt" parameterType="map" resultMap="BaseResultMap">
        select *
        from (select t.ext_vol_dtl_no,
                     t.books_dtl_no,
                     t.tx_acct_no,
                     t.fund_code,
                     t.protocol_no,
                     t.cp_acct_no,
                     t.ack_dt,
                     t.reg_dt,
                     t.open_rede_dt,
                     t.fund_share_class,
                     sum(t.avail_vol) as avail_vol,
                     t.create_dtm,
                     t.update_dtm
              from SUB_CUST_BOOKS t
              where t.TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
                and t.FUND_CODE = #{fundCode,jdbcType=VARCHAR}
                and t.OPEN_REDE_DT = #{openRedeDt,jdbcType=VARCHAR}
                and t.AVAIL_VOL > 0
              group by t.ext_vol_dtl_no, t.books_dtl_no, t.tx_acct_no, t.fund_code,
                       t.protocol_no, t.cp_acct_no, t.ack_dt, t.reg_dt, t.open_rede_dt,
                       t.fund_share_class, t.create_dtm, t.update_dtm
              order by t.reg_dt asc) temp
    </select>
    
    <!-- 更新可用份额 -->
    <update id="updateAvailVolByExtVolDtlNo" parameterType="map">
        update SUB_CUST_BOOKS
        set AVAIL_VOL = #{availVol,jdbcType=DECIMAL},
            UPDATE_DTM = now()
        where EXT_VOL_DTL_NO = #{extVolDtlNo,jdbcType=VARCHAR}
    </update>
  </mapper>
  ```

#==================== 代码生成步骤 ====================

# 生成PO类步骤
generate_po_steps:
  1. 根据数据库表结构，生成对应的PO类
  2. 确保类名符合命名规范：表名转驼峰命名法 + Po后缀
  3. 确保字段名符合命名规范：数据库字段名转小驼峰命名法
  4. 确保字段类型符合类型映射规范
  5. 添加适当的类注释和字段注释
  6. 实现Serializable接口，定义serialVersionUID

# 生成基础Mapper接口步骤
generate_base_mapper_steps:
  1. 根据PO类，生成对应的基础Mapper接口
  2. 确保接口名符合命名规范：表名转驼峰命名法 + PoAutoMapper后缀
  3. 实现基本的CRUD方法
  4. 添加适当的类注释

# 生成基础Mapper XML步骤
generate_base_mapper_xml_steps:
  1. 根据PO类和基础Mapper接口，生成对应的基础Mapper XML
  2. 确保XML文件名符合命名规范：表名转驼峰命名法 + PoAutoMapper.xml
  3. 实现基本的CRUD SQL语句
  4. 确保resultMap和SQL语句符合规范

# 生成自定义Mapper接口步骤
generate_custom_mapper_steps:
  1. 根据业务需求，生成对应的自定义Mapper接口
  2. 确保接口名符合命名规范：表名转驼峰命名法 + PoMapper后缀
  3. 实现自定义的查询、更新、删除方法
  4. 添加适当的类注释和方法注释

# 生成自定义Mapper XML步骤
generate_custom_mapper_xml_steps:
  1. 根据自定义Mapper接口，生成对应的自定义Mapper XML
  2. 确保XML文件名符合命名规范：表名转驼峰命名法 + PoMapper.xml
  3. 实现自定义的SQL语句
  4. 确保SQL语句符合规范
  5. 继承基础Mapper的resultMap

#==================== 注意事项 ====================

# 开发注意事项
development_notes:
  1. 确保生成的代码符合项目规范
  2. 确保字段名和类型映射正确
  3. 确保注释完整、准确
  4. 确保SQL语句正确、高效
  5. 自定义Mapper接口和XML文件应根据实际业务需求进行定制
  6. 避免生成冗余或重复的代码
  7. 确保生成的代码可以正常编译和运行
  8. 自定义Mapper XML必须继承基础Mapper的resultMap
  9. 使用@Param注解明确参数名称
  10. 复杂查询使用Map作为参数类型

# 性能优化建议
performance_optimization:
  1. 合理使用索引，避免全表扫描
  2. 使用分页查询处理大数据量
  3. 避免在循环中执行数据库操作
  4. 使用批量操作提高性能
  5. 合理使用缓存机制
  6. 避免使用select *，明确指定需要的字段
  7. 使用连接查询代替多次单表查询
  8. 合理设置超时时间

# 安全注意事项
security_notes:
  1. 使用参数化查询防止SQL注入
  2. 避免在日志中记录敏感数据
  3. 对输入参数进行严
</augment_code_snippet>

赎回费需求:
	赎回费主要就是基金赎回的订单,有些需要收赎回手续费,这些需要提前预警出来,不同的基金,根据持有时间需要收取的费用逻辑不一样,下面就是订单赎回计算赎回费的规则,总体而言有锁定期的基金,就会有份额明细,赎回单需要将赎回份额拆分到每个份额明细维度,计算持有天数,获取手续费率计算手续费;没有锁定期的,就只能根据买入订单来反推持有天数,计算手续费,以下是具体的逻辑;

1.如果该基金是循环锁定基金，且客户该基金的份额明细中只有一个资金账号，或者如果该基金没有05明细文件，且客户该基金的份额明细中只有一个资金账号，则使用份额不为0的份额明细数据。
	a.对于预约赎回的产品，将锁定结束日期<=赎回订单的开放日的份额明细照登记日期从小到大排序，对于每日开放赎回的产品，将锁定结束日期<=当前工作日的份额明细照登记日期从小到大排序，将该客户的所有未确认的申请成功的赎回订单按照申请时间从小到大排序，逐笔将赎回申请份额分摊到份额明细上。
	b.根据被分摊到的明细的登记日期和赎回订单上报日期，计算差值得到本期赎回的每笔明细的持有天数。
	c.根据被分摊到的明细的登记日期和和下一个赎回日，计算得到下一期赎回的每笔明细的持有天数。
		预约赎回的基金：下一个赎回日=下一个开放日历的开放开始日
		不支持预约的：下一个赎回日=当前工作日+7个工作日
	d.根据本期每笔明细的持有天数、和下一期每笔明细的持有天数查询落在的业务类型=赎回的费率区间。
	e.如果订单的本期每笔明细的持有天数落在的费率区间和下一期每笔明细的持有天数落在的费率区间不同，如果在【赎回费预估告警订单】中不存在或者告警的关键要素不一致，更新并告警。
		关键要素：本期每笔明细的持有天数、本期赎回的费率区间及份额、下一期每笔明细的持有天数、下一期赎回的费率区间及份额
		告警内容：基金代码JJDM，基金简称JJJC，客户号KKH1、KKH2、KKH3，数据来源：份额明细，客户份额类型：单卡，本期赎回和下一期赎回的费率可能不同，循环锁定产品下一个开放日不一定可赎回。
 2.如果该基金是循环锁定基金，且客户该基金的份额明细中有多个资金账号，或者如果该基金没有05明细文件，且客户该基金的份额明细中有多个资金账号，使用以下数据进行判断
	a.预约赎回的基金，使用锁定结束日<=赎回订单的上报日的、登记日期最大的份额明细，包括份额为0的明细，
	b.对于每日开放赎回的基金，使用锁定结束日<=当前工作日的、登记日期最大的份额明细，包括份额为0的明细。
	c.根据份额明细的登记日期和赎回订单上报日期，计算差值得到本期赎回的每笔明细的持有天数。
	d.根据份额明细的登记日期和和下一个赎回日，计算得到下一期赎回的每笔明细的持有天数。
		预约赎回的基金：下一个赎回日=下一个开放日历的开放开始日
		不支持预约的：下一个赎回日=当前工作日+7个工作日
	e.根据本期每笔明细的持有天数、和下一期每笔明细的持有天数查询落在的业务类型=赎回的费率区间。
	f.如果订单的本期每笔明细的持有天数落在的费率区间和下一期每笔明细的持有天数落在的费率区间不同，如果在【赎回费预估告警订单】中不存在或者告警的关键要素不一致，更新并告警。
		关键要素：本期每笔明细的持有天数、本期赎回的费率区间及份额、下一期每笔明细的持有天数、下一期赎回的费率区间及份额
		告警内容：基金代码JJDM，基金简称JJJC，客户号KKH1、KKH2、KKH3，数据来源：份额明细，客户份额类型：多卡，本期赎回和下一期赎回的费率可能不同。
 3.如果该基金没有05明细文件，也没有份额明细，则使用订单。
	a.将客户所有确认成功或者部分成功的申购、认购、红利再投、强增、非交易过户转入、转托管转入、份额迁移转入的订单按照确认日期从大到小排序，将客户当前的确认持仓逐笔分摊到订单上，即确认每笔增加份额的订单上当前仍持有的份额。
	b.将仍持有份额的订单，按照确认日期从小到大排序，将该客户的所有未确认的申请成功的赎回订单按照申请时间从小到大排序，逐笔将赎回申请份额分摊到订单上。
	c.根据被分摊到的订单的确认日期和赎回订单上报日期，计算差值得到本期赎回的每笔明细的持有天数。
	d.根据被分摊到的订单的确认日期和和下一个赎回开放日历的开放日，如果开放日为区间取最近的一个开放日，计算得到下一期赎回的每笔明细的持有天数。
	e.根据本期每笔明细的持有天数、和下一期每笔明细的持有天数查询落在的业务类型=赎回的费率区间。
	f.如果订单的本期每笔明细的持有天数落在的费率区间和下一期每笔明细的持有天数落在的费率区间不同，如果在【赎回费预估告警订单】中不存在或者告警的关键要素不一致，更新并告警。
		关键要素：本期每笔明细的持有天数、本期赎回的费率区间及份额、下一期每笔明细的持有天数、下一期赎回的费率区间及份额
		告警内容：基金代码JJDM，基金简称JJJC，客户号KKH1、KKH2、KKH3，数据来源：订单，本期赎回和下一期赎回的费率可能不同。


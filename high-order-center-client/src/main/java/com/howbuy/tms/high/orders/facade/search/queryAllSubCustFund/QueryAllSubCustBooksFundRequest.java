package com.howbuy.tms.high.orders.facade.search.queryAllSubCustFund;

import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import lombok.Data;

/**
 * @Description:查询持仓子账单明细中,有持仓的产品列表
 * @Author: yun.lu
 * Date: 2024/10/12 16:56
 */
@Data
public class QueryAllSubCustBooksFundRequest extends OrderSearchBaseRequest {

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryAllSubCustFund.QueryAllSubCustBooksFundFacade.execute()
     * @apiVersion 1.0.0
     * @apiGroup QueryAllSubCustBooksFundService
     * @apiName execute
     * @apiDescription 查询持仓子账单明细中, 有持仓的产品列表
     * @apiParamExample 请求参数示例
     * hbOneNo=x&pageSize=350&disCode=8o&txChannel=QiCciuK9j&appTm=KmwvyE&subOutletCode=BcCPA&pageNo=6296&operIp=hBpH5clx&txAcctNo=6xJPsv9K&appDt=EJ6htf4J&dataTrack=T2H4vZfx&txCode=O9emDD&outletCode=m
     * @apiSuccess (响应结果) {Array} fundCodeList 产品列表
     * @apiSuccess (响应结果) {String} returnCode 返回码
     * @apiSuccess (响应结果) {String} description 描述
     * @apiSuccess (响应结果) {Number} totalCount 总记录数
     * @apiSuccess (响应结果) {Number} totalPage 总页数
     * @apiSuccess (响应结果) {Number} pageNo 当前页
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"qP5UhTg","fundCodeList":["uim83cw5P"],"totalPage":1105,"pageNo":1750,"description":"xpKPZqt0","totalCount":6866}
     */
    public QueryAllSubCustBooksFundRequest(){
        setTxCode(TxCodes.QUERY_ALL_SUB_CUSTBOOKS_FUND_INFO);
        this.setDisCode(DisCodeEnum.HM.getCode());
    }
}

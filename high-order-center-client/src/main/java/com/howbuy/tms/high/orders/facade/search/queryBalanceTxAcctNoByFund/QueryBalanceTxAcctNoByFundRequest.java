package com.howbuy.tms.high.orders.facade.search.queryBalanceTxAcctNoByFund;

import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import lombok.Data;

/**
 * @Description:根据产品查询持仓用户交易账号入参
 * @Author: yun.lu
 * Date: 2024/10/22 14:20
 */
@Data
public class QueryBalanceTxAcctNoByFundRequest extends OrderSearchBaseRequest {
    /**
     * 产品编码
     */
    private String fundCode;
    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryBalanceTxAcctNoByFund.QueryBalanceTxAcctNoByFundFacade.execute(QueryBalanceTxAcctNoByFundRequest request)
     * @apiVersion 1.0.0
     * @apiGroup QueryBalanceTxAcctNoByFundService
     * @apiName execute
     * @apiDescription 根据产品查询持仓用户交易账号
     * @apiParam (请求参数) {String} fundCode 产品编码
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode 分销机构代码
     * @apiParam (请求参数) {String} outletCode 网点代码
     * @apiParam (请求参数) {String} appDt 申请日期
     * @apiParam (请求参数) {String} appTm 申请时间
     * @apiParam (请求参数) {Number} pageNo 页码
     * @apiParam (请求参数) {Number} pageSize 每页记录数
     * @apiParam (请求参数) {String} operIp 交易IP
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
     * @apiParam (请求参数) {String} dataTrack 数据跟踪
     * @apiParam (请求参数) {String} subOutletCode 子网点代码
     * @apiParamExample 请求参数示例
     * hbOneNo=599CN&pageSize=7234&disCode=MJ8r&txChannel=QW&appTm=Eda&fundCode=psBo1qp6&subOutletCode=GhgN8Gku&pageNo=8620&operIp=LD0mA7F&txAcctNo=5PXF&appDt=B5oL4&dataTrack=5&txCode=zBX7H6Z&outletCode=8v0eTUcrk
     * @apiSuccess (响应结果) {Array} txAcctNoList 交易账号集合
     * @apiSuccess (响应结果) {Array} hbOneNoList 一账通列表
     * @apiSuccess (响应结果) {String} returnCode 返回码
     * @apiSuccess (响应结果) {String} description 描述
     * @apiSuccess (响应结果) {Number} totalCount 总记录数
     * @apiSuccess (响应结果) {Number} totalPage 总页数
     * @apiSuccess (响应结果) {Number} pageNo 当前页
     * @apiSuccess (响应结果) {String} txId txId
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"2yY115noiw","txAcctNoList":["uvZq9"],"totalPage":7053,"pageNo":356,"description":"b","txId":"HRxJPw","totalCount":7461,"hbOneNoList":["pjV"]}
     */
   public QueryBalanceTxAcctNoByFundRequest(){
       setTxCode(TxCodes.QUERY_BALANCE_TX_ACCT_NO_BY_FUND);
       setDisCode(DisCodeEnum.HM.getCode());
    }

}

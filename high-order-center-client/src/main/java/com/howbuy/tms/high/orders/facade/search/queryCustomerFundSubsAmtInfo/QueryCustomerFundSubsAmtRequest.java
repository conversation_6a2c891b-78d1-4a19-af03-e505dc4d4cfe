package com.howbuy.tms.high.orders.facade.search.queryCustomerFundSubsAmtInfo;

import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import lombok.Data;

/**
 * @Description:查询用户产品认缴信息入参
 * @Author: yun.lu
 * Date: 2024/7/15 18:51
 */
@Data
public class QueryCustomerFundSubsAmtRequest extends OrderSearchBaseRequest {
    /**
     * 产品编码
     */
    private String fundCode;

    public QueryCustomerFundSubsAmtRequest() {
        super.setTxCode(TxCodes.QUERY_CUSTOMER_FUND_SUBS_AMT);
    }
}

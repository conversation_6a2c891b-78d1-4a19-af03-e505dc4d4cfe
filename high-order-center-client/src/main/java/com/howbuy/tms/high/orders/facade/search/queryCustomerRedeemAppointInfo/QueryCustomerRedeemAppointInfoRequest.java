package com.howbuy.tms.high.orders.facade.search.queryCustomerRedeemAppointInfo;

import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import lombok.Data;

import java.util.List;

/**
 * @Description:查询用户赎回日历信息入参
 * @Author: yun.lu
 * Date: 2024/10/9 16:32
 */
@Data
public class QueryCustomerRedeemAppointInfoRequest extends OrderSearchBaseRequest {
    /**
     * 产品编码
     */
    private List<String> fundCodeList;

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryCustomerRedeemAppointInfo.QueryCustomerRedeemAppointInfoFacade.execute(QueryCustomerRedeemAppointInfoRequest request)
     * @apiVersion 1.0.0
     * @apiGroup QueryCustomerRedeemAppointInfoService
     * @apiName execute
     * @apiDescription 查询用户赎回日历信息
     * @apiParam (请求参数) {Array} fundCodeList 产品编码
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode
     * @apiParam (请求参数) {String} outletCode
     * @apiParam (请求参数) {String} appDt
     * @apiParam (请求参数) {String} appTm
     * @apiParam (请求参数) {Number} pageNo
     * @apiParam (请求参数) {Number} pageSize
     * @apiParam (请求参数) {String} operIp
     * @apiParam (请求参数) {String} txCode
     * @apiParam (请求参数) {String} txChannel
     * @apiParam (请求参数) {String} dataTrack
     * @apiParam (请求参数) {String} subOutletCode
     * @apiParamExample 请求参数示例
     * fundCodeList=bm1&hbOneNo=OSZHBZxsZ&pageSize=413&disCode=Y2gh3yL49&txChannel=NATEzC4U&appTm=uzmALeFVXX&subOutletCode=16KThFkC&pageNo=6688&operIp=lgNkcotEk&txAcctNo=L&appDt=J4sC&dataTrack=wInUbo8&txCode=w&outletCode=Zj3
     * @apiSuccess (响应结果) {Array} customerRedeemAppointInfoList 用户赎回日历信息列表
     * @apiSuccess (响应结果) {String} customerRedeemAppointInfoList.txAcctNo 交易账号
     * @apiSuccess (响应结果) {String} customerRedeemAppointInfoList.fundCode 产品编码
     * @apiSuccess (响应结果) {String} customerRedeemAppointInfoList.fundName 产品简称
     * @apiSuccess (响应结果) {Number} customerRedeemAppointInfoList.totalVol 总份额
     * @apiSuccess (响应结果) {Number} customerRedeemAppointInfoList.availVol 可用份额
     * @apiSuccess (响应结果) {String} customerRedeemAppointInfoList.openRedeemDt 份额锁定期,yyyyMMdd
     * @apiSuccess (响应结果) {String} customerRedeemAppointInfoList.lockStatus 锁定状态（1、锁定中； 2、锁定可赎回； 3、锁定已过,4,不可赎回）
     * @apiSuccess (响应结果) {String} customerRedeemAppointInfoList.redeemStartDt 可赎回开始日期,yyyyMMdd
     * @apiSuccess (响应结果) {String} customerRedeemAppointInfoList.redeemEndDt 可赎回结束日期,yyyyMMdd
     * @apiSuccess (响应结果) {String} customerRedeemAppointInfoList.openStartDt 开放开始日期,yyyyMMdd
     * @apiSuccess (响应结果) {String} customerRedeemAppointInfoList.openEndDt 开放结束日期,yyyyMMdd
     * @apiSuccess (响应结果) {String} returnCode
     * @apiSuccess (响应结果) {String} description
     * @apiSuccess (响应结果) {Number} totalCount
     * @apiSuccess (响应结果) {Number} totalPage
     * @apiSuccess (响应结果) {Number} pageNo
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"1AX","totalPage":7641,"pageNo":32,"description":"H","customerRedeemAppointInfoList":[{"lockStatus":"b3ov50K","redeemStartDt":"HoUbwODU9z","fundCode":"9Ub","availVol":6524.525548087702,"redeemEndDt":"uv7aa41","openStartDt":"0DxhkPFYvP","txAcctNo":"v7wfpJ","totalVol":9892.542626317869,"openRedeemDt":"pcty","openEndDt":"oA2","fundName":"YvvixQC"}],"totalCount":9450}
     */
    public QueryCustomerRedeemAppointInfoRequest() {
        setTxCode(TxCodes.QUERY_CUSTOMER_REDEEM_APPOINT_INFO);
        this.setDisCode(DisCodeEnum.HM.getCode());
    }
}

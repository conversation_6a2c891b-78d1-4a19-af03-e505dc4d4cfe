package com.howbuy.tms.high.orders.facade.search.queryFirstBuyFlagFacade;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.validate.MyValidation;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import lombok.Data;

/**
 * @Description:是否首次购买请求
 * @Author: yun.lu
 * Date: 2024/5/29 15:29
 */
@Data
public class QueryFirstBuyFlagRequest extends OrderSearchBaseRequest {
    /**
     * 产品编码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "产品编码", isRequired = true)
    private String fundCode;

    private String queryHzBuyOrderInfoRequest;

    public QueryFirstBuyFlagRequest() {
        setTxCode(TxCodes.QUERY_HZ_BUY_ORDER_INFO);
        setDisCode(DisCodeEnum.HZ.getCode());
    }
}

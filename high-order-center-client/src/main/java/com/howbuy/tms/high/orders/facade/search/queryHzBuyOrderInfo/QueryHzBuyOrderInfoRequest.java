package com.howbuy.tms.high.orders.facade.search.queryHzBuyOrderInfo;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.validate.MyValidation;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import lombok.Data;

import java.util.Date;

/**
 * @Description:好臻下单所需订单信息查询入参
 * @Author: yun.lu
 * Date: 2023/11/21 19:00
 */
@Data
public class QueryHzBuyOrderInfoRequest extends OrderSearchBaseRequest {
    /**
     * 产品编码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "产品编码", isRequired = true)
    private String fundCode;
    /**
     * 指定查询日期
     */
    private Date queryDate;

    private String queryHzBuyOrderInfoRequest;

    public QueryHzBuyOrderInfoRequest() {
        setTxCode(TxCodes.QUERY_HZ_BUY_ORDER_INFO);
        setDisCode(DisCodeEnum.HZ.getCode());
    }
}

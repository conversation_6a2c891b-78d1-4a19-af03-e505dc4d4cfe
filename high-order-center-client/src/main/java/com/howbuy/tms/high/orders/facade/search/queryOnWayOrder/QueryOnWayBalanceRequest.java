package com.howbuy.tms.high.orders.facade.search.queryOnWayOrder;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.validate.MyValidation;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import lombok.Data;

/**
 * @Description:在途订单查询请求
 * @Author: yun.lu
 * Date: 2023/11/27 15:00
 */
@Data
public class QueryOnWayBalanceRequest extends OrderSearchBaseRequest {
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "产品编码", isRequired = true)
    private String fundCode;


    public QueryOnWayBalanceRequest() {
        setTxCode(TxCodes.QUERY_ACCT_ON_WAY_ORDER);
        setDisCode(DisCodeEnum.HM.getCode());
    }
}

/**
 * Copyright (c) 2017, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */


package com.howbuy.tms.high.orders.facade.search.queryacctbalance;

import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import com.howbuy.tms.high.orders.facade.enums.TxCodes;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description:(查询客户持仓接口请求参数)
 * @reason:
 * @date 2018年6月21日 下午4:55:44
 * @since JDK 1.7
 */
@Data
public class QueryAcctBalanceRequest extends OrderSearchBaseRequest {

    private static final long serialVersionUID = 2435434454295240441L;

    /**
     * @api {dubbo} com.howbuy.tms.high.orders.facade.search.queryacctbalanceNew.QueryAcctBalanceNewFacade.execute()
     * @apiVersion 1.0.0
     * @apiGroup QueryAcctBalanceFacadeNewService
     * @apiName execute
     * @apiDescription 查询用户持仓, 海外的转包海外接口, 其他的查询之前的接口
     * @apiParam (请求参数) {String} hkSaleFlag 好买香港代销标识
     * @apiParam (请求参数) {String} productCode 产品代码
     * @apiParam (请求参数) {String} productType 产品类型
     * @apiParam (请求参数) {String} productSubType 产品子类型
     * @apiParam (请求参数) {String} protocolType 协议类型，4-高端产品协议
     * @apiParam (请求参数) {Array} disCodeList 分销机构代码列表-股权直销改造
     * @apiParam (请求参数) {String} callType 1-新资产中心      2-老资产中心
     * @apiParam (请求参数) {String} balanceStatus 兼容老逻辑,注意,字段不传也是查持仓的      持仓状态,0:不持仓,1:持仓,,2:全部      默认查持仓的
     * @apiParam (请求参数) {String} notFilterHkFund 不过滤香港产品,1:是,0:否
     * @apiParam (请求参数) {String} notFilterHzFund 不过滤好臻产品,1:是,0:否
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode 分销机构代码
     * @apiParam (请求参数) {String} outletCode 网点代码
     * @apiParam (请求参数) {String} appDt 申请日期
     * @apiParam (请求参数) {String} appTm 申请时间
     * @apiParam (请求参数) {Number} pageNo 页码
     * @apiParam (请求参数) {Number} pageSize 每页记录数
     * @apiParam (请求参数) {String} operIp 交易IP
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
     * @apiParam (请求参数) {String} dataTrack 数据跟踪
     * @apiParam (请求参数) {String} subOutletCode 子网点代码
     * @apiParamExample 请求参数示例
     * notFilterHzFund=9YguyFH&hbOneNo=BIZOKAn&balanceStatus=fSsGC&pageSize=5839&protocolType=yhaBZhdVMl&disCode=TFk4vJCX&txChannel=a7kpZ5cBY&productSubType=n&callType=MpnEbmpNpn&appTm=1&productCode=Jc6Yj&disCodeList=gd1WkE7&subOutletCode=3F58TMzex&pageNo=1045&operIp=JDiibYnRr&txAcctNo=ZWdGQghpEU&appDt=GOq&dataTrack=EqjUA&hkSaleFlag=g3QATkd&notFilterHkFund=AuYd0hF&txCode=X8JAex&productType=KqatIMGX&outletCode=RqrJ
     * @apiSuccess (响应结果) {Array} disCodeList 分销机构号列表-股权直销改造
     * @apiSuccess (响应结果) {String} txAcctNo 交易账号
     * @apiSuccess (响应结果) {Number} totalMarketValue 总市值
     * @apiSuccess (响应结果) {Number} totalUnconfirmedAmt 在途总金额
     * @apiSuccess (响应结果) {Number} totalUnconfirmedNum 待确认笔数
     * @apiSuccess (响应结果) {Number} redeemUnconfirmedNum 赎回待确认笔数
     * @apiSuccess (响应结果) {Number} totalCurrentAsset 当前总收益
     * @apiSuccess (响应结果) {String} totalIncomCalStat 总收益计算状态: 0-计算中;1-计算成功
     * @apiSuccess (响应结果) {Number} totalCashCollection 总回款
     * @apiSuccess (响应结果) {String} hasHZProduct 是否持有好臻产品 0:没有,1:有
     * @apiSuccess (响应结果) {String} hasHKProduct 是否持有好买香港产品  0:没有,1:有
     * @apiSuccess (响应结果) {Array} balanceList 持仓明细列表
     * @apiSuccess (响应结果) {String} balanceList.disCode 分销代码
     * @apiSuccess (响应结果) {Array} balanceList.disCodeList 分销代码列表
     * @apiSuccess (响应结果) {String} balanceList.productCode 产品代码
     * @apiSuccess (响应结果) {String} balanceList.subProductCode 子产品代码
     * @apiSuccess (响应结果) {String} balanceList.productName 产品名称
     * @apiSuccess (响应结果) {String} balanceList.productType 产品类型
     * @apiSuccess (响应结果) {String} balanceList.productSubType 产品子类型(好买产品线)
     * @apiSuccess (响应结果) {Number} balanceList.balanceVol 总份额
     * @apiSuccess (响应结果) {Number} balanceList.unconfirmedVol 待确认份额
     * @apiSuccess (响应结果) {Number} balanceList.unconfirmedAmt 待确认金额
     * @apiSuccess (响应结果) {String} balanceList.currency 币种
     * @apiSuccess (响应结果) {Number} balanceList.nav 净值
     * @apiSuccess (响应结果) {String} balanceList.navDt 净值日期
     * @apiSuccess (响应结果) {String} balanceList.navDivFlag 净值分红标识 0-否，1-是
     * @apiSuccess (响应结果) {Number} balanceList.marketValue 市值
     * @apiSuccess (响应结果) {Number} balanceList.currencyMarketValue 当前币种的市值
     * @apiSuccess (响应结果) {String} balanceList.scaleType 销售类型: 1-直销;2-代销
     * @apiSuccess (响应结果) {String} balanceList.hkSaleFlag 好买香港代销标识: 0-否; 1-是
     * @apiSuccess (响应结果) {String} balanceList.StageEstablishFlag 分期成立标识(证券类有此标识:0-否,1-是)
     * @apiSuccess (响应结果) {String} balanceList.fractionateCallFlag 分次call标识(股权类有此标识:0-否,1-是)
     * @apiSuccess (响应结果) {String} balanceList.fundCXQXStr 产品存续期限(类似于5+3+2这种说明)
     * @apiSuccess (响应结果) {Number} balanceList.netBuyAmount 净购买金额(投资成本)
     * @apiSuccess (响应结果) {Number} balanceList.currencyNetBuyAmount 净购买金额(投资成本)(当前币种)
     * @apiSuccess (响应结果) {Number} balanceList.paidInAmt 认缴金额
     * @apiSuccess (响应结果) {String} balanceList.incomeDt 收益日期
     * @apiSuccess (响应结果) {String} balanceList.incomeCalStat 0-计算中；1-计算完成
     * @apiSuccess (响应结果) {Number} balanceList.currentAsset 当前收益(人民币）
     * @apiSuccess (响应结果) {Number} balanceList.currentAssetCurrency 当前收益（当前币种）
     * @apiSuccess (响应结果) {Number} balanceList.accumIncome 累计收益
     * @apiSuccess (响应结果) {Number} balanceList.accumIncomeRmb 累计收益(人民币)
     * @apiSuccess (响应结果) {Number} balanceList.accumRealizedIncome 累计已实现收益
     * @apiSuccess (响应结果) {Number} balanceList.accumRealizedIncomeRmb 累计已实现收益人民币
     * @apiSuccess (响应结果) {String} balanceList.rePurchaseFlag 是否复构 0-否 1-是
     * @apiSuccess (响应结果) {String} balanceList.benchmark 业绩比较基准
     * @apiSuccess (响应结果) {String} balanceList.benchmarkType 业绩比较基准类型：0-业绩比较基准（年化） 1-业绩报酬计提基准（年化）
     * @apiSuccess (响应结果) {String} balanceList.valueDate 起息日
     * @apiSuccess (响应结果) {String} balanceList.dueDate 到期日
     * @apiSuccess (响应结果) {String} balanceList.standardFixedIncomeFlag 标准固收标识(固收类有此标识:0-非标准固收，1-标准固收，2-现金管理， 3-券商集合理财 4-纯债产品)
     * @apiSuccess (响应结果) {String} balanceList.investmentHorizon
     * @apiSuccess (响应结果) {String} balanceList.cooperation
     * @apiSuccess (响应结果) {String} balanceList.crisisFlag
     * @apiSuccess (响应结果) {Number} balanceList.yieldIncome
     * @apiSuccess (响应结果) {String} balanceList.yieldIncomeDt
     * @apiSuccess (响应结果) {Number} balanceList.copiesIncome 万份收益
     * @apiSuccess (响应结果) {String} balanceList.hwSaleFlag 是否海外产品 0-否 1-是
     * @apiSuccess (响应结果) {String} balanceList.regDt 登记日期
     * @apiSuccess (响应结果) {String} balanceList.oneStepType 一级监管分类
     * @apiSuccess (响应结果) {String} balanceList.twoStepType 二级监管分类
     * @apiSuccess (响应结果) {String} balanceList.secondStepType 三级监管分类
     * @apiSuccess (响应结果) {String} balanceList.productSaleType 产品销售类型 0-好买 1-海外 2-其他
     * @apiSuccess (响应结果) {String} balanceList.naProductFeeType NA产品收费类型 10201-好买收费 0-管理人收费
     * @apiSuccess (响应结果) {Number} balanceList.receivManageFee 累计应收管理费
     * @apiSuccess (响应结果) {Number} balanceList.receivPreformFee 累计应收业绩报酬
     * @apiSuccess (响应结果) {Number} balanceList.currencyMarketValueExFee NA产品费后市值(当前币种， 减去当前累计应收管理费和累计应收业绩报酬)
     * @apiSuccess (响应结果) {Number} balanceList.marketValueExFee NA产品费后市值(人民币， 减去当前累计应收管理费和累计应收业绩报酬)
     * @apiSuccess (响应结果) {Number} balanceList.balanceIncomeNew 当前收益（股权新算法）不含费
     * @apiSuccess (响应结果) {Number} balanceList.balanceIncomeNewRmb 当前收益（股权新算法）不含费-人民币
     * @apiSuccess (响应结果) {Number} balanceList.balanceFactor 平衡因子
     * @apiSuccess (响应结果) {String} balanceList.convertFinish 平衡因子转换完成 1-是 0-否
     * @apiSuccess (响应结果) {String} balanceList.balanceFactorDate 平衡因子日期
     * @apiSuccess (响应结果) {Number} balanceList.yieldRate 收益率
     * @apiSuccess (响应结果) {Number} balanceList.accumIncomeNew 累计收益（股权固收新算法）不含费
     * @apiSuccess (响应结果) {Number} balanceList.accumIncomeNewRmb 累计收益（股权固收新算法）不含费-人民币
     * @apiSuccess (响应结果) {Number} balanceList.balanceCost 持仓总成本(人民币）
     * @apiSuccess (响应结果) {Number} balanceList.balanceCostCurrency 持仓总成本（当前币种）
     * @apiSuccess (响应结果) {Number} balanceList.dailyAsset 日收益(人民币）
     * @apiSuccess (响应结果) {Number} balanceList.dailyAssetCurrency 日收益（当前币种）
     * @apiSuccess (响应结果) {Number} balanceList.cashCollection 私募股权回款
     * @apiSuccess (响应结果) {Number} balanceList.currencyCashCollection 私募股权回款(当前币种)
     * @apiSuccess (响应结果) {Number} balanceList.accumYieldRate
     * @apiSuccess (响应结果) {Number} balanceList.accumCost
     * @apiSuccess (响应结果) {Number} balanceList.accumCostRmb
     * @apiSuccess (响应结果) {Number} balanceList.balanceFloatIncome
     * @apiSuccess (响应结果) {Number} balanceList.balanceFloatIncomeRmb
     * @apiSuccess (响应结果) {Number} balanceList.balanceFloatIncomeRate
     * @apiSuccess (响应结果) {Number} balanceList.dayAssetRate
     * @apiSuccess (响应结果) {Number} balanceList.dayIncomeGrowthRate
     * @apiSuccess (响应结果) {Number} balanceList.accumCostNew
     * @apiSuccess (响应结果) {Number} balanceList.accumCostRmbNew
     * @apiSuccess (响应结果) {Number} balanceList.balanceAmt
     * @apiSuccess (响应结果) {Number} balanceList.balanceAmtRmb
     * @apiSuccess (响应结果) {Number} balanceList.accumCollection
     * @apiSuccess (响应结果) {Number} balanceList.accumCollectionRmb
     * @apiSuccess (响应结果) {Number} balanceList.balanceAmtExFee
     * @apiSuccess (响应结果) {Number} balanceList.balanceAmtExFeeRmb
     * @apiSuccess (响应结果) {String} balanceList.sxz
     * @apiSuccess (响应结果) {Number} balanceList.currentIncome 当前收益(当前币种）
     * @apiSuccess (响应结果) {Number} balanceList.currentIncomeRmb 当前收益(人民币）
     * @apiSuccess (响应结果) {Number} balanceList.currentAccumIncome 当前累计收益(当前币种）
     * @apiSuccess (响应结果) {Number} balanceList.currentAccumIncomeRmb 当前累计收益(人民币）
     * @apiSuccess (响应结果) {String} balanceList.stageFlag 是否拆单产品 1-是
     * @apiSuccess (响应结果) {String} balanceList.establishDt 产品成立日期
     * @apiSuccess (响应结果) {String} balanceList.assetUpdateDate 收益计算日期
     * @apiSuccess (响应结果) {Number} balanceList.unitBalanceCostExFee 单位持仓成本去费
     * @apiSuccess (响应结果) {Number} balanceList.unitBalanceCostExFeeRmb 单位持仓成本去费(人民币)
     * @apiSuccess (响应结果) {String} balanceList.ownershipTransferIdentity 股权转让标识
     * @apiSuccess (响应结果) {String} balanceList.sfhwcxg 是否海外储蓄罐(1:是;0:否)
     * @apiSuccess (响应结果) {String} balanceList.cpqxsm 股权产品期限说明
     * @apiSuccess (响应结果) {String} balanceList.navDisclosureType 净值披露方式(1-净值,2-份额收益)--持仓特殊产品指标控制需求新增 20221122
     * @apiSuccess (响应结果) {String} balanceList.abnormalFlag 对于 阳光私募/非货基型券商集合 产品，若【最新净值日期】比第一笔交易的【确认日期】早3个月以上          异常标志(0-否 1-是)--持仓特殊产品指标控制需求新增 20221122
     * @apiSuccess (响应结果) {String} balanceList.marketValueCtl 人民币市值-是否控制表人为置空(0-否 1-是)
     * @apiSuccess (响应结果) {String} balanceList.currencyMarketValueCtl 当前币种市值-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.currencyMarketValueExFeeCtl NA产品费后市值（当前币种）-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.marketValueExFeeCtl NA产品费后市值（人民币）-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.currentAssetCtl 人民币收益-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.currentAssetCurrencyCtl 当前币种收益-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.dailyAssetCtl 人民币日收益-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.dailyAssetCurrencyCtl 当前币种日收益-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.accumIncomeCtl 累计收益-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.accumIncomeRmbCtl 累计收益人民币-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.accumRealizedIncomeCtl 累计已实现收益-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.accumRealizedIncomeRmbCtl 累计已实现收益人民币-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.balanceIncomeNewCtl 当前收益（股权新算法）不含费-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.balanceIncomeNewRmbCtl 当前收益（股权新算法）不含费人民币-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.accumIncomeNewCtl 累计收益（股权固收新算法）不含费-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.accumIncomeNewRmbCtl 累计收益（股权固收新算法）不含费人民币-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.yieldRateCtl 收益率-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.balanceVolCtl 份额-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.unconfirmedVolCtl 待确认份额-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.navCtl 净值-是否控制表人为置空
     * @apiSuccess (响应结果) {String} balanceList.qianXiFlag 是否为千禧年产品 0-否、1-是
     * @apiSuccess (响应结果) {Number} balanceList.unPaidInAmt 待投金额（人民币）
     * @apiSuccess (响应结果) {Number} balanceList.currencyUnPaidInAmt 待投金额（当前币种）
     * @apiSuccess (响应结果) {Array} unconfirmeProducts
     * @apiSuccess (响应结果) {String} unconfirmeProducts.fundCode 产品代码
     * @apiSuccess (响应结果) {String} unconfirmeProducts.productType 产品类型
     * @apiSuccess (响应结果) {String} unconfirmeProducts.productSubType 产品子类型
     * @apiSuccess (响应结果) {Number} unconfirmeProducts.unconfirmedAmt 待确认金额(人民币)
     * @apiSuccess (响应结果) {String} unconfirmeProducts.hkSaleFlag 好买香港代销标识: 0-否; 1-是
     * @apiSuccess (响应结果) {String} unconfirmeProducts.disCode 销售渠道
     * @apiSuccess (响应结果) {String} returnCode 返回码
     * @apiSuccess (响应结果) {String} description 描述
     * @apiSuccess (响应结果) {Number} totalCount 总记录数
     * @apiSuccess (响应结果) {Number} totalPage 总页数
     * @apiSuccess (响应结果) {Number} pageNo 当前页
     * @apiSuccess (响应结果) {String} txId txId
     * @apiSuccessExample 响应结果示例
     * {"hasHZProduct":"7hm6jUl6Rt","totalUnconfirmedAmt":1528.0404171615546,"totalPage":9714,"totalUnconfirmedNum":2871,"totalCashCollection":2449.2129304285627,"totalMarketValue":27.43974055211207,"description":"9v8I","txId":"wGsHEQZ","totalCount":9218,"returnCode":"O2Sxwh7Y","disCodeList":["uC"],"unconfirmeProducts":[{"fundCode":"NgO6Vvo","unconfirmedAmt":9126.060506889746,"disCode":"FAf","hkSaleFlag":"rAAfCT","productSubType":"g64beijz","productType":"8eHT1Kyg"}],"pageNo":5262,"txAcctNo":"ACnba1","balanceList":[{"balanceCostCurrency":9215.523156988864,"accumIncomeRmbCtl":"AeHqk18Lpr","dailyAsset":5929.421718634142,"currencyCashCollection":9515.540806376726,"disCode":"m8n0LFXpz","dailyAssetCurrency":3894.527150267173,"accumIncomeNewRmbCtl":"QRfRVc9V","balanceIncomeNewRmbCtl":"DZq","productSubType":"RMFitOwm","accumRealizedIncomeCtl":"LRgX","productName":"Qd","balanceIncomeNewRmb":4907.***********,"currentAsset":7590.912903805926,"oneStepType":"P0NIH","accumIncomeNewRmb":8175.081405397111,"accumYieldRate":1379.3001821519313,"productSaleType":"QC","cashCollection":1317.6459734521372,"currentAccumIncome":7215.883284939917,"balanceFloatIncomeRate":3268.1349192811194,"cooperation":"Bc2Pg","nav":6666.684870089662,"navDt":"vARqobc5","accumRealizedIncome":2090.4948442077866,"abnormalFlag":"4A1VQPGY","unconfirmedVolCtl":"smdvRof","dayAssetRate":5051.050462764669,"benchmark":"B8U","regDt":"WM","netBuyAmount":9984.860827052096,"balanceAmtExFeeRmb":585.4364545470547,"balanceFloatIncome":7920.895121477866,"accumIncomeNew":5828.031862677413,"currencyMarketValueExFee":846.7197562376161,"unPaidInAmt":1211.6882254618556,"dayIncomeGrowthRate":9086.471534996173,"marketValueExFeeCtl":"fC","establishDt":"Iw0rx","currencyMarketValue":448.1708651162075,"navDisclosureType":"tneLQ5U","assetUpdateDate":"dKFAjzZ","currency":"SCpFMP","balanceFactor":6762.920002731211,"accumIncomeRmb":1663.892529435367,"hwSaleFlag":"i","standardFixedIncomeFlag":"O5B","yieldIncome":8032.876035496552,"balanceVolCtl":"CJ8cIPgtF","balanceIncomeNew":4266.154093527815,"currentAccumIncomeRmb":5000.414634542816,"naProductFeeType":"kzP","qianXiFlag":"dfzpya","incomeCalStat":"3aJAtEn4b","yieldRate":1332.442070807387,"accumCollectionRmb":4429.464529261906,"productCode":"mMnW","paidInAmt":1957.***********37,"currentAssetCurrency":9898.471650145004,"accumCollection":7756.652309900403,"copiesIncome":6044.383560372228,"dailyAssetCtl":"4","accumIncomeNewCtl":"9X5gHCwnd","crisisFlag":"83wQ","dueDate":"L7omXvss","investmentHorizon":"rx","marketValueExFee":7697.6914585520335,"disCodeList":["Qv4"],"accumCost":191.14130975605326,"balanceAmtExFee":5205.467063749454,"StageEstablishFlag":"Q","incomeDt":"5Ac3kSF4","balanceAmtRmb":8502.00643249882,"balanceIncomeNewCtl":"PHM8xF","receivPreformFee":2113.791714485459,"accumIncome":5875.536987370564,"yieldRateCtl":"Q","currencyNetBuyAmount":9244.51615588994,"currencyUnPaidInAmt":2613.268163104848,"rePurchaseFlag":"yH4bfl9","ownershipTransferIdentity":"07mY6eI9FE","unitBalanceCostExFee":5828.186830959045,"sxz":"jPQQMqc6","scaleType":"nqcW6Btx","currentAssetCtl":"uEVfwK","currencyMarketValueCtl":"DvxaoGeHTS","navCtl":"WLqWiZjul","benchmarkType":"g","accumIncomeCtl":"S7xsZ","balanceFactorDate":"bTcYk5","currencyMarketValueExFeeCtl":"AU","secondStepType":"HvzkPd","navDivFlag":"l","fundCXQXStr":"hNZ9P","currentIncome":9517.643572863273,"balanceVol":1367.291859769999,"marketValueCtl":"M8rbsGLUi2","balanceAmt":7539.933353338186,"balanceCost":9265.14480438933,"yieldIncomeDt":"GJ3ypOZJl","balanceFloatIncomeRmb":9212.17465182469,"hkSaleFlag":"3iaf5ajpd","sfhwcxg":"ad6dvXJtCo","accumRealizedIncomeRmbCtl":"re","productType":"lCz","accumCostRmbNew":6672.18479783685,"cpqxsm":"zLY7","subProductCode":"i","twoStepType":"D3sFYiNA","accumCostRmb":1093.9306027840735,"dailyAssetCurrencyCtl":"KYhZkwyfQ","marketValue":6040.707702516541,"valueDate":"EHD","receivManageFee":151.24939829477714,"accumRealizedIncomeRmb":6961.7957371364455,"currentIncomeRmb":8696.091820008216,"unitBalanceCostExFeeRmb":3768.1110316469944,"unconfirmedVol":3439.1897173516127,"unconfirmedAmt":7894.024134052426,"fractionateCallFlag":"PSw4","currentAssetCurrencyCtl":"8Aq","stageFlag":"nerjH","convertFinish":"78t","accumCostNew":8767.291930307263}],"hasHKProduct":"iQ","totalCurrentAsset":67.78016558439236,"redeemUnconfirmedNum":8572,"totalIncomCalStat":"ni"}
     */
    public QueryAcctBalanceRequest() {
        setTxCode(TxCodes.HIGH_FUND_QUERY_ACCT_BALANCE);
        setDisCode(DisCodeEnum.HM.getCode());
    }

    /**
     * 好买香港代销标识
     */
    private String hkSaleFlag;
    /**
     * 产品代码
     */
    private String productCode;
    /**
     * 产品类型
     */
    private String productType;
    /**
     * 产品子类型
     */
    private String productSubType;
    /**
     * 协议类型，4-高端产品协议
     */
    private String protocolType = "4";

    /**
     * 分销机构代码列表-股权直销改造
     */
    private List<String> disCodeList;

    /**
     * 1-新资产中心
     * 2-老资产中心
     */
    private String callType = "2";
    /**
     * 是否授权,默认是按照已授权查询所有数据
     */
    @Deprecated
    private String isAuth;

    /**
     * 兼容老逻辑,注意,字段不传也是查持仓的
     * 持仓状态,0:不持仓,1:持仓,2:全部
     * 默认查持仓的
     */
    private String balanceStatus = "1";

    /**
     * 不过滤香港产品,1:是,0:否
     */
    private String notFilterHkFund;

    /**
     * 不过滤好臻产品,1:是,0:否
     */
    private String notFilterHzFund;

}


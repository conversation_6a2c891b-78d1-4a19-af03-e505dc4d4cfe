/**
 * Copyright (c) 2021, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.facade.search.querybalancevoldtlforcrm;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.validate.MyValidation;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;

import java.util.List;

/**
 * crm查询份额明细
 * <AUTHOR>
 * @date 2021/5/13 16:30
 * @since JDK 1.8
 */
public class QueryBalanceVolDtlForCrmRequest extends OrderSearchBaseRequest {

    private static final long serialVersionUID = -7184786246228598504L;

    /**
     * @api {dubbo} com.howbuy.tms.high.orders.facade.search.querybalancevoldtlforcrm.QueryBalanceVolDtlForCrmFacade.execute() crm查询份额明细
     * @apiGroup high-order-center
     * @apiDescription crm查询份额明细
     *
     * @apiUse orderBaseRequest
     * @apiUse orderSearchBaseRequest
     *
     * @apiParam {String} fundCode 基金代码
     * @apiParam {List} disCodeList 分销机构代码列表
     *
     * @apiParamExample {json} Request Example
     * dubbo com.howbuy.tms.high.orders.facade.search.querybalancevoldtlforcrm.QueryBalanceVolDtlForCrmRequest
     *{
     *    "appDt": "20170413",
     *    "appTm": "143122",
     *    "dataTrack": "0c7b031f-7e1f-4d7c-b1de-3eaaa735db9c",
     *    "disCode": "HB000A001",
     *    "operIp": "127.0.0.1",
     *    "outletCode": "A20150120",
     *    "pageNo": 1,
     *    "pageSize": 20,
     *    "fundCode": "481009",
     *    "hboneNo": "**********",
     *    "txChannel": "4",
     *    "txCode": "Z330016"
     *}
     *
     * @apiUse orderBaseResponse
     * @apiUse orderSearchBaseResponse
     *
     * @apiSuccess {List} balanceVolDtlList 持仓份额明细列表
     *
     * @apiSuccess (balanceVolDtlList) {BigDecimal} balanceVol 持仓份额
     * @apiSuccess (balanceVolDtlList) {String} bankName 银行名称
     * @apiSuccess (balanceVolDtlList) {String} bankAcctMask 银行卡号（按照掩码规则只给后六位）
     * @apiSuccess (balanceVolDtlList) {String} cpAcctNo 资金账号
     * @apiSuccess (balanceVolDtlList) {String} canRedeemDt 可赎回日期
     *
     * @apiSuccessExample {json} Response Example
     * dubbo com.howbuy.tms.high.orders.facade.search.querybalancevoldtlforcrm.QueryBalanceVolDtlForCrmResponse
     *{
     *    "balanceVolDtlList": [
     *        {
     *            "description": "成功",
     *            "returnCode": "Z0000000",
     *            "balanceVol": 20792.08,
     *            "bankName": "内蒙古自治区分行",
     *            "bankAcctMask": "******1369",
     *            "cpAcctNo": "****************",
     *            "canRedeemDt": ""
     *        }
     *    ],
     *
     *}
     *
     */
    public QueryBalanceVolDtlForCrmRequest() {
        setTxCode(TxCodes.HIGH_FUND_QUERY_BALANCE_VOL_DTL_FOR_CRM);
        setDisCode(DisCodeEnum.HM.getCode());
    }

    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金代码", isRequired = true, max = 6)
    private String fundCode;

    /**
     * 分销机构代码列表
     */
    private List<String> disCodeList;

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public List<String> getDisCodeList() {
        return disCodeList;
    }

    public void setDisCodeList(List<String> disCodeList) {
        this.disCodeList = disCodeList;
    }
}
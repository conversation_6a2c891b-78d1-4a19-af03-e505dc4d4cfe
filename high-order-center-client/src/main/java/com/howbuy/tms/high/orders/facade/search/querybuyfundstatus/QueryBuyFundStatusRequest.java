/**
 *Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.facade.search.querybuyfundstatus;

import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import java.util.List;

/**
 * 
 * @description:(查询产品购买状态)
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2017年11月28日 上午9:15:15
 * @since JDK 1.6
 */
public class QueryBuyFundStatusRequest extends OrderSearchBaseRequest {

    private static final long serialVersionUID = 247721317350139773L;

    public QueryBuyFundStatusRequest() {
        this.setTxCode(TxCodes.QUERY_BUY_FUND_STATUS);
    }

    /**
     * 产品代码
     */
    private List<String> productCodeList;
    /**
     * 预约日历场景标识 1-是
     */
    private String appointmentFlag;
    /**
     * 是否首页使用 1-是
     */
    private String homePage;
    /**
     * 是否不校验渠道,1:不校验,0/空:校验
     */
    private String notCheckChannel;

    public String getNotCheckChannel() {
        return notCheckChannel;
    }

    public void setNotCheckChannel(String notCheckChannel) {
        this.notCheckChannel = notCheckChannel;
    }

    public List<String> getProductCodeList() {
        return productCodeList;
    }

    public void setProductCodeList(List<String> productCodeList) {
        this.productCodeList = productCodeList;
    }

    public String getAppointmentFlag() {
        return appointmentFlag;
    }

    public void setAppointmentFlag(String appointmentFlag) {
        this.appointmentFlag = appointmentFlag;
    }

    public String getHomePage() {
        return homePage;
    }

    public void setHomePage(String homePage) {
        this.homePage = homePage;
    }
}
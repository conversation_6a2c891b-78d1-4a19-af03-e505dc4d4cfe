/**
 *Copyright (c) 2016, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.querycustbalancedetail;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.validate.MyValidation;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;

import java.util.List;

/**
 * 
 * @description:查询用户持仓明细(银行卡维度或者基金维度)
 */

/**
* @api {dubbo} com.howbuy.tms.high.orders.facade.search.querycustbalancedetail.QueryCustBalanceDetailFacade.execute() 查询用户持仓明细接口
* @apiGroup high-order-center-search
* @apiDescription 查询用户持仓明细
*
*
* @apiParam {String} txAcctNo 交易帐号
* @apiParam {String} idNo 交易帐号
* @apiParam {String} disCode 分销号
* @apiParam {String} fundCode 基金代码
* @apiParam {String} bankAcct 银行账号
* @apiParam {String} shareType 份额业务类型
*
* @apiUse orderSearchBaseResponse
* @apiSuccess {QueryCustBalanceDetailResponse} queryCustBalanceDetailResponse
* @apiSuccess (queryCustBalanceDetailResponse) {List(CustBalanceDetail)} custBalanceDetailList 用户持仓详情列表
* @apiSuccess (CustBalanceDetail) {String} cpAcctNo 资金账号
* @apiSuccess (CustBalanceDetail) {String} fundCode 基金代码
* @apiSuccess (CustBalanceDetail) {String} fundAttr 基金简称
* @apiSuccess (CustBalanceDetail) {String} taCode 基金TA代码
* @apiSuccess (CustBalanceDetail) {String} protocolType 协议类型
* @apiSuccess (CustBalanceDetail) {String} protocolNo 协议号
* @apiSuccess (CustBalanceDetail) {String} bankAcct 银行卡号
* @apiSuccess (CustBalanceDetail) {String} bankName 银行名称
* @apiSuccess (CustBalanceDetail) {BigDecimal} balanceVol 总份额
* @apiSuccess (CustBalanceDetail) {BigDecimal} availVol 可用份额
* @apiSuccess (CustBalanceDetail) {BigDecimal} unconfirmedVol 冻结金额
* @apiSuccess (CustBalanceDetail) {BigDecimal} justFrznVol 司法冻结份额
*/

public class QueryCustBalanceDetailRequest extends OrderSearchBaseRequest {

	/**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    private static final long serialVersionUID = -8199019963766046871L;

    public QueryCustBalanceDetailRequest() {
		this.setTxCode(TxCodes.QUERY_HIGH_CUST_BALANCE_DETAIL);
	}

	/**
	 * 基金代码
	 */
	@MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金代码", isRequired = false, max = 10)
	private String fundCode;
	
	/**
	 * 资金账号列表
	 */
	private List<String> cpAcctNos;
	
	/**
	 * 银行卡号
	 */
	@MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "银行卡号", isRequired = false, max = 30)
	private String bankAcct;
	
	/**
	 * 份额业务类型
	 */
	@MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "份额业务类型", isRequired = true, max = 3)
	private String shareType;

    public List<String> getCpAcctNos() {
        return cpAcctNos;
    }

    public void setCpAcctNos(List<String> cpAcctNos) {
        this.cpAcctNos = cpAcctNos;
    }

	public String getFundCode() {
		return fundCode;
	}

	public void setFundCode(String fundCode) {
		this.fundCode = fundCode;
	}

	public String getBankAcct() {
		return bankAcct;
	}

	public void setBankAcct(String bankAcct) {
		this.bankAcct = bankAcct;
	}

    public String getShareType() {
        return shareType;
    }

    public void setShareType(String shareType) {
        this.shareType = shareType;
    }
}

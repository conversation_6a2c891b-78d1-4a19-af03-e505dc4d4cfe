/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.high.orders.facade.search.querycustfunddiv;

import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;

import java.util.List;

/**
 * @description:(查询客户持有基金的分红方式 request) 
 * <AUTHOR>
 * @date 2017年4月17日 下午1:24:11
 * @since JDK 1.6
 */
public class QueryCustFundDivRequest extends OrderSearchBaseRequest {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    
    private static final long serialVersionUID = -6734876136340338621L;
    
    /**
     * 协议类型，4-高端产品协议
     */
    private String protocolType = "4";

    /**
     * 分销代码列表
     */
    private List<String> disCodeList;

    /**
     * 是否授权,默认是认为授权,查询所有数据
     */
    @Deprecated
    private String isAuth= YesOrNoEnum.YES.getCode();

    /**
     * 不过滤香港产品,1:是,0:否
     */
    private String notFilterHkFund;

    /**
     * 不过滤好臻产品,1:是,0:否
     */
    private String notFilterHzFund;
    /**
     * @api {post} dubbo:com.howbuy.tms.high.orders.facade.search.querycustfunddiv.QueryCustFundDivFacade.execute() 查询高端用户修改分红方式查询
     * @apiGroup high-order-center
     * @apiDescription 查询高端用户修改分红方式查询接口
     * 
     * @apiUse orderBaseRequest
     * @apiUse orderSearchBaseRequest
     * 
     * @apiParam {String} [protocolType]  协议类型  = 4-高端协议类型
     * 
     * @apiParamExample {json} Request Example
     * dubbo com.howbuy.tms.high.orders.facade.search.queryacctbalancedtl.QueryAcctBalanceDtlRequest
     *{
     *    "appDt": "20170413", 
     *    "appTm": "143122", 
     *    "dataTrack": "0c7b031f-7e1f-4d7c-b1de-3eaaa735db9c", 
     *    "disCode": "HB000A001", 
     *    "operIp": "127.0.0.1", 
     *    "outletCode": "A20150120", 
     *    "pageNo": 1, 
     *    "pageSize": 20, 
     *    "productCode": "481009", 
     *    "protocolType": "4", 
     *    "txAcctNo": "1100875141", 
     *    "txChannel": "4", 
     *    "txCode": "Z330021"
     *}
     * 
     * 
     * @apiSuccess {custFundDivBean} custFundDivBean 基金分红方式
     * 
     * @apiSuccess (custFundDivBean) {List} custFundDivList 持有基金分红方式列表
     * 
     * @apiSuccess (custFundDivBean) {String} fundAttr 基金简称
     * @apiSuccess (custFundDivBean) {String} fundAttrHb 好买基金简称
     * @apiSuccess (custFundDivBean) {String} fundCode 基金代码
     * @apiSuccess (custFundDivBean) {String} divMode  分红方式
     *                                                   0-红利再投; 1-现金红利
     * @apiSuccess (custFundDivBean) {String} shareClass 份额类型
     * @apiSuccess (custFundDivBean) {String} allowModifyDivMode 是否准许修改分红方式 
     *                                                   0-不准许修改; 1-准许修改;2-确认中
     * @apiSuccessExample {json} Response Example
     * dubbo com.howbuy.tms.high.orders.facade.search.querycustfunddiv.QueryCustFundDivResponse
    *{
    *    "custFundDivList": [
    *        {
    *            "fundAttr": "工银货币", 
    *            "fundAttrHb": 工银货币, 
    *            "fundCode": "482001", 
    *            "divMode": "0", 
    *            "shareClass": "A", 
    *            "allowModifyDivMode": 0
    *        }
    *    ], 
    *    "returnCode": "Z0000000", 
    *    "totalCount": 0, 
    *    "totalPage": 0, 
    *    "txAcctNo": "1100875141"
    *}
     * 
     */
    public QueryCustFundDivRequest(){
        super.setTxCode(TxCodes.QUERY_CUST_FUND_DIV);
    }

    public String getIsAuth() {
        return isAuth;
    }

    public void setIsAuth(String isAuth) {
        this.isAuth = isAuth;
    }

    public String getProtocolType() {
        return protocolType;
    }

    public String getNotFilterHkFund() {
        return notFilterHkFund;
    }

    public void setNotFilterHkFund(String notFilterHkFund) {
        this.notFilterHkFund = notFilterHkFund;
    }

    public String getNotFilterHzFund() {
        return notFilterHzFund;
    }

    public void setNotFilterHzFund(String notFilterHzFund) {
        this.notFilterHzFund = notFilterHzFund;
    }

    public void setProtocolType(String protocolType) {
        this.protocolType = protocolType;
    }

    public List<String> getDisCodeList() {
        return disCodeList;
    }

    public void setDisCodeList(List<String> disCodeList) {
        this.disCodeList = disCodeList;
    }
}


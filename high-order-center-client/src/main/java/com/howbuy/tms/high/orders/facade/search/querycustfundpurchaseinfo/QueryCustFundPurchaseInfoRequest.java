package com.howbuy.tms.high.orders.facade.search.querycustfundpurchaseinfo;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.common.validate.MyValidation;
import com.howbuy.tms.high.orders.facade.common.OrderBaseRequest;

import java.util.List;

import com.howbuy.tms.high.orders.facade.enums.TxCodes ;

/**
 * Copyright (c) 2025, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

/**
 * @description: 查询客户产品购买信息请求对象
 * @author: hongdong.xie
 * @date: 2025-03-20 13:19:43
 * @since JDK 1.8
 */
public class QueryCustFundPurchaseInfoRequest extends OrderBaseRequest {
    /**
     * 一账通账号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "一账通账号", isRequired = true, max = 10)
    private String hbOneNo;
    /**
     * 基金代码列表
     */
    private List<String> fundCodeList;

    public QueryCustFundPurchaseInfoRequest() {
        setTxCode(TxCodes.QUERY_BUY_FUND_STATUS);
    }

    public String getHbOneNo() {
        return hbOneNo;
    }

    public void setHbOneNo(String hbOneNo) {
        this.hbOneNo = hbOneNo;
    }

    public List<String> getFundCodeList() {
        return fundCodeList;
    }

    public void setFundCodeList(List<String> fundCodeList) {
        this.fundCodeList = fundCodeList;
    }
} 
package com.howbuy.tms.high.orders.facade.search.querycustfundrisklevel;

import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;

import java.util.List;

public class QueryCustFundRiskLevelRequest  extends OrderSearchBaseRequest {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = -1L;

    public QueryCustFundRiskLevelRequest(){
        super.setTxCode(TxCodes.QUERY_CUST_FUND_RISK_LEVEL);
    }

}

/**
 * Copyright (c) 2016, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.facade.search.querydealorder;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.validate.MyValidation;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;

/**
 * @description:(查询交易订单接口 request)
 * @reason:
 * <AUTHOR>
 * @date 2017年3月31日 上午11:31:11
 * @since JDK 1.7
 */

public class QueryDealOrderRequest extends OrderSearchBaseRequest {

    private static final long serialVersionUID = 247721317350139773L;


    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.querydealorder.QueryDealOrderFacade.execute() 查询交易订单接口
     * @apiGroup high-order-center
     * @apiDescription 查询交易订单接口
     *
     * @apiUse orderBaseRequest
     * @apiUse orderSearchBaseRequest
     *
     * @apiParam {String} dealNo 订单号
     * @apiParamExample {json} Request Example
     * dubbo com.howbuy.tms.high.orders.facade.search.querydealorder.QueryDealOrderRequest
     *
     * @apiUse orderBaseResponse
     * @apiUse orderSearchBaseResponse
     *
     * @apiSuccess {String} dealNo 客户订单号
     * @apiSuccess {String} txAcctNo 交易账号
     * @apiSuccess {String} appointmentDealNo 预约订单号
     * @apiSuccess {String} custName 客户姓名
     * @apiSuccess {String} mBusiCode 中台业务码 1120-认购；1122-申购；1124-赎回；1142-强赎；1143-分红；1144-强增；1145-强减；1129-修改分红方式
     * @apiSuccess {String} productName 产品名称
     * @apiSuccess {String} productCode 产品代码
     * @apiSuccess {String} paymentType 支付方式 01-自划款；04-银行卡代扣；06-储蓄罐代扣
     * @apiSuccess {BigDecimal} appAmt 申请金额
     * @apiSuccess {BigDecimal} appVol 申请份额
     * @apiSuccess {BigDecimal} ackVol 确认份额
     * @apiSuccess {BigDecimal} ackAmt 确认金额
     * @apiSuccess {BigDecimal} fee 手续费
     * @apiSuccess {BigDecimal} discountRate 折扣率
     * @apiSuccess {String} payEndDate 打款截止日期
     * @apiSuccess {String} appDate 申请日期  yyyyMMdd
     * @apiSuccess {String} appTime 申请时间 hhMMss
     * @apiSuccess {String} txPmtFlag 支付标识  0-无需付款；1-未付款；2-付款成功；3-付款失败；4-付款中；5-已退款
     * @apiSuccess {String} pmtCompFlag 支付对账标识 0-无需对账；1-未对账；2-对账完成；3-金额不一致；
     * @apiSuccess {Date} pmtCompleteDtm 支付完成时间（yyyyMMddhhmmss）
     * @apiSuccess {String} orderStatus 订单状态 1-申请成功 2-部分确认 3-确认成功 4-确认失败 5-自行撤销;6-强制取消
     * @apiSuccess {Date} calmDtm冷静期时间      yyyymmddHH24miss
     * @apiSuccess {String} txChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；5-App
     * @apiSuccess {String} taTradeDt TA交易日期(上报日)
     * @apiSuccess {String} txChannel  交易渠道
     * @apiSuccess {String} feeCalMode 手续费计算类型，0-外扣法；1-内扣法
     * @apiSuccess {String} submitAppFlag 上报申请标识 0-无需上报 1-上报中 2-上报完成 3-需重新上报
     * @apiSuccess {String} currency  币种
     * @apiSuccess {String} bankAcct  银行卡号
     * @apiSuccess {String} bankCode  银行code
     * @apiSuccess {String} idType    证件类型
     * @apiSuccess {String} idNo    证件号
     * @apiSuccess {String} cpAcctNo 资金账号
     * @apiSuccess {String} redeemDirection 赎回去向  0-银行卡  1-储蓄罐
     * @apiSuccess {String} expectedTradeDt 预计交易日期
     * @apiSuccess {BigDecimal}  nav 净值
     * @apiSuccess {String} advanceFlag 递延标识 1-不顺延；2-顺延
     * @apiSuccess {String} firstBuyFlag 首次购买标识 1-首次购买；2-追加购买
     * @apiSuccess {Date} appDtm 下单时间
     * @apiSuccess {String} submitTaDt 上报TA日期
     * @apiSuccess {String} volConfirmBookPath 份额确认书路径
     * @apiSuccess {String} peDivideCallFlag 是否分次CALL款股权产品 0-否 1是
     * @apiSuccess {String} subsAmt 认缴金额
     * @apiSuccessExample {json} Response Example
     * dubbo com.howbuy.tms.high.orders.facade.search.querydealorder.QueryDealOrderResponse
     *
     * {
     *      "appAmt": 3528000,
     *      "appDate": "20171025",
     *      "appTime": "100000",
     *      "appVol": null,
     *      "appointmentDealNo": "",
     *      "calmDtm": 1508904035029,
     *      "currency": "156",
     *      "custName": "测试",
     *      "dealNo": "1017102510563600001083210",
     *      "description": "成功",
     *      "discountRate": 1,
     *      "fee": null,
     *      "feeCalMode": "0",
     *      "mBusiCode": "1120",
     *      "orderStatus": "1",
     *      "pageNo": 0,
     *      "payEndDate": "",
     *      "paymentType": "01",
     *      "pmtCompFlag": "2",
     *      "pmtCompleteDtm": 1508904035029,
     *      "productCode": "028051",
     *      "productName": "国泰嘉祥债券分级",
     *      "returnCode": "Z0000000",
     *      "submitAppFlag": "2",
     *      "taTradeDt": "20171025",
     *      "totalCount": 0,
     *      "totalPage": 0,
     *      "txAcctNo": "1100876000",
     *      "txChannel": "1",
     *      "txPmtFlag": "2",
     *      "peDivideCallFlag": "0",
     *      "subsAmt": "200"
     *  }
     *
     */
    public QueryDealOrderRequest() {
        this.setTxCode(TxCodes.QUERY_HIGH_DEAL_ORDER);
    }

    /**
     * 订单号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "订单号", isRequired = true)
    private String dealNo;

    public String getDealNo() {
        return dealNo;
    }

    public void setDealNo(String dealNo) {
        this.dealNo = dealNo;
    }

}
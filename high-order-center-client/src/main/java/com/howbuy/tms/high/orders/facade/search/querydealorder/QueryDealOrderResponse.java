/**
 * Copyright (c) 2016, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.querydealorder;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description:(查询交易订单)
 * @reason:
 * @date 2017年3月31日 上午11:15:50
 * @since JDK 1.7
 */
public class QueryDealOrderResponse extends OrderSearchBaseResponse {

    private static final long serialVersionUID = -4239262718357701019L;
    /**
     * 订单号
     */
    private String dealNo;
    /**
     * 交易账号
     */
    private String txAcctNo;
    /**
     * 预约单号
     */
    private String appointmentDealNo;
    /**
     * 业务代码
     */
    private String mBusiCode;
    /**
     * 客户姓名
     */
    private String custName;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 产品代码
     */
    private String productCode;
    /**
     * 支付方式
     */
    private String paymentType;
    /**
     * 申请金额
     */
    private BigDecimal appAmt;
    /**
     * 申请份额
     */
    private BigDecimal appVol;
    /**
     * 申请日期
     */
    private String appDate;
    /**
     * 申请时间
     */
    private String appTime;
    /**
     * 订单状态
     */
    private String orderStatus;
    /**
     * 交易渠道
     */
    private String txChannel;
    /**
     * 交易时间
     */
    private String taTradeDt;
    /**
     * 手续费
     */
    private BigDecimal fee;
    /**
     * 最终折扣
     */
    private BigDecimal discountRate;
    /**
     * 付款截止日期
     */
    private String payEndDate;
    /**
     * 0-无需付款；
     * 1-未付款；
     * 2-付款成功；
     * 3-付款失败；
     * 4-付款中；
     * 5-已退款;
     * 6-等待付款；
     * 7-撤单成功；
     * 8-撤单中；
     * 9-未冻结
     * 10-冻结中；
     * 11-冻结成功；
     * 12-冻结失败；
     * 13-解冻中
     * 14-解冻成功；
     * 15-解冻失败；
     * 16-冻结支付中；
     * 17-冻结支付成功；
     * 18-冻结支付失败'
     */
    private String txPmtFlag;
    /**
     * 支付对账标记
     * 0-无需对账；
     * 1-未对账；
     * 2-对账完成；
     * 3-金额不一致
     */
    private String pmtCompFlag;
    /**
     * 支付完成日期时间
     */
    private Date pmtCompleteDtm;
    /**
     * 冷静期时间
     */
    private Date calmDtm;
    /**
     * 手续费计算类型，0-外扣法；1-内扣法
     */
    private String feeCalMode;
    /**
     * 上报申请标记
     * 0-无需上报
     * 1-上报中
     * 2-上报完成
     * 3-需重新上报
     */
    private String submitAppFlag;
    /**
     * 货币类型
     */
    private String currency;
    /**
     * 银行账户
     */
    private String bankAcct;
    /**
     * 银行编码
     */
    private String bankCode;
    /**
     * 证件类型
     */
    private String idType;
    /**
     * 证件号
     */
    private String idNo;
    /**
     * 资金账号
     */
    private String cpAcctNo;
    /**
     * 回款方向
     */
    private String redeemDirection;
    /**
     * ta上报日
     */
    private String expectedTradeDt;
    /**
     * 净值
     */
    private BigDecimal nav;
    /**
     * 顺延标志：1-不顺延；2-顺延
     */
    private String advanceFlag;
    /**
     * 首次购买标识，1-首次购买；2-追加购买
     */
    private String firstBuyFlag;
    /**
     * 申请时间
     */
    private Date appDtm;
    /**
     * 确认金额
     */
    private BigDecimal ackAmt;
    /**
     * 确认份额
     */
    private BigDecimal ackVol;
    /**
     * ta上报日
     */
    private String submitTaDt;
    /**
     * 份额确认书地址
     */
    private String volConfirmBookPath;

    /**
     * 定投编号
     */
    private String planId;
    /**
     * 是否是定投首期 0-不是 1-是
     */
    private String firstPlanFlag;
    /**
     * 支付单号
     */
    private String pmtDealNo;

    /**
     * 是否分次CALL款股权产品 0-否 1是
     */
    private String peDivideCallFlag;

    /**
     * 认缴金额
     */
    private BigDecimal subsAmt;

    /**
     * 1:失效,0:有效
     */
    private String recStat;

    /**
     * 客户风险等级
     */
    private String custRiskLevel;

    /**
     * 产品风险等级
     */
    private String fundRiskLevel;

    /**
     * 投资者类别: 0-普通; 1-专业
     */
    private String qualificationType;

    /**
     * 折扣类型：1-使用折扣率，2-使用折扣金额
     * {@link com.howbuy.crm.base.discount.DisCountUseTypeEnum}
     */
    private String discountUseType;

    /**
     * 折扣金额
     */
    private BigDecimal discountAmt;

    public String getDiscountUseType() {
        return discountUseType;
    }

    public void setDiscountUseType(String discountUseType) {
        this.discountUseType = discountUseType;
    }

    public BigDecimal getDiscountAmt() {
        return discountAmt;
    }

    public void setDiscountAmt(BigDecimal discountAmt) {
        this.discountAmt = discountAmt;
    }

    public String getCustRiskLevel() {
        return custRiskLevel;
    }

    public void setCustRiskLevel(String custRiskLevel) {
        this.custRiskLevel = custRiskLevel;
    }

    public String getFundRiskLevel() {
        return fundRiskLevel;
    }

    public void setFundRiskLevel(String fundRiskLevel) {
        this.fundRiskLevel = fundRiskLevel;
    }

    public String getQualificationType() {
        return qualificationType;
    }

    public void setQualificationType(String qualificationType) {
        this.qualificationType = qualificationType;
    }

    public String getRecStat() {
        return recStat;
    }

    public void setRecStat(String recStat) {
        this.recStat = recStat;
    }

    public String getPeDivideCallFlag() {
        return peDivideCallFlag;
    }

    public void setPeDivideCallFlag(String peDivideCallFlag) {
        this.peDivideCallFlag = peDivideCallFlag;
    }

    public BigDecimal getSubsAmt() {
        return subsAmt;
    }

    public void setSubsAmt(BigDecimal subsAmt) {
        this.subsAmt = subsAmt;
    }

    public BigDecimal getAckAmt() {
        return ackAmt;
    }

    public void setAckAmt(BigDecimal ackAmt) {
        this.ackAmt = ackAmt;
    }

    public BigDecimal getAckVol() {
        return ackVol;
    }

    public void setAckVol(BigDecimal ackVol) {
        this.ackVol = ackVol;
    }

    public String getDealNo() {
        return dealNo;
    }

    public void setDealNo(String dealNo) {
        this.dealNo = dealNo;
    }

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    public String getAppointmentDealNo() {
        return appointmentDealNo;
    }

    public void setAppointmentDealNo(String appointmentDealNo) {
        this.appointmentDealNo = appointmentDealNo;
    }

    public String getmBusiCode() {
        return mBusiCode;
    }

    public void setmBusiCode(String mBusiCode) {
        this.mBusiCode = mBusiCode;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public BigDecimal getAppAmt() {
        return appAmt;
    }

    public void setAppAmt(BigDecimal appAmt) {
        this.appAmt = appAmt;
    }

    public BigDecimal getAppVol() {
        return appVol;
    }

    public void setAppVol(BigDecimal appVol) {
        this.appVol = appVol;
    }

    public String getAppDate() {
        return appDate;
    }

    public void setAppDate(String appDate) {
        this.appDate = appDate;
    }

    public String getAppTime() {
        return appTime;
    }

    public void setAppTime(String appTime) {
        this.appTime = appTime;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getTxChannel() {
        return txChannel;
    }

    public void setTxChannel(String txChannel) {
        this.txChannel = txChannel;
    }

    public String getTaTradeDt() {
        return taTradeDt;
    }

    public void setTaTradeDt(String taTradeDt) {
        this.taTradeDt = taTradeDt;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public BigDecimal getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(BigDecimal discountRate) {
        this.discountRate = discountRate;
    }

    public String getPayEndDate() {
        return payEndDate;
    }

    public void setPayEndDate(String payEndDate) {
        this.payEndDate = payEndDate;
    }

    public String getTxPmtFlag() {
        return txPmtFlag;
    }

    public void setTxPmtFlag(String txPmtFlag) {
        this.txPmtFlag = txPmtFlag;
    }

    public String getPmtCompFlag() {
        return pmtCompFlag;
    }

    public void setPmtCompFlag(String pmtCompFlag) {
        this.pmtCompFlag = pmtCompFlag;
    }

    public Date getPmtCompleteDtm() {
        return pmtCompleteDtm;
    }

    public void setPmtCompleteDtm(Date pmtCompleteDtm) {
        this.pmtCompleteDtm = pmtCompleteDtm;
    }

    public Date getCalmDtm() {
        return calmDtm;
    }

    public void setCalmDtm(Date calmDtm) {
        this.calmDtm = calmDtm;
    }

    public String getFeeCalMode() {
        return feeCalMode;
    }

    public void setFeeCalMode(String feeCalMode) {
        this.feeCalMode = feeCalMode;
    }

    public String getSubmitAppFlag() {
        return submitAppFlag;
    }

    public void setSubmitAppFlag(String submitAppFlag) {
        this.submitAppFlag = submitAppFlag;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getBankAcct() {
        return bankAcct;
    }

    public void setBankAcct(String bankAcct) {
        this.bankAcct = bankAcct;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }

    public String getRedeemDirection() {
        return redeemDirection;
    }

    public void setRedeemDirection(String redeemDirection) {
        this.redeemDirection = redeemDirection;
    }

    public String getExpectedTradeDt() {
        return expectedTradeDt;
    }

    public void setExpectedTradeDt(String expectedTradeDt) {
        this.expectedTradeDt = expectedTradeDt;
    }

    public BigDecimal getNav() {
        return nav;
    }

    public void setNav(BigDecimal nav) {
        this.nav = nav;
    }

    public String getAdvanceFlag() {
        return advanceFlag;
    }

    public void setAdvanceFlag(String advanceFlag) {
        this.advanceFlag = advanceFlag;
    }

    public String getFirstBuyFlag() {
        return firstBuyFlag;
    }

    public void setFirstBuyFlag(String firstBuyFlag) {
        this.firstBuyFlag = firstBuyFlag;
    }

    public Date getAppDtm() {
        return appDtm;
    }

    public void setAppDtm(Date appDtm) {
        this.appDtm = appDtm;
    }

    public String getSubmitTaDt() {
        return submitTaDt;
    }

    public void setSubmitTaDt(String submitTaDt) {
        this.submitTaDt = submitTaDt;
    }

    public String getVolConfirmBookPath() {
        return volConfirmBookPath;
    }

    public void setVolConfirmBookPath(String volConfirmBookPath) {
        this.volConfirmBookPath = volConfirmBookPath;
    }

    public String getPlanId() {
        return planId;
    }

    public void setPlanId(String planId) {
        this.planId = planId;
    }

    public String getFirstPlanFlag() {
        return firstPlanFlag;
    }

    public void setFirstPlanFlag(String firstPlanFlag) {
        this.firstPlanFlag = firstPlanFlag;
    }

    public String getPmtDealNo() {
        return pmtDealNo;
    }

    public void setPmtDealNo(String pmtDealNo) {
        this.pmtDealNo = pmtDealNo;
    }
}
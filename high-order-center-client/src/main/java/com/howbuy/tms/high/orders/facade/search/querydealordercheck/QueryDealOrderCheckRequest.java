package com.howbuy.tms.high.orders.facade.search.querydealordercheck;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.common.client.BaseRequest;
import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.validate.MyValidation;
import com.howbuy.tms.high.orders.facade.common.OrderBaseRequest;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;

import java.util.List;

/**
 * @description:(1. （需求1792）数据核对自动化需求)
 * @reason:
 */

public class QueryDealOrderCheckRequest extends OrderBaseRequest {

    private static final long serialVersionUID = 247721317350139773L;



    // 产品代码集合
    private List<String> fundCodes;
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "预约截止日期")
    private String apponitEndDt;
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "上报TA日期")
    private String submitTaDt;

    /**
     * 业务类型：0-购买（1120-认购、1122-申购）、1-赎回（1124-赎回）
     */
    private String businessType;

    /**
     * 基金类型：0-高端公募（7-一对多专户、9-券商小集合）、1-高端私募（11-私募）
     */
    private String fundType;

    public List<String> getFundCodes() {
        return fundCodes;
    }

    public void setFundCodes(List<String> fundCodes) {
        this.fundCodes = fundCodes;
    }

    public String getApponitEndDt() {
        return apponitEndDt;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType;
    }

    public void setApponitEndDt(String apponitEndDt) {
        this.apponitEndDt = apponitEndDt;
    }

    public String getSubmitTaDt() {
        return submitTaDt;
    }

    public void setSubmitTaDt(String submitTaDt) {
        this.submitTaDt = submitTaDt;
    }
}
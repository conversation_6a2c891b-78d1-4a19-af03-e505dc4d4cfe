/**
 *Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.facade.search.querydealorderfores;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.validate.MyValidation;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;

/**
 * 
 * @description:查询订单信息（ES-CENTER查询用）
 * <AUTHOR>
 * @date 2019年3月5日 下午4:46:01
 * @since JDK 1.6
 */
public class QueryHighDealOrderEsRequest extends OrderSearchBaseRequest {

	private static final long serialVersionUID = 247721317350139773L;
	

	public QueryHighDealOrderEsRequest() {
	    this.setTxCode(TxCodes.QUERY_HIGH_DEAL_ORDER_ES);
	}
	
    /**
     * 订单号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "订单号", isRequired = true)
    private String dealNo;

    public String getDealNo() {
        return dealNo;
    }

    public void setDealNo(String dealNo) {
        this.dealNo = dealNo;
    }
	
}
/**
 *Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.facade.search.querydealorderlist;

import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import java.util.Date;
import java.util.List;

/**
 * @description:(查询订单列表 request)
 * @reason:
 * <AUTHOR>
 * @date 2017年3月31日 上午11:31:11
 * @since JDK 1.7
 */

public class QueryDealOrderListRequest extends OrderSearchBaseRequest {

	private static final long serialVersionUID = 247721317350139773L;
	
	/**
	 * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListFacade.execute() 查询交易订单列表接口
	 * @apiGroup high-order-center
	 * @apiDescription 查询交易订单列表
	 * 
	 * @apiUse orderBaseRequest
	 * @apiUse orderSearchBaseRequest
	 * 
	 * @apiParam {String} [productCode]  母产品代码
	 * @apiParam {String} [dealNo]  订单号
	 * @apiParam {String} [productName]  产品名称
	 * @apiParam {String} [appBeginDtm]  查询申请开始时间
	 * @apiParam {String} [appEndDtm]    查询申请结束时间
	 * @apiParam {String} [cpAcctNo]     资金帐号
	 * @apiParam {String} [isAuth]     是否授权,1:是,0:否
	 * @apiParam {String} [notFilterHkFund]     不过滤香港产品,默认不过滤,1:是,0:否
	 * @apiParam {String} [notFilterHzFund;]    不过滤好臻产品,默认不过滤,1:是,0:否
	 * @apiParam {String[]} [mBusiCodeArr]  交易业务码<br>1120-认购;1122-申购;1124-赎回; 1129-修改分红方式
	 * @apiParam {String[]} [orderStatusArr]  订单状态<br>1-申请成功;2-部分确认;3-确认成功;4-确认失败;5-自行撤销;6-强制取消	
	 * @apiParam {String} [fundNameOrCode]    基金代码或名称
	 * @apiParamExample {json} Request Example
	 * dubbo com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListRequest
	 *{
	 *    "appDt": "20170413", 
	 *    "appTm": "143122", 
	 *    "disCode": "HB000A001", 
	 *    "txAcctNo": "**********", 
	 *    "hbOneNo": "**********", 
	 *    "outletCode": "A20150120", 
	 *    "appBeginDtm": *************,
	 *    "appEndDtm": "*************",
	 *    "productCode": "481001",
	 *    "productName": "XXX",
	 *    "mBusiCodeArr": ["1120","1122"],
	 *    "orderStatusArr": ["0","1"],
	 *    "cpAcctNo":"****************",
	 *    "pageNo": 1, 
	 *    "pageSize": 20,
	 *}
	 *
	 * @apiUse orderBaseResponse
	 * @apiUse orderSearchBaseResponse
	 *
	 * @apiSuccess {HighOrderBean} highOrderBean 高端订单
	 * 
	 * @apiSuccess (highOrderBean) {List} highOrderList 高端订单列表
	 * @apiSuccess (highOrderList) {String} dealNo 客户订单号
	 * @apiSuccess (highOrderList) {String} disCode 分销代码
	 * @apiSuccess (highOrderList) {String} txAcctNo 客户号
	 * @apiSuccess (highOrderList) {String} cpAcctNo 资金账号
	 * @apiSuccess (highOrderList) {String} bankAcct  银行账号
	 * @apiSuccess (highOrderList) {String} bankCode  银行代码
	 * @apiSuccess (highOrderList) {String} paymentType  支付方式
	 * @apiSuccess (highOrderList) {String} productName 产品名称
	 * @apiSuccess (highOrderList) {String} productCode 产品代码
	 * @apiSuccess (highOrderList) {String} subProductCode 子产品代码
	 * @apiSuccess (highOrderList) {String} appAmt  申请金额
	 * @apiSuccess (highOrderList) {String} appVol 申请份额
	 * @apiSuccess (highOrderList) {String} ackAmt  确认金额
     * @apiSuccess (highOrderList) {String} ackVol  确认份额
     * @apiSuccess (highOrderList) {String} ackDt  确认日期
	 * @apiSuccess (highOrderList) {String} appDtm 申请日期时间
	 * @apiSuccess (highOrderList) {String} payStatus 付款状态<br>0-无需付款;1-未付款;2-付款中;3-部分成功;4-成功;5-失败
	 * @apiSuccess (highOrderList) {String} orderStatus 订单状态<br>1-申请成功;2-部分确认;3-确认成功;4-确认失败;5-自行撤销;6-强制取消
	 * @apiSuccess (highOrderList) {String} taTradeDt TA交易日期
	 * @apiSuccess (highOrderList) {String} redeemDirection 赎回去向
	 * @apiSuccess (highOrderList) {BigDecimal} fee 手续费
	 * @apiSuccess (highOrderList) {String} divMode 分红方式<br>0-红利再投;1-现金分红
	 * @apiSuccess (highOrderList) {String} mBusiCode 
	 *                                 中台业务码<br>1120-认购; 1122-申购; 1124-赎回; 1136-基金转换; 1236-基金转投; 1142-强赎; 1143-分红;1144-强增;1145-强减;1129-修改分红方式;1260-拉杆平衡;1261-波动平衡;1262-观点平衡
	 * @apiSuccess(highOrderList) {String} memo 备注
	 * @apiSuccess(highOrderList) {BigDecimal} nav 净值
	 * @apiSuccess(highOrderList) {String} submitTaDt 上报TA日期
	 * @apiSuccess(highOrderList) {String} isVolTansfer 是否份额结转 1是，0否
	 * @apiSuccess(highOrderList) {BigDecimal} transferPrice 转让价格
	 * @apiSuccess(highOrderList) {String} isNoTradeTransfer 是否非交易转让 1是，0否
	 * @apiSuccess(highOrderList) {List} SubDealOrderBeans 子订单列表
	 * @apiSuccess (SubDealOrderBean) {String} dealNo 客户订单号
	 * @apiSuccess (SubDealOrderBean) {String} disCode 分销代码
	 * @apiSuccess (SubDealOrderBean) {String} txAcctNo 客户号
	 * @apiSuccess (SubDealOrderBean) {String} cpAcctNo 资金账号
	 * @apiSuccess (SubDealOrderBean) {String} bankAcct  银行账号
	 * @apiSuccess (SubDealOrderBean) {String} bankCode  银行代码
	 * @apiSuccess (SubDealOrderBean) {String} paymentType  支付方式
	 * @apiSuccess (SubDealOrderBean) {String} productName 产品名称
	 * @apiSuccess (SubDealOrderBean) {String} productCode 产品代码
	 * @apiSuccess (SubDealOrderBean) {String} subProductCode 子产品代码
	 * @apiSuccess (SubDealOrderBean) {String} appAmt  申请金额
	 * @apiSuccess (SubDealOrderBean) {String} appVol 申请份额
	 * @apiSuccess (SubDealOrderBean) {String} ackAmt  确认金额
	 * @apiSuccess (SubDealOrderBean) {String} ackVol  确认份额
	 * @apiSuccess (SubDealOrderBean) {String} ackDt  确认日期
	 * @apiSuccess (SubDealOrderBean) {String} appDtm 申请日期时间
	 * @apiSuccess (SubDealOrderBean) {String} payStatus 付款状态<br>0-无需付款;1-未付款;2-付款中;3-部分成功;4-成功;5-失败
	 * @apiSuccess (SubDealOrderBean) {String} orderStatus 订单状态<br>1-申请成功;2-部分确认;3-确认成功;4-确认失败;5-自行撤销;6-强制取消
	 * @apiSuccess (SubDealOrderBean) {String} taTradeDt TA交易日期
	 * @apiSuccess (SubDealOrderBean) {String} redeemDirection 赎回去向
	 * @apiSuccess (SubDealOrderBean) {BigDecimal} fee 手续费
	 * @apiSuccess (SubDealOrderBean) {String} divMode 分红方式<br>0-红利再投;1-现金分红
	 * @apiSuccess (SubDealOrderBean) {String} mBusiCode
	 *                                 中台业务码<br>1120-认购; 1122-申购; 1124-赎回; 1136-基金转换; 1236-基金转投; 1142-强赎; 1143-分红;1144-强增;1145-强减;1129-修改分红方式;1260-拉杆平衡;1261-波动平衡;1262-观点平衡
	 * @apiSuccess(SubDealOrderBean) {String} memo 备注
	 * @apiSuccess(SubDealOrderBean) {BigDecimal} nav 净值
	 * @apiSuccess(SubDealOrderBean) {String} submitTaDt 上报TA日期
	 * @apiSuccess(SubDealOrderBean) {String} isVolTansfer 是否份额结转 1是，0否
	 * @apiSuccessExample {json} Response Example
	 * dubbo com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListResponse
	 * 
	 * {
	 * "highCanCancelDealOrders": [
	 *        {
	 *               "taTradeDt": "********",                                  
	 *               "dealNo": "1017041316512500002002155",                    
	 *               "productCode": "481001",                                  
	 *               "productName": "工银瑞信核心价值混合型证券投资基金",                       
	 *               "cpAcctNo": "****************",                           
	 *               "bankCode": "XXX",                           
	 *               "bankAcct": "XXXXXXXX",                           
	*                "appDtm": *************,                                  
	*                "orderStatus": "6",                                       
	*                "payStatus": "5",                                         
	*                "redeemDirection": "1",                                         
	*                "appVol": 5555,                                           
	*                "appAmt": 5555,  
	*                "ackVol": 5555,                                           
    *                "ackAmt": 5555,                                          
	*                "fee":0.1,
	*                "mBusiCode":"1122",
	*                "nav":1.0
	*        }
	*    ], 
	*    "returnCode": "Z0000000", 
	*    "totalCount": 0, 
	*    "pageNum":1,
	*    "totalPage": 0, 
	*    "txAcctNo": "**********"
	* 
	* }
	* 
	*/
	public QueryDealOrderListRequest() {
	    this.setTxCode(TxCodes.QUERY_HIGH_DEAL_ORDER_LIST);
		setDisCode(DisCodeEnum.HM.getCode());
	}
	
	/**
	 * 产品代码
	 */
    private String productCode;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 交易开始时间
     */
    private Date appBeginDtm;
    /**
     * 交易结束时间
     */
    private Date appEndDtm;
    /**
     * 资金账号
     */
    private String cpAcctNo;
    /**
     * 中台业务码
     */
    private String[] mBusiCodeArr;
    /**
     * 订单状态
     */
    private String[] orderStatusArr;
    
    /**
     * 基金代码或名称
     */
    private String fundNameOrCode;
	/**
	 * 分销机构代码列表
	 */
    private List<String> disCodeList;

	/**
	 * 订单号
	 */
	private String dealNo;
	/**
	 * 是否授权,1:是,0:否
	 */
	@Deprecated
	private String isAuth= YesOrNoEnum.YES.getCode();

	/**
	 * 不过滤香港产品,1:是,0:否
	 */
	private String notFilterHkFund;

	/**
	 * 不过滤好臻产品,1:是,0:否
	 */
	private String notFilterHzFund;

	public String getNotFilterHkFund() {
		return notFilterHkFund;
	}

	public void setNotFilterHkFund(String notFilterHkFund) {
		this.notFilterHkFund = notFilterHkFund;
	}

	public String getNotFilterHzFund() {
		return notFilterHzFund;
	}

	public void setNotFilterHzFund(String notFilterHzFund) {
		this.notFilterHzFund = notFilterHzFund;
	}

	public String getIsAuth() {
		return isAuth;
	}

	public void setIsAuth(String isAuth) {
		this.isAuth = isAuth;
	}

	public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public Date getAppBeginDtm() {
        return appBeginDtm;
    }

    public void setAppBeginDtm(Date appBeginDtm) {
        this.appBeginDtm = appBeginDtm;
    }

    public Date getAppEndDtm() {
        return appEndDtm;
    }

    public void setAppEndDtm(Date appEndDtm) {
        this.appEndDtm = appEndDtm;
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }

    public String[] getmBusiCodeArr() {
        return mBusiCodeArr;
    }

    public void setmBusiCodeArr(String[] mBusiCodeArr) {
        this.mBusiCodeArr = mBusiCodeArr;
    }

    public String[] getOrderStatusArr() {
        return orderStatusArr;
    }

    public void setOrderStatusArr(String[] orderStatusArr) {
        this.orderStatusArr = orderStatusArr;
    }

    public String getFundNameOrCode() {
        return fundNameOrCode;
    }

    public void setFundNameOrCode(String fundNameOrCode) {
        this.fundNameOrCode = fundNameOrCode;
    }

	public List<String> getDisCodeList() {
		return disCodeList;
	}

	public void setDisCodeList(List<String> disCodeList) {
		this.disCodeList = disCodeList;
	}

	public String getDealNo() {
		return dealNo;
	}

	public void setDealNo(String dealNo) {
		this.dealNo = dealNo;
	}

}
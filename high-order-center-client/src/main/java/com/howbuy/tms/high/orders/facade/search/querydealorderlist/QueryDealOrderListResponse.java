/**
 * Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.querydealorderlist;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;
import com.howbuy.tms.high.orders.facade.search.querydealorderlist.bean.SubDealOrderBean;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description:(查询订单列表)
 * @reason:
 * <AUTHOR>
 * @date 2017年3月31日 上午11:15:50
 * @since JDK 1.7
 */
public class QueryDealOrderListResponse extends OrderSearchBaseResponse {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = -3671618386893410819L;
    /**
     * 是否持有好臻产品 0:没有,1:有
     */
    private String hasHZProduct;
    /**
     * 是否持有好买香港产品  0:没有,1:有
     */
    private String hasHKProduct;
    /**
     * 高端订单列表
     */
    private List<DealOrderBean> dealOrderList;

    public List<DealOrderBean> getDealOrderList() {
        return dealOrderList;
    }

    public void setDealOrderList(List<DealOrderBean> dealOrderList) {
        this.dealOrderList = dealOrderList;
    }

    public String getHasHZProduct() {
        return hasHZProduct;
    }

    public void setHasHZProduct(String hasHZProduct) {
        this.hasHZProduct = hasHZProduct;
    }

    public String getHasHKProduct() {
        return hasHKProduct;
    }

    public void setHasHKProduct(String hasHKProduct) {
        this.hasHKProduct = hasHKProduct;
    }

    /**
     * @description:(订单bean)
     * @reason:
     * <AUTHOR>
     * @date 2017年7月12日 下午4:58:11
     * @since JDK 1.7
     */
    public static class DealOrderBean implements Serializable {

        /**
         * serialVersionUID:TODO（用一句话描述这个变量表示什么）
         *
         * @since Ver 1.1
         */

        private static final long serialVersionUID = 7757051939821034685L;
        /**
         * 客户订单号
         */
        private String dealNo;
        /**
         * 分销代码
         */
        private String disCode;
        /**
         * 交易账号
         */
        private String txAcctNo;
        /**
         * 资金账号
         */
        private String cpAcctNo;
        /**
         * 银行账号
         */
        private String bankAcct;
        /**
         * 银行代码
         */
        private String bankCode;
        /**
         * 银行代码
         */
        private String paymentType;
        /**
         * 产品名称
         */
        private String productName;
        /**
         * 产品简称
         */
        private String productAttr;
        /**
         * 产品代码
         */
        private String productCode;
        /**
         * 申请金额
         */
        private BigDecimal appAmt;
        /**
         * 申请份额
         */
        private BigDecimal appVol;
        /**
         * 申请日期时间
         */
        private Date appDtm;
        /**
         * 付款状态
         */
        private String payStatus;
        /**
         * 订单状态
         */
        private String orderStatus;
        /**
         * TA交易日期
         */
        private String taTradeDt;
        /**
         * 手续费
         */
        private BigDecimal fee;
        /**
         * 中台业务码
         */
        private String mBusiCode;

        /**
         * 业务类型中文描述
         */
        private String businessDesc;
        /**
         * 赎回去向
         */
        private String redeemDirection;
        /**
         * 销售类型: 1-直销; 2-代销
         */
        private String scaleType;
        /**
         * 分红方式
         */
        private String divMode;

        /**
         * 确认份额
         */
        private BigDecimal ackVol;

        /**
         * 确认金额
         */
        private BigDecimal ackAmt;
        /**
         * 确认日期
         */
        private String ackDt;
        /**
         * 备注字段
         */
        private String memo;
        /**
         * 净值
         */
        private BigDecimal nav;

        /**
         * 递延标识
         */
        private String advanceFlag;

        /**
         * 产品类型
         */
        private String productType;

        /**
         * 上报TA日期
         */
        private String submitTaDt;

        /**
         * 产品子类型
         */
        private String productSubType;
        /**
         * 是否份额结转 1是，0否
         */
        private String isVolTansfer;
        /**
         * 合并上报标识 1-合并上报
         */
        private String mergeSubmitFlag;
        /**
         * 币种
         */
        private String currency;
        /**
         * 是否私募定投 0-不是 1-是
         */
        private String highFundInvPlanFlag;
        /**
         * 好买香港代销标识: 0-否; 1-是
         */
        private String hkSaleFlag;

        /**
         * 是否分期成立（证券类有此标识）
         */
        private String sffqcl;

        /**
         * 子基金代码
         */
        private String subProductCode;

        /**
         * 参与日期
         */
        private String joinDt;
        /**
         * 顺延标识，0-否、1-是
         */
        private String continuanceFlag;
        /**
         * 拆单标识（淡水泉分期成立），0-否、1-是
         */
        private String stageFlag;
        /**
         * 确认标识
         */
        private String txAckFlag;
        /**
         * 转让价格
         */
        private BigDecimal transferPrice;
        /**
         * 是否非交易转让,0:不是,1:是
         */
        private String isNoTradeTransfer;

        /**
         * 转入基金代码
         */
        private String transferInFundCode;
        /**
         * 转入基金名称
         */
        private String transferInFundName;
        /**
         * 转入基金确认份额
         */
        private BigDecimal transferInAckVol;
        /**
         * 转入基金确认金额
         */
        private BigDecimal transferInAckAmt;
        /**
         * 转入基金母基金代码
         */
        private String transferInMainFundCode;
        /**
         * 转入基金净值日期yyyyMMdd
         */
        private String transferInNavDt;
        /**
         * 转入基金确认净值
         */
        private BigDecimal transferInAckNav;
        /**
         * 资金出款日期
         */
        private String payOutDt;

        /**
         * 认缴金额,只有代销有,直销的数据库没有同步该字段
         */
        private BigDecimal subsAmt;

        public void setSubsAmt(BigDecimal subsAmt) {
            this.subsAmt = subsAmt;
        }

        public BigDecimal getSubsAmt() {
            return subsAmt;
        }

        public String getBusinessDesc() {
            return businessDesc;
        }

        public void setBusinessDesc(String businessDesc) {
            this.businessDesc = businessDesc;
        }

        public String getTransferInFundCode() {
            return transferInFundCode;
        }

        public void setTransferInFundCode(String transferInFundCode) {
            this.transferInFundCode = transferInFundCode;
        }

        public String getTransferInFundName() {
            return transferInFundName;
        }

        public void setTransferInFundName(String transferInFundName) {
            this.transferInFundName = transferInFundName;
        }

        public BigDecimal getTransferInAckVol() {
            return transferInAckVol;
        }

        public void setTransferInAckVol(BigDecimal transferInAckVol) {
            this.transferInAckVol = transferInAckVol;
        }

        public BigDecimal getTransferInAckAmt() {
            return transferInAckAmt;
        }

        public void setTransferInAckAmt(BigDecimal transferInAckAmt) {
            this.transferInAckAmt = transferInAckAmt;
        }

        public String getTransferInMainFundCode() {
            return transferInMainFundCode;
        }

        public void setTransferInMainFundCode(String transferInMainFundCode) {
            this.transferInMainFundCode = transferInMainFundCode;
        }

        public String getTransferInNavDt() {
            return transferInNavDt;
        }

        public void setTransferInNavDt(String transferInNavDt) {
            this.transferInNavDt = transferInNavDt;
        }

        public BigDecimal getTransferInAckNav() {
            return transferInAckNav;
        }

        public void setTransferInAckNav(BigDecimal transferInAckNav) {
            this.transferInAckNav = transferInAckNav;
        }

        private List<SubDealOrderBean> subDealOrderBeans;

        public BigDecimal getTransferPrice() {
            return transferPrice;
        }

        public void setTransferPrice(BigDecimal transferPrice) {
            this.transferPrice = transferPrice;
        }

        public String getIsNoTradeTransfer() {
            return isNoTradeTransfer;
        }

        public void setIsNoTradeTransfer(String isNoTradeTransfer) {
            this.isNoTradeTransfer = isNoTradeTransfer;
        }

        public String getJoinDt() {
            return joinDt;
        }

        public void setJoinDt(String joinDt) {
            this.joinDt = joinDt;
        }

        public String getContinuanceFlag() {
            return continuanceFlag;
        }

        public void setContinuanceFlag(String continuanceFlag) {
            this.continuanceFlag = continuanceFlag;
        }

        public String getStageFlag() {
            return stageFlag;
        }

        public void setStageFlag(String stageFlag) {
            this.stageFlag = stageFlag;
        }

        public String getTxAckFlag() {
            return txAckFlag;
        }

        public void setTxAckFlag(String txAckFlag) {
            this.txAckFlag = txAckFlag;
        }

        public String getSffqcl() {
            return sffqcl;
        }

        public void setSffqcl(String sffqcl) {
            this.sffqcl = sffqcl;
        }

        public String getSubProductCode() {
            return subProductCode;
        }

        public void setSubProductCode(String subProductCode) {
            this.subProductCode = subProductCode;
        }

        public String getAckDt() {
            return ackDt;
        }

        public void setAckDt(String ackDt) {
            this.ackDt = ackDt;
        }

        public String getDivMode() {
            return divMode;
        }

        public void setDivMode(String divMode) {
            this.divMode = divMode;
        }

        public String getDisCode() {
            return disCode;
        }

        public void setDisCode(String disCode) {
            this.disCode = disCode;
        }

        public String getTxAcctNo() {
            return txAcctNo;
        }

        public void setTxAcctNo(String txAcctNo) {
            this.txAcctNo = txAcctNo;
        }

        public String getDealNo() {
            return dealNo;
        }

        public void setDealNo(String dealNo) {
            this.dealNo = dealNo;
        }

        public String getCpAcctNo() {
            return cpAcctNo;
        }

        public void setCpAcctNo(String cpAcctNo) {
            this.cpAcctNo = cpAcctNo;
        }

        public String getBankAcct() {
            return bankAcct;
        }

        public void setBankAcct(String bankAcct) {
            this.bankAcct = bankAcct;
        }

        public String getBankCode() {
            return bankCode;
        }

        public void setBankCode(String bankCode) {
            this.bankCode = bankCode;
        }

        public String getProductName() {
            return productName;
        }

        public void setProductName(String productName) {
            this.productName = productName;
        }

        public String getProductCode() {
            return productCode;
        }

        public void setProductCode(String productCode) {
            this.productCode = productCode;
        }

        public BigDecimal getAppAmt() {
            return appAmt;
        }

        public void setAppAmt(BigDecimal appAmt) {
            this.appAmt = appAmt;
        }

        public BigDecimal getAppVol() {
            return appVol;
        }

        public void setAppVol(BigDecimal appVol) {
            this.appVol = appVol;
        }

        public Date getAppDtm() {
            return appDtm;
        }

        public void setAppDtm(Date appDtm) {
            this.appDtm = appDtm;
        }

        public String getPayStatus() {
            return payStatus;
        }

        public void setPayStatus(String payStatus) {
            this.payStatus = payStatus;
        }

        public String getOrderStatus() {
            return orderStatus;
        }

        public void setOrderStatus(String orderStatus) {
            this.orderStatus = orderStatus;
        }

        public String getTaTradeDt() {
            return taTradeDt;
        }

        public void setTaTradeDt(String taTradeDt) {
            this.taTradeDt = taTradeDt;
        }

        public BigDecimal getFee() {
            return fee;
        }

        public void setFee(BigDecimal fee) {
            this.fee = fee;
        }

        public String getmBusiCode() {
            return mBusiCode;
        }

        public void setmBusiCode(String mBusiCode) {
            this.mBusiCode = mBusiCode;
        }

        public String getPaymentType() {
            return paymentType;
        }

        public void setPaymentType(String paymentType) {
            this.paymentType = paymentType;
        }

        public String getRedeemDirection() {
            return redeemDirection;
        }

        public void setRedeemDirection(String redeemDirection) {
            this.redeemDirection = redeemDirection;
        }

        public String getScaleType() {
            return scaleType;
        }

        public void setScaleType(String scaleType) {
            this.scaleType = scaleType;
        }

        public BigDecimal getAckVol() {
            return ackVol;
        }

        public void setAckVol(BigDecimal ackVol) {
            this.ackVol = ackVol;
        }

        public BigDecimal getAckAmt() {
            return ackAmt;
        }

        public void setAckAmt(BigDecimal ackAmt) {
            this.ackAmt = ackAmt;
        }

        public String getMemo() {
            return memo;
        }

        public void setMemo(String memo) {
            this.memo = memo;
        }

        public BigDecimal getNav() {
            return nav;
        }

        public void setNav(BigDecimal nav) {
            this.nav = nav;
        }

        public String getAdvanceFlag() {
            return advanceFlag;
        }

        public void setAdvanceFlag(String advanceFlag) {
            this.advanceFlag = advanceFlag;
        }

        public String getProductType() {
            return productType;
        }

        public void setProductType(String productType) {
            this.productType = productType;
        }

        public String getSubmitTaDt() {
            return submitTaDt;
        }

        public void setSubmitTaDt(String submitTaDt) {
            this.submitTaDt = submitTaDt;
        }

        public String getProductSubType() {
            return productSubType;
        }

        public void setProductSubType(String productSubType) {
            this.productSubType = productSubType;
        }

        public String getIsVolTansfer() {
            return isVolTansfer;
        }

        public void setIsVolTansfer(String isVolTansfer) {
            this.isVolTansfer = isVolTansfer;
        }

        public String getMergeSubmitFlag() {
            return mergeSubmitFlag;
        }

        public void setMergeSubmitFlag(String mergeSubmitFlag) {
            this.mergeSubmitFlag = mergeSubmitFlag;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }

        public String getHighFundInvPlanFlag() {
            return highFundInvPlanFlag;
        }

        public void setHighFundInvPlanFlag(String highFundInvPlanFlag) {
            this.highFundInvPlanFlag = highFundInvPlanFlag;
        }

        public String getHkSaleFlag() {
            return hkSaleFlag;
        }

        public void setHkSaleFlag(String hkSaleFlag) {
            this.hkSaleFlag = hkSaleFlag;
        }

        public String getProductAttr() {
            return productAttr;
        }

        public void setProductAttr(String productAttr) {
            this.productAttr = productAttr;
        }

        public List<SubDealOrderBean> getSubDealOrderBeans() {
            return subDealOrderBeans;
        }

        public void setSubDealOrderBeans(List<SubDealOrderBean> subDealOrderBeans) {
            this.subDealOrderBeans = subDealOrderBeans;
        }

        public String getPayOutDt() {
            return payOutDt;
        }

        public void setPayOutDt(String payOutDt) {
            this.payOutDt = payOutDt;
        }
    }

}
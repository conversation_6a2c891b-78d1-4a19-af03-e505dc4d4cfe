/**
 *Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.facade.search.querydealorderrefund;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.validate.MyValidation;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;

/**
 * @description:查询订单回款信息
 * @author: chuanguang.tang
 * @date: 2021/8/4 9:45
 * @since JDK 1.8
 */
public class QueryDealOrderRefundRequest extends OrderSearchBaseRequest {

	private static final long serialVersionUID = 247721317350139773L;
	
	public QueryDealOrderRefundRequest() {
	    this.setTxCode(TxCodes.QUERY_HIGH_DEAL_ORDER);
	}
	
    /**
     * 订单号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "订单号")
    private String dealNo;

    /**
     * 产品代码
     */
    private String productCode;
    /**
     * 业务类型
     */
    private String mBusiCode;
    /**
     * 上报开始日期
     */
    private String queryBeginDt;
    /**
     * 上报结束日期
     */
    private String queryEndDt;

    public String getDealNo() {
        return dealNo;
    }

    public void setDealNo(String dealNo) {
        this.dealNo = dealNo;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getmBusiCode() {
        return mBusiCode;
    }

    public void setmBusiCode(String mBusiCode) {
        this.mBusiCode = mBusiCode;
    }

    public String getQueryBeginDt() {
        return queryBeginDt;
    }

    public void setQueryBeginDt(String queryBeginDt) {
        this.queryBeginDt = queryBeginDt;
    }

    public String getQueryEndDt() {
        return queryEndDt;
    }

    public void setQueryEndDt(String queryEndDt) {
        this.queryEndDt = queryEndDt;
    }
}
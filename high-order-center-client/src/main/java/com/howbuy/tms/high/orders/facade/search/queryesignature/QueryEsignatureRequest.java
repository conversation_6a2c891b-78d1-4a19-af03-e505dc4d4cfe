/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */


package com.howbuy.tms.high.orders.facade.search.queryesignature;

import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;

/**
 * Description:查询电子签名接口请求参数
 *
 * @reason:
 * <AUTHOR>
 * @date 2017年4月13日 下午6:18:43
 * @since JDK 1.7
 */
public class QueryEsignatureRequest extends OrderSearchBaseRequest {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = 7792774317963053067L;

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryesignature.QueryEsignatureFacade.execute() 查询电子签名接口
     * @apiGroup high-order-center
     * @apiDescription 查询电子签名接口
     *
     * @apiUse orderBaseRequest
     * @apiUse orderSearchBaseRequest
     *
     * @apiParam {String} [signatureSid] 电子签名流水号
     * @apiParam {String} [productCode] 产品代码
     * @apiParam {String} [fundShareClass] 份额类型
     *                                      A-前收费；B-后收费
     * @apiParam {String} [dealNo] 订单号
     * @apiParam {String} [signatureStatus] 签名状态<br>1-未签;2-签订成功

     *
     * @apiParamExample {json} Request Example
     * dubbo com.howbuy.tms.high.orders.facade.search.queryesignature.QueryEsignatureRequest
     *{
     *    "appDt": "20170413",
     *    "appTm": "200954",
     *    "dataTrack": "892ab470-b749-4b2a-9857-018b410ddfb7",
     *    "disCode": "HB000A001",
     *    "operIp": "127.0.0.1",
     *    "outletCode": "A20150120",
     *    "pageNo": 1,
     *    "pageSize": 20,
     *    "txAcctNo": "1100875141",
     *    "txChannel": "4"
     *}
     *
     * @apiUse orderBaseResponse
     * @apiUse orderSearchBaseResponse
     *
     * @apiSuccess {EsignatureBean} esignatureBean 电子签名信息
     *
     * @apiSuccess (esignatureBean) {List} esignatureList 持仓信息列表
     *
     * @apiSuccess (esignatureList) {String} signatureSid 电子签名流水号
     * @apiSuccess (esignatureList) {String} txAcctNo 交易账号
     * @apiSuccess (esignatureList) {String} productCode 产品代码
     * @apiSuccess (esignatureList) {String}  productName 产品名称
     * @apiSuccess (esignatureList) {String} fundShareClass 份额类型
     *                                                      A-前收费；B-后收费
     * @apiSuccess (esignatureList) {Long} singnatureValue 签名结果值
     * @apiSuccess (esignatureList) {String} productType 产品类型
     * @apiSuccess (esignatureList) {String} productChannel 产品通道
     * @apiSuccess (esignatureList) {Date} createDtm 创建日期时间 
     * @apiSuccess (esignatureList) {Date} updateDtm 更新日期时间 
     * @apiSuccess (esignatureList) {String} payStatus 支付状态<br>0-无需付款;1-未付款;2-付款中;3-部分成功;4-成功;5-失败
     * @apiSuccess (esignatureList) {String} orderStatus 订单状态<br>1-申请成功;2-部分确认;3-确认成功;4-确认失败;5-自行撤销;6-强制取消
     * @apiSuccess (esignatureList) {String} contractType 合同类型 1购买合同；2补签协议合同
     * @apiSuccess (esignatureList) {List} agreementList 协议列表
     *
     * @apiSuccess (agreementList) {String} agreementCode 协议代码
     * @apiSuccess (agreementList) {String} agreementName 协议名称
     *
     * @apiSuccessExample {json} Response Example
     * dubbo com.howbuy.tms.high.orders.facade.search.queryesignature.QueryEsignatureResponse
     *{
     *    "description": "成功",
     *    "esignatureList": [
     *        {
     *            "createDtm": 1492084673972,
     *            "fundShareClass": "A",
     *            "productCode": "481008",
     *            "productName":"测试产品",
     *            "signatureSid": "9917041319575300002001141",
     *            "txAcctNo": "1100875141",
     *            "updateDtm": 1492085237903
     *        }
     *    ],
     *    "pageNo": 0,
     *    "returnCode": "Z0000000",
     *    "totalCount": 0,
     *    "totalPage": 0
     *}
     *
     */
    public QueryEsignatureRequest() {
        setTxCode(TxCodes.HIGH_FUND_QUERY_ESIGNATURE);
    }

    /**
     * 电子签名流水号
     */
    private String signatureSid;
    /**
     * 产品代码
     */
    private String productCode;
    /**
     * 份额类型
     */
    private String fundShareClass;
    /**
     * 中台订单号
     */
    private String dealNo;
    /**
     * 签名状态: 1-未签;2-签订成功
     */
    private String signatureStatus;

    public String getSignatureStatus() {
        return signatureStatus;
    }

    public void setSignatureStatus(String signatureStatus) {
        this.signatureStatus = signatureStatus;
    }

    public String getSignatureSid() {
        return signatureSid;
    }

    public void setSignatureSid(String signatureSid) {
        this.signatureSid = signatureSid;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getFundShareClass() {
        return fundShareClass;
    }

    public void setFundShareClass(String fundShareClass) {
        this.fundShareClass = fundShareClass;
    }

    public String getDealNo() {
        return dealNo;
    }

    public void setDealNo(String dealNo) {
        this.dealNo = dealNo;
    }

}


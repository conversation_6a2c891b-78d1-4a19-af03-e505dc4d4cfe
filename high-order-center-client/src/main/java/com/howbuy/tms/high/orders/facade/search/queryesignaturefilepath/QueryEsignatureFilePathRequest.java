/**
 * Copyright (c) 2020, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.facade.search.queryesignaturefilepath;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.validate.MyValidation;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;

/**
 * 请在此添加描述
 * <AUTHOR>
 * @date 2020/12/16 16:24
 * @since JDK 1.8
 */
public class QueryEsignatureFilePathRequest extends OrderSearchBaseRequest {

    private static final long serialVersionUID = 5030746092050446314L;

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryesignaturefilepath.QueryEsignatureFilePathFacade.execute() 查询客户电子签名文件路径
     * @apiGroup high-order-center
     * @apiDescription 查询客户电子签名文件路径
     *
     * @apiUse orderBaseRequest
     * @apiUse orderSearchBaseRequest
     *
     * @apiParam {String} signatureSid 电子签名流水号
     * @apiParam {String} agreementCode 协议代码
     * @apiParam {String} [taCode] ta代码(兼容老文件，拼接路径用)

     *
     * @apiParamExample {json} Request Example
     * dubbo com.howbuy.tms.high.orders.facade.search.queryesignaturefilepath.QueryEsignatureFilePathRequest
     *{ }
     *
     * @apiUse orderBaseResponse
     * @apiUse orderSearchBaseResponse
     *
     * @apiSuccess {String} filePath 文件路径
     *
     * @apiSuccessExample {json} Response Example
     * dubbo com.howbuy.tms.high.orders.facade.search.queryesignaturefilepath.QueryEsignatureFilePathResponse
     *{
     *    "description": "成功",
     *    "esignatureList": "",
     *    "pageNo": 0,
     *    "returnCode": "Z0000000",
     *    "totalCount": 0,
     *    "totalPage": 0
     *}
     *
     */
    public QueryEsignatureFilePathRequest() {
        setTxCode(TxCodes.HIGH_FUND_QUERY_ESIGNATURE_FILE_PATH);
    }

    /**
     * 电子签名流水号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "电子签名流水号", isRequired = true, max = 32)
    private String signatureSid;
    /**
     * 协议代码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "协议代码", isRequired = true, max = 20)
    private String agreementCode;
    /**
     * ta代码(兼容老文件，拼接路径用)
     */
    private String taCode;

    public String getSignatureSid() {
        return signatureSid;
    }

    public void setSignatureSid(String signatureSid) {
        this.signatureSid = signatureSid;
    }

    public String getAgreementCode() {
        return agreementCode;
    }

    public void setAgreementCode(String agreementCode) {
        this.agreementCode = agreementCode;
    }

    public String getTaCode() {
        return taCode;
    }

    public void setTaCode(String taCode) {
        this.taCode = taCode;
    }
}
package com.howbuy.tms.high.orders.facade.search.queryfeeinfo;

import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.validate.MyValidation;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description:查询费用信息入参
 * @Author: yun.lu
 * Date: 2023/12/27 14:51
 */
public class QueryFeeInfoFacadeRequest extends OrderSearchBaseRequest {
    /**
     * 基金代码
     */
    @MyValidation(fieldName = "基金代码", isRequired = true)
    private String fundCode;

    /**
     * 认缴金额
     */
    private BigDecimal subsAmt;

    /**
     * 实缴金额/净申请金额
     */
    private BigDecimal paidAmt;

    /**
     * 计算手续费方式,4:按认缴金额,5:按实缴金额
     */
    @Deprecated
    private String feeRateMethod;

    /**
     * 支付方式：01-自划款；04-银行卡代扣；06-储蓄罐支付
     */
    private String paymentType;


    /**
     * 是否首次实缴,1:是;0:不是
     */
    private String isFirstPay;


    /**
     * 预约订单号
     */
    private String appointmentDealNo;

    /**
     * OP手动输入的折扣
     */
    private BigDecimal discountRate;

    /**
     * 银行编码
     */
    private String bankCode;

    /**
     * 交易渠道
     */
    private String txChannelCode;

    /**
     * 中台交易编码
     */
    private String mBusinessCode;

    /**
     * 当前日期
     */
    private Date queryDate;

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public BigDecimal getSubsAmt() {
        return subsAmt;
    }

    public void setSubsAmt(BigDecimal subsAmt) {
        this.subsAmt = subsAmt;
    }

    public BigDecimal getPaidAmt() {
        return paidAmt;
    }

    public void setPaidAmt(BigDecimal paidAmt) {
        this.paidAmt = paidAmt;
    }

    public String getFeeRateMethod() {
        return feeRateMethod;
    }

    public void setFeeRateMethod(String feeRateMethod) {
        this.feeRateMethod = feeRateMethod;
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public String getIsFirstPay() {
        return isFirstPay;
    }

    public void setIsFirstPay(String isFirstPay) {
        this.isFirstPay = isFirstPay;
    }

    public String getAppointmentDealNo() {
        return appointmentDealNo;
    }

    public void setAppointmentDealNo(String appointmentDealNo) {
        this.appointmentDealNo = appointmentDealNo;
    }

    public BigDecimal getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(BigDecimal discountRate) {
        this.discountRate = discountRate;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getTxChannelCode() {
        return txChannelCode;
    }

    public void setTxChannelCode(String txChannelCode) {
        this.txChannelCode = txChannelCode;
    }

    public String getmBusinessCode() {
        return mBusinessCode;
    }

    public void setmBusinessCode(String mBusinessCode) {
        this.mBusinessCode = mBusinessCode;
    }

    public Date getQueryDate() {
        return queryDate;
    }

    public void setQueryDate(Date queryDate) {
        this.queryDate = queryDate;
    }

    public QueryFeeInfoFacadeRequest() {
        setTxCode(TxCodes.QUERY_FEE_INFO);
    }
}

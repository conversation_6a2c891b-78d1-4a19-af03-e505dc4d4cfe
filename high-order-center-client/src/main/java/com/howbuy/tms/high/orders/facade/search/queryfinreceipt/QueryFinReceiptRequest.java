/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */


package com.howbuy.tms.high.orders.facade.search.queryfinreceipt;

import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;

import java.util.List;

/**
 * @description:(查询资金到账提醒数据接口请求参数)
 * @reason:
 * <AUTHOR>
 * @date 2018年6月21日 下午4:55:44
 * @since JDK 1.7
 */
public class QueryFinReceiptRequest extends OrderSearchBaseRequest {

    private static final long serialVersionUID = -3882167711821963597L;

    public QueryFinReceiptRequest() {
        setTxCode(TxCodes.HIGH_FIN_RECEIPT);
        setDisCode(DisCodeEnum.HM.getCode());
    }

    /**
     * 交易账号
     */
    private String txAcctNo;

    /**
     * 分销机构代码列表
     */
    private List<String> disCodeList;
    /**
     * 是否授权
     */
    @Deprecated
    private String isAuth= YesOrNoEnum.YES.getCode();
    /**
     * 是否仅需香港产品
     */
    private String onlyHkProduct;

    public String getOnlyHkProduct() {
        return onlyHkProduct;
    }

    public void setOnlyHkProduct(String onlyHkProduct) {
        this.onlyHkProduct = onlyHkProduct;
    }

    /**
     * 不过滤香港产品,1:是,0:否
     */
    private String notFilterHkFund;

    /**
     * 不过滤好臻产品,1:是,0:否
     */
    private String notFilterHzFund;

    public String getNotFilterHkFund() {
        return notFilterHkFund;
    }

    public void setNotFilterHkFund(String notFilterHkFund) {
        this.notFilterHkFund = notFilterHkFund;
    }

    public String getNotFilterHzFund() {
        return notFilterHzFund;
    }

    public void setNotFilterHzFund(String notFilterHzFund) {
        this.notFilterHzFund = notFilterHzFund;
    }

    public String getIsAuth() {
        return isAuth;
    }

    public void setIsAuth(String isAuth) {
        this.isAuth = isAuth;
    }

    @Override
    public String getTxAcctNo() {
        return txAcctNo;
    }

    @Override
    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    public List<String> getDisCodeList() {
        return disCodeList;
    }

    public void setDisCodeList(List<String> disCodeList) {
        this.disCodeList = disCodeList;
    }
}


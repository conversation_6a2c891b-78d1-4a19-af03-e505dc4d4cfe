/**
 *Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.high.orders.facade.search.queryforcecancelorderlist;

import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;

/**
 * @description:(查询高端可强撤单列表) 
 * <AUTHOR>
 * @date 2017年7月11日 下午3:09:55
 * @since JDK 1.6
 */
public class QueryForceCancelOrderListRequest extends OrderSearchBaseRequest{
    
    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    
    private static final long serialVersionUID = 2344792007400426065L;
    
    private String taTradeDt;

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryforcecancelorderlist.QueryForceCancelOrderListFacade.execute() 查询高端可强撤订单列表接口
     * @apiGroup high-order-center
     * @apiDescription 查询高端可强撤订单列表
     * 
     * @apiUse orderBaseRequest
     * @apiUse orderSearchBaseRequest
     * 
     * @apiParam {String} txAcctNo 交易账号
     * @apiParam {String} disCode 分销机构
     * @apiParam {String} outletCode 网点号
     * @apiParam {String} txCode 中台交易代码
     * @apiParam {String} taTradeDt TA交易日期
     * 
     * @apiParamExample {json} Request Example
     * dubbo com.howbuy.tms.high.orders.facade.search.queryforcecancelorderlist.QueryForceCancelOrderListRequest
     *{
     *    "appDt": "20170413", 
     *    "appTm": "143122", 
     *    "dataTrack": "0c7b031f-7e1f-4d7c-b1de-3eaaa735db9c", 
     *    "disCode": "HB000A001", 
     *    "operIp": "127.0.0.1", 
     *    "outletCode": "A20150120", 
     *    "pageNo": 1, 
     *    "pageSize": 20, 
     *    "advanceFlag": "1",
     *    "description": "成功",
     *    "disCode": "HB000A001",
     *    "pageNo": 0,
     *    "returnCode": "Z0000000",
     *    "taTradeDt": "20170210",
     *    "totalCount": 0,
     *    "totalPage": 0,
     *    "txAcctNo": "1100875155",
     *    "txChannel": "4",
     *    "txCode": "Z330024"
     *}
     *
     * @apiUse orderBaseResponse
     * @apiUse orderSearchBaseResponse
     *
     * @apiSuccess {CancelOrderBean} cancelOrderBean 高端可撤订单
     * 
     * @apiSuccess (cancelOrderBean) {List}  cancelDealOrderList 高端订单列表
     * @apiSuccess (cancelDealOrderList) {String} dealNo 客户订单号
     * @apiSuccess (cancelDealOrderList) {String} txAcctNo 交易账号
     * @apiSuccess (cancelDealOrderList) {String} disCode 分销机构
     * @apiSuccess (cancelDealOrderList) {String} disTxAcctNo 分销交易账号
     * @apiSuccess (cancelDealOrderList) {String} outletCode  网点号
     * @apiSuccess (cancelDealOrderList) {String} cpAcctNo 资金账号
     * @apiSuccess (cancelDealOrderList) {String} subTxAcctNo 子交易账号
     * @apiSuccess (cancelDealOrderList) {String} bankAcct  银行账号
     * @apiSuccess (cancelDealOrderList) {String} bankCode  银行代码
     * @apiSuccess (cancelDealOrderList) {String} custName  客户姓名
     * @apiSuccess (cancelDealOrderList) {String} idNo  证件号码
     * @apiSuccess (cancelDealOrderList) {String} txCode 中台交易代码<br>Z310001-组合申购接口(买入);Z310002-组合赎回接口(卖出);Z310003-组合转投(拉杆平衡);Z310004-组合调仓(波动平衡);Z310005-分红方式修改;Z310006-强制赎回;Z310007-分红;Z310008-强增;Z310009-强减
     * @apiSuccess (cancelDealOrderList) {String} productName 产品名称
     * @apiSuccess (cancelDealOrderList) {String} productCode 产品代码
     * @apiSuccess (cancelDealOrderList) {String} protocolName  协议名称
     * @apiSuccess (cancelDealOrderList) {String} protocolNo  协议号
     * @apiSuccess (cancelDealOrderList) {String} paymentType 支付方式<br>01-自划款; 04-代扣款; 06-储蓄罐
     * @apiSuccess (cancelDealOrderList) {String} appAmt  申请金额
     * @apiSuccess (cancelDealOrderList) {String} appVol 申请份额
     * @apiSuccess (cancelDealOrderList) {String} appRatio 申请比例
     * @apiSuccess (cancelDealOrderList) {String} appDate 申请日期
     * @apiSuccess (cancelDealOrderList) {String} appTime  申请时间
     * @apiSuccess (cancelDealOrderList) {String} appDtm 申请日期时间
     * @apiSuccess (cancelDealOrderList) {String} updateDtm  更新日期时间
     * @apiSuccess (cancelDealOrderList) {String} payStatus 付款状态<br>0-无需付款;1-未付款;2-付款中;3-部分成功;4-成功;5-失败
     * @apiSuccess (cancelDealOrderList) {String} orderStatus 订单状态<br>1-申请成功;2-部分确认;3-确认成功;4-确认失败;5-自行撤销;6-强制取消
     * @apiSuccess (cancelDealOrderList) {String} txChannel 交易渠道
     * @apiSuccess (cancelDealOrderList) {String} invstType 投资者类型<br>0-机构;1-个人
     * @apiSuccess (cancelDealOrderList) {String} ipAddress IP地址
     * @apiSuccess (cancelDealOrderList) {String} dataTrack 数据跟踪
     * @apiSuccess (cancelDealOrderList) {String} taTradeDt TA交易日期
     * @apiSuccess (cancelDealOrderList) {String} protocolType 协议类型
     * @apiSuccess (cancelDealOrderList) {String} externalDealNo 外部订单号
     * @apiSuccess (cancelDealOrderList) {String} advanceFlag 预约标志<br>1-普通交易;2-预约交易
     * @apiSuccess (cancelDealOrderList) {String} dealType 订单类型<br>1-公募;2-高端;3-定期
     * @apiSuccess (cancelDealOrderList) {String} channelCode  渠道代码<br>101-自助渠道;102-预约渠道
     * @apiSuccess (cancelDealOrderList) fee 手续费
     * @apiSuccess (cancelDealOrderList) mBusiCode  中台业务码<br>1120-认购; 1122-申购; 1124-赎回; 1136-基金转换; 1236-基金转投; 1142-强赎; 1143-分红;1144-强增;1145-强减;1129-修改分红方式;1260-拉杆平衡;1261-波动平衡;1262-观点平衡
     * @apiSuccess (cancelDealOrderList) redeemDirection  赎回去向<br>0-银行卡;1-储蓄罐
     * @apiSuccessExample {json} Response Example
     * dubbo com.howbuy.tms.high.orders.facade.search.queryforcecancelorderlist.QueryForceCancelOrderListResponse
     * 
     * {
     * "highCanCancelDealOrders": [
     *        {
     *               "advanceFlag": "1",                                       
    *                "appAmt": 5555,                                           
    *                "appDate": "20170413",                                    
    *                "appDtm": 1492073481006,                                  
    *                "appTime": "045049",                                      
    *                "channelCode": "101",                                     
    *                "cpAcctNo": "2017020800761370",                           
    *                "custName": "米子晴",                                        
    *                "dealNo": "1017041316512500002002155",                    
    *                "dealType": "2",                                          
    *                "description": "成功",                                      
    *                "disCode": "HB000A001",                                   
    *                "disTxAcctNo": "1100875155HB000A001",                     
    *                "externalDealNo": "12123122131321554",                    
    *                "idNo": "321183198707210756",                             
    *                "idType": "0",                                            
    *                "invstType": "1",                                         
    *                "ipAddress": "***********",                               
    *                "orderStatus": "6",                                       
    *                "outletCode": "H20131104",                                
    *                "pageNo": 0,                                              
    *                "payStatus": "5",                                         
    *                "paymentType": "04",                                      
    *                "productCode": "481001",                                  
    *                "productName": "工银瑞信核心价值混合型证券投资基金",                       
    *                "protocolNo": "1517041316512500002001155",                
    *                "protocolType": "4",                                      
    *                "returnCode": "Z0000000",                                 
    *                "subTxAcctNo": "2017020800761370HB000A001",               
    *                "taTradeDt": "20170210",                                  
    *                "totalCount": 0,                                          
    *                "totalPage": 0,                                           
    *                "txAcctNo": "1100875155",                                 
    *                "txChannel": "4",                                         
    *                "txCode": "Z330008",                                      
    *                "updateDtm": 1492073593674,
    *                "fee":0.1,
    *                "mBusiCode":"1122",
    *                redeemDirection:"0"                              
    *        }
    *    ], 
    *    "returnCode": "Z0000000", 
    *    "totalCount": 0, 
    *    "pageNum":1,
    *    "totalPage": 0, 
    *    "txAcctNo": "1100875141"
    * 
    * }
    * 
    */
    public QueryForceCancelOrderListRequest(){
        super.setTxCode(TxCodes.QUERY_HIGH_CAN_FORCE_CANCEL_DEAL_ORDER_LIST);
    }

    public String getTaTradeDt() {
        return taTradeDt;
    }

    public void setTaTradeDt(String taTradeDt) {
        this.taTradeDt = taTradeDt;
    }
    
}


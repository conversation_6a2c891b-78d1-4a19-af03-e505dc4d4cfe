package com.howbuy.tms.high.orders.facade.search.queryfundackvolandamtinfo;

import com.howbuy.tms.high.orders.facade.common.BaseDto;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @description: 基金确认金额与确认份额信息
 * @author: yun.lu
 * @date: 2025/3/20 14:30
 * @since JDK 1.8
 */
@Getter
@Setter
public class FundAckVolAndAmtInfoDto extends BaseDto {

    /**
     * 基金代码
     */
    private String fundCode;


    /**
     * 中台业务类型
     */
    private String mBusiCode;

    /**
     * 业务类型中文
     */
    private String mBusiCodeStr;

    /**
     * 确认金额
     */
    private BigDecimal ackAmt;

    /**
     * 确认份额
     */
    private BigDecimal ackVol;
}
package com.howbuy.tms.high.orders.facade.search.queryfundackvolandamtinfo;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import com.howbuy.tms.high.orders.facade.enums.TxCodes;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @description: 查询基金确认金额与确认份额请求
 * @author: yun.lu
 * @date: 2025/3/20 14:30
 * @since JDK 1.8
 */
@Getter
@Setter
public class QueryFundAckVolAndAmtInfoRequest extends OrderSearchBaseRequest {

    private static final long serialVersionUID = 1L;

    public QueryFundAckVolAndAmtInfoRequest() {
        this.setTxCode(TxCodes.QUERY_FUND_ACK_VOL_AMT);
    }

    /**
     * 基金代码列表
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金代码列表", isRequired = true)
    private List<String> fundCodeList;

    /**
     * 确认开始日期
     * 格式：YYYYMMDD
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "确认开始日期", isRequired = true)
    private String ackStartDt;

    /**
     * 确认结束日期
     * 格式：YYYYMMDD
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "确认结束日期", isRequired = true)
    private String ackEndDt;
}
package com.howbuy.tms.high.orders.facade.search.queryfundackvolandamtinfo;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @description: 查询基金确认金额与确认份额响应
 * @author: yun.lu
 * @date: 2025/3/20 14:30
 * @since JDK 1.8
 */
@Getter
@Setter
public class QueryFundAckVolAndAmtInfoResponse extends OrderSearchBaseResponse {

    private static final long serialVersionUID = 1L;

    /**
     * 基金确认金额与确认份额信息列表
     */
    private List<FundAckVolAndAmtInfoDto> fundAckVolAndAmtInfoList;
}
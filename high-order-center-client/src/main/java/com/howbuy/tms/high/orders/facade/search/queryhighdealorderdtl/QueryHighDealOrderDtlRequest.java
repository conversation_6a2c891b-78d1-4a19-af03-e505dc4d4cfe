/**
 *Copyright (c) 2016, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.facade.search.queryhighdealorderdtl;


import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.validate.MyValidation;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;

/**
 * @description:(查询高端交易订单明细接口 request)
 * @reason:
 * <AUTHOR>
 * @date 2017年3月31日 上午11:31:11
 * @since JDK 1.7
 */



public class QueryHighDealOrderDtlRequest extends OrderSearchBaseRequest {

	private static final long serialVersionUID = 523721717350139773L;
	/**
	* @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryhighdealorderdtl.QueryHighDealOrderDtlFacade.execute() 查询高端交易订单明细接口
	* @apiGroup high-order-center
	* @apiDescription 查询高端交易订单明细接口
	*
	* @apiUse orderBaseRequest
	* @apiUse orderSearchBaseRequest
	*
	* @apiParam {String} dealNo 订单号
	* @apiParamExample {json} Request Example
	* dubbo com.howbuy.tms.high.orders.facade.search.queryhighdealorderdtl.QueryHighDealOrderDtlRequest
	*
	* @apiUse orderBaseResponse
	* @apiUse orderSearchBaseResponse
	*
	* @apiSuccess {String} dealNo 客户订单号
	* @apiSuccess {String} fundCode 基金代码
	* @apiSuccess {String} fundName 基金名称
	* @apiSuccess {String} fundType 基金类型
	* @apiSuccess {String} fundShareClass 基金份额类型
	* @apiSuccess {BigDecimal} appAmt 申请金额
	* @apiSuccess {BigDecimal} appVol 申请份额
	* @apiSuccess {String} redeemDirection 赎回去向<br>0-银行卡;1-储蓄罐
	* @apiSuccess {BigDecimal} discountRate 费用折扣率
	* @apiSuccess {String} txAppFlag 订单申请标记
	* @apiSuccess {String} fundDivMode 基金分红方式<br>0-红利再投;1-现金红利
	* @apiSuccess {String} mBusiCode 中台业务代码
	* @apiSuccess {BigDecimal} fee 手续费
	* @apiSuccess {BigDecimal} ackAmt 确认金额
	* @apiSuccess {BigDecimal} ackVol 确认份额
	* @apiSuccess {String} ackDt 确认日期
	* @apiSuccess {String} taTradeDt TA交易日期
	* @apiSuccess {String} memo 备注
	* @apiSuccess {BigDecimal} nav   净值
	* @apiSuccess {BigDecimal} appointmentDiscount 预约折扣
	* @apiSuccess {Date} payTime 付款时间
	* @apiSuccess {String} bankAcct 银行卡
	* @apiSuccess {String} bankCode 银行代码
    * @apiSuccess {String} bankName 银行名称
	* @apiSuccess {String} payStatus 付款状态<br>0-无需付款;1-未付款;2-付款中;3-部分成功;4-成功;5-失败
    * @apiSuccess {String} orderStatus 订单状态<br>1-申请成功;2-部分确认;3-确认成功;4-确认失败;5-自行撤销;6-强制取消
    * @apiSuccess {String}  confirmDt    确认时间
    * @apiSuccess {String} paymentReceiptDt  赎回到款日期
    * @apiSuccess {String}  pigReceiptDt 储蓄罐到款日期
    * @apiSuccess {String}  txCode 中台交易码
    * @apiSuccess {String}  appDt 申请日期
    * @apiSuccess {String}  appTm 申请时间
    * @apiSuccess {String}  paymentType 支付方式<br>01-自划款;04-代扣款;06-储蓄罐
    * @apiSuccess {BigDecimal}  esitmateFee 预估手续费
    * @apiSuccess {BigDecimal}  achievementPay 业绩报酬
    * @apiSuccess {String}  firstBuyFlag 首次购买标识<br>1-首次购买;2-追加购买
    * @apiSuccess {String}  contractNo 群济系统交易订单号
    * @apiSuccess {String}  scaleType  销售类型<br>1-直销;2-代销
    * @apiSuccess {String}  productChannel  产品通道<br>1-好买创新;2-创昱达;3-群济;4-好买储蓄罐;5-好买公募
    * @apiSuccess {Date} appDtm 下单时间
    * @apiSuccess {String} submitTaDt 上报TA日期
    * @apiSuccess {String} isVolTansfer 是否份额结转 1是，0否
	* @apiSuccessExample {json} Response Example
	* dubbo com.howbuy.tms.high.orders.facade.search.queryhighdealorderdtl.QueryHighDealOrderDtlResponse
	*
	* {
	*    "appAmt": 5555,
	*    "appVol": 0,
	*    "dealNo": "1017041316512500002002155",
	*    "description": "成功",
	*    "discountRate": 0.4,
	*    "fundCode": "481001",
	*    "fundName": "工银瑞信核心价值混合型证券投资基金",
	*    "fundShareClass": "A",
	*    "fundType": "7",
	*    "mBusiCode": "1122",
	*    "pageNo": 0,
	*    "returnCode": "Z0000000",
	*    "taTradeDt": "20170210",
	*    "totalCount": 0,
	*    "totalPage": 0,
	*    "txAppFlag": "1",
	*    "payTime": 1492752311700,
	*    "confirmDt":"20170421",
	*    "paymentReceiptDt":"20170421",
	*    "pigReceiptDt":"20170421",
	*    "appDt":"20170421",
	*    "appTm":"20170421",
	*     "paymentType":"01"
	* }
	*
	*/
	public QueryHighDealOrderDtlRequest() {
	    this.setTxCode(TxCodes.QUERY_HIGH_DEAL_ORDER_DTL);
	}
	
    /**
     * 订单号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "订单号", isRequired = true)
    private String dealNo;

    public String getDealNo() {
        return dealNo;
    }

    public void setDealNo(String dealNo) {
        this.dealNo = dealNo;
    }
    
	
}
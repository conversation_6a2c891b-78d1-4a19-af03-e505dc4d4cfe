/**
 * Copyright (c) 2021, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.facade.search.queryhighfundinvplan;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.validate.MyValidation;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;

/**
 * @description: (查询定投信息请求)
 * <AUTHOR>
 * @date 2021/11/9 13:45
 * @since JDK 1.8
 */
public class QueryHighFundInvPlanRequest extends OrderSearchBaseRequest {

    private static final long serialVersionUID = -3465973458229555630L;

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryhighfundinvplan.QueryHighFundInvPlanFacade.execute() 定投信息查询
     * @apiGroup high-order-center
     * @apiDescription 定投信息查询请求
     *
     * @apiParam {String} planId 定投合约单号
     *
     * @apiUse orderBaseRequest
     * @apiUse orderSearchBaseRequest
     *
     * @apiParamExample {json} Request Example
     * dubbo com.howbuy.tms.high.orders.facade.search.queryhighfundinvplan.QueryHighFundInvPlanRequest
     *
     * @apiUse orderBaseResponse
     * @apiUse orderSearchBaseResponse
     *
     * @apiSuccess {String} fundCode 一账通号
     * @apiSuccess {String} txAcctNo 客户号
     * @apiSuccess {String} custName 客户姓名
     * @apiSuccess {String} idType 证件类型
     * @apiSuccess {String} idNo 证件号
     * @apiSuccess {String} fundCode 基金代码
     * @apiSuccess {String} fundAttr 基金简称
     * @apiSuccess {String} planPaymentType 支付方式
     * @apiSuccess {String} cpAcctNo 资金账号
     * @apiSuccess {String} bankAcct 银行账号
     * @apiSuccess {String} planRate 定投周期
     * @apiSuccess {Integer} planTotalNum 定投期数
     * @apiSuccess {BigDecimal} planAmount 定投金额
     * @apiSuccess {BigDecimal} planTotalAmount 定投总金额
     * @apiSuccess {BigDecimal} planDiscount 定投折扣率
     * @apiSuccess {String} planStartDate 合约生效日期
     * @apiSuccess {String} planMainStatus 定投合约状态
     *
     * @apiSuccessExample {json} Response Example
     * dubbo com.howbuy.tms.high.orders.facade.search.queryhighfundinvplan.QueryHighFundInvPlanResponse
     **/

    /**
     * 定投合约单号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "定投合约单号", isRequired = false)
    private String planId;


    public QueryHighFundInvPlanRequest() {
        this.setTxCode(TxCodes.QUERY_HIGH_FUND_INV_PLAN);
    }

    public String getPlanId() {
        return planId;
    }

    public void setPlanId(String planId) {
        this.planId = planId;
    }

}
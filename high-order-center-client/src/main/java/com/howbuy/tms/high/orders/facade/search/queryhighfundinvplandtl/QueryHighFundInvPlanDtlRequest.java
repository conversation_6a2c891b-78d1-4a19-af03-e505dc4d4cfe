/**
 * Copyright (c) 2021, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.facade.search.queryhighfundinvplandtl;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.validate.MyValidation;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;

/**
 * @description: (定投计划明细查询请求)
 * <AUTHOR>
 * @date 2021/11/9 15:08
 * @since JDK 1.8
 */
public class QueryHighFundInvPlanDtlRequest extends OrderSearchBaseRequest {

    private static final long serialVersionUID = 6769731044969115115L;

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryhighfundinvplandtl.QueryHighFundInvPlanDtlFacade.execute() 定投计划明细查询
     * @apiGroup high-order-center
     * @apiDescription 定投计划明细查询请求
     *
     * @apiParam {String} planId 定投合约单号
     *
     * @apiUse orderBaseRequest
     * @apiUse orderSearchBaseRequest
     *
     * @apiParamExample {json} Request Example
     * dubbo com.howbuy.tms.high.orders.facade.search.queryhighfundinvplandtl.QueryHighFundInvPlanDtlRequest
     *
     * @apiUse orderBaseResponse
     * @apiUse orderSearchBaseResponse
     *
     * @apiSuccess {List} highFundInvPlanDtlVoList 计划明细列表
     * @apiSuccess (highFundInvPlanDtlVoList) {String} planId 定投编号
     * @apiSuccess (highFundInvPlanDtlVoList) {String} planNum 期数
     * @apiSuccess (highFundInvPlanDtlVoList) {String} paymentDate 扣款日期
     * @apiSuccess (highFundInvPlanDtlVoList) {String} planAmount 定投金额
     * @apiSuccess (highFundInvPlanDtlVoList) {String} planFee 定投手续费
     * @apiSuccess (highFundInvPlanDtlVoList) {String} planStatus 执行状态
     * @apiSuccess (highFundInvPlanDtlVoList) {String} cpAcctNo 资金账号
     * @apiSuccess (highFundInvPlanDtlVoList) {String} bankAcct 银行卡号
     * @apiSuccess (highFundInvPlanDtlVoList) {String} execTime 执行时间  YYYYMMDDHHMMSS
     * @apiSuccess (highFundInvPlanDtlVoList) {String} memo 备注
     *
     * @apiSuccessExample {json} Response Example
     * dubbo com.howbuy.tms.high.orders.facade.search.queryhighfundinvplan.QueryHighFundInvPlanDtlResponse
     **/
    /**
     * 定投合约单号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "定投合约单号", isRequired = false)
    private String planId;

    public QueryHighFundInvPlanDtlRequest() {
        this.setTxCode(TxCodes.QUERY_HIGH_FUND_INV_PLAN_DTL);
    }

    public String getPlanId() {
        return planId;
    }

    public void setPlanId(String planId) {
        this.planId = planId;
    }
}
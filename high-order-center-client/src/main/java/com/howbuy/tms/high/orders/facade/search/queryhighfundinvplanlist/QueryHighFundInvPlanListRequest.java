/**
 * Copyright (c) 2021, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.facade.search.queryhighfundinvplanlist;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.validate.MyValidation;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;

/**
 * @description: (查询定投信息请求)
 * <AUTHOR>
 * @date 2021/11/9 13:45
 * @since JDK 1.8
 */
public class QueryHighFundInvPlanListRequest extends OrderSearchBaseRequest {

    private static final long serialVersionUID = -3465973458229555630L;

    /**
     * 定投合约单号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "定投合约单号", isRequired = false)
    private String planId;
    /**
     * 产品代码
     */
    private String fundCode;

    public QueryHighFundInvPlanListRequest() {
        this.setTxCode(TxCodes.QUERY_HIGH_FUND_INV_PLAN);
    }

    public String getPlanId() {
        return planId;
    }

    public void setPlanId(String planId) {
        this.planId = planId;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }
}
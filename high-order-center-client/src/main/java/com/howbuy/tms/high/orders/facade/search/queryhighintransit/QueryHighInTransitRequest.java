/**
 *Copyright (c) 2016, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.queryhighintransit;

import com.howbuy.tms.common.client.DefaultParamsConstant;
import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;

import java.util.List;

/**
 * @description:查询是否存在在途交易
 * @reason:查询是否存在在途交易
 */

/**
 * @api {dubbo} com.howbuy.tms.high.orders.facade.search.queryhighintransit.QueryHighInTransitFacade.execute() 查询是否存在在途交易接口
 * @apiGroup high-order-center-search
 * @apiDescription 查询是否存在在途交易接口
 * 
 * 
 * @apiUse orderSearchBaseRequest
 * @apiParam {String} cpAcctNo 资金账号
 * @apiParam {String} searchDisCode 查询分销机构(用于查询在途交易)

 * 
 * @apiUse orderSearchBaseResponse
 * @apiSuccess {String} isUnfinlish 是否存在在途交易:0-否,1-是
 */
public class QueryHighInTransitRequest extends OrderSearchBaseRequest {

    private static final long serialVersionUID = -1413164511760991063L;

    public QueryHighInTransitRequest() {
        super.setDisCode(DefaultParamsConstant.DEFULT_DIS_CODE);
        super.setOutletCode(DefaultParamsConstant.DEFAULT_OUTLET_CODE);
        super.setOperIp(DefaultParamsConstant.DEFAULT_OPER_IP);
        super.setTxChannel(DefaultParamsConstant.DEFAULT_TRADE_CHANNEL);
        this.setTxCode(TxCodes.QUERY_HIGH_INTRANSIT);
    }

    /**
     * 资金账号
     */
    private String cpAcctNo;
    
    
    /**
     * 资金账号列表
     */
    private List<String> cpAcctNos;

    /**
     * 查询分销机构（用于查询在途交易）
     */
    private String searchDisCode;

    public String getSearchDisCode() {
        return searchDisCode;
    }

    public void setSearchDisCode(String searchDisCode) {
        this.searchDisCode = searchDisCode;
    }

    public List<String> getCpAcctNos() {
        return cpAcctNos;
    }

    public void setCpAcctNos(List<String> cpAcctNos) {
        this.cpAcctNos = cpAcctNos;
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }


}
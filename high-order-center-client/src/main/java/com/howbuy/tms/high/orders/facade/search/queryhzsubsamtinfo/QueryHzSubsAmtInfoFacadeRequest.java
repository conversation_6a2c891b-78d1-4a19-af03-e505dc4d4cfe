package com.howbuy.tms.high.orders.facade.search.queryhzsubsamtinfo;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.validate.MyValidation;
import com.howbuy.tms.high.orders.facade.common.OrderBaseRequest;
import lombok.Data;

/**
 * @Description:查询好臻认缴金额信息接口入参
 * @Author: yun.lu
 * Date: 2024/3/7 15:15
 */
@Data
public class QueryHzSubsAmtInfoFacadeRequest extends OrderBaseRequest {

    /**
     * 一账通
     */
    private String hboneNo;


    /**
     * 交易账号
     */
    private String txAcctNo;

    /**
     * 产品编码
     */
    @MyValidation(validatorType= ValidatorTypeEnum.String, fieldName = "产品编码", isRequired = true, max=16)
    private String fundCode;



    public QueryHzSubsAmtInfoFacadeRequest() {
        setTxCode(TxCodes.QUERY_SUBSAMT_INFO);
        setDisCode(DisCodeEnum.HZ.getCode());
    }
}

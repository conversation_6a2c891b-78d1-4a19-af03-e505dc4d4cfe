package com.howbuy.tms.high.orders.facade.search.queryhzsubscribeamtinfo;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.validate.MyValidation;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import lombok.Data;

/**
 * @Description:查询认缴金额请求入参
 * @Author: yun.lu
 * Date: 2023/12/25 14:31
 */
@Data
public class QueryHzSubscribeAmtInfoRequest extends OrderSearchBaseRequest {
    /**
     * 产品编码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "产品编码", isRequired = true)
    private String fundCode;

    public QueryHzSubscribeAmtInfoRequest(){
        setTxCode(TxCodes.QUERY_HZ_SUBSCRIBE_AMT_INFO);
        setDisCode(DisCodeEnum.HZ.getCode());
    }
}

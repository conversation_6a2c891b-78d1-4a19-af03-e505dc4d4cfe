/**
 * Copyright (c) 2021, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.facade.search.queryinvplancustinfoforcrm;

import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;

/**
 * @description: (定投第一步，查询客户银行卡信息和持仓信息请求)
 * <AUTHOR>
 * @date 2021/11/15 14:32
 * @since JDK 1.8
 */
public class QueryInvPlanCustInfoForCrmRequest extends OrderSearchBaseRequest {

    private static final long serialVersionUID = -661854612373986246L;

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryinvplancustinfoforcrm.QueryInvPlanCustInfoForCrmFacade.execute() 定投第一步，查询客户银行卡信息和持仓信息请求
     * @apiGroup high-order-center
     * @apiDescription 定投第一步，查询客户银行卡信息和持仓信息请求
     *
     * @apiUse orderBaseRequest
     * @apiUse orderSearchBaseRequest
     *
     * @apiParamExample {json} Request Example
     * dubbo com.howbuy.tms.high.orders.facade.search.queryinvplancustinfoforcrm.QueryInvPlanCustInfoForCrmRequest
     *
     * @apiUse orderBaseResponse
     * @apiUse orderSearchBaseResponse
     *
     * @apiSuccess {List} balanceVolDtlPlanBeanList 持仓份额明细列表
     * @apiSuccess (balanceVolDtlPlanBeanList) {String} fundCode 基金代码
     * @apiSuccess (balanceVolDtlPlanBeanList) {String} fundAttr 基金简称
     * @apiSuccess (balanceVolDtlPlanBeanList) {String} fundEndDt 基金结束日期
     * @apiSuccess (balanceVolDtlPlanBeanList) {BigDecimal} minAddPurchaseAmt 追加净金额下限
     * @apiSuccess (balanceVolDtlPlanBeanList) {Integer} prodDiffer 产品追加极差
     * @apiSuccess (balanceVolDtlPlanBeanList) {String} supportCardType 是否支持多卡  1-TA多卡 2-产品单卡 3-TA单卡
     * @apiSuccess (balanceVolDtlPlanBeanList) {List} cpAcctNoList 资金账户列表
     *
     * @apiSuccess {List} bankCardInfoBeanList 银行卡列表
     * @apiSuccess (bankCardInfoBeanList) {String} cpAcctNo 资金账号
     * @apiSuccess (bankCardInfoBeanList) {String} bankCode 银行编号
     * @apiSuccess (bankCardInfoBeanList) {String} bankAcctDigest 银行账号摘要
     * @apiSuccess (bankCardInfoBeanList) {String} bankAcctMask 银行账号掩码
     * @apiSuccess (bankCardInfoBeanList) {String} bankAcct 银行账号
     * @apiSuccess (bankCardInfoBeanList) {String} bankAcctName 银行账户名称
     * @apiSuccess (bankCardInfoBeanList) {String} bankRegionName 分行名称
     * @apiSuccess (bankCardInfoBeanList) {String} bankAcctStatus 银行账户状态 0:正常;1:待审核;2:注销;3:冻结;4:销户待确认;5:冻结待确认
     * @apiSuccess (bankCardInfoBeanList) {String} vrfyCardStat 卡验证状态 2-验证通过，3-验证失败
     *
     * @apiSuccessExample {json} Response Example
     * dubbo com.howbuy.tms.high.orders.facade.search.queryinvplancustinfoforcrm.QueryInvPlanCustInfoForCrmResponse
     **/
    public QueryInvPlanCustInfoForCrmRequest() {
        setTxCode(TxCodes.QUERY_INV_PLAN_CUST_INFO_FOR_CRM);
    }

}
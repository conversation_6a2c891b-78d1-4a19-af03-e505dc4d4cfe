/**
 *Copyright (c) 2018, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.querylatelyprebook;

import java.util.List;

import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;

/**
 * @description:(查询最近一条可使用预约单)
 * @reason:
 * <AUTHOR>
 * @date 2018年1月4日 下午7:41:13
 * @since JDK 1.6
 */


/**
 * @api {post} dubbo:com.howbuy.tms.high.orders.facade.search.querylatelyprebook.QueryLatelyPreBookFacade.execute() 查询最近一条可使用预约单
 * @apiGroup high-order-center
 * @apiDescription 查询最近一条可使用预约单
 * 
 * @apiUse orderBaseRequest
 * @apiUse orderSearchBaseRequest
 * 
 * @apiParam {List} [preType] 预约类型  <br> 1-纸质成单2-电子成单3-无纸化交易
 * @apiParam {List} [tradeType] 交易类型  <br> 1-购买 2-追加 3-赎回
 * @apiParam {List} [fundCode] 产品代码
 * @apiParam {String} [preBookState] 预约单状态 <br> 1-未确认2-已确认4-已撤销
 * @apiParam {String} [noPaperState] 无纸化预约单状态 <br> 1-未确认2-已确认3-驳回
 * @apiParam {String} useFlag 使用标识 <br> 1-未使用 2-已使用 
 *
 * 
 * @apiParamExample {json} Request Example
 * dubbo :com.howbuy.tms.high.orders.facade.search.querylatelyprebook.QueryLatelyPreBookRequest
 *{
 *    "appDt": "20170413", 
 *    "appTm": "143122", 
 *    "dataTrack": "0c7b031f-7e1f-4d7c-b1de-3eaaa735db9c", 
 *    "disCode": "HB000A001", 
 *    "operIp": "127.0.0.1", 
 *    "outletCode": "A20150120", 
 *    "pageNo": 1, 
 *    "pageSize": 20
 *}
 * 
 * @apiSuccess  {String} preId 预约ID
 * @apiSuccess  {String} hboneNo 一帐通号
 * @apiSuccess  {String} custNo 客户号
 * @apiSuccess  {String} custName 客户姓名
 * @apiSuccess  {String} fundCode 产品代码
 * @apiSuccess  {String} fundName 产品名称
 * @apiSuccess  {String} tradeType 交易类型   <br>1-购买 2-追加 3-赎回
 * @apiSuccess  {String} idType  证件类型
 * @apiSuccess  {String} idNo 证件号
 * @apiSuccess  {String} prebookState 预约单状态<br>1-未确认；2-已确认；4-已撤销
 * @apiSuccess  {String} nopaperState 无纸化预约单状态<br> 1-未确认； 2-已确认 3-驳回
 * @apiSuccess  {String} ackAmt 预约金额
 * @apiSuccess  {String} sellVol 预约份额
 * @apiSuccess  {String} fee 手续费
 * @apiSuccess  {String} discountfee 无折扣手续费
 * @apiSuccess  {String} creDt 预约日期
 * @apiSuccessExample {json} Response Example
 * dubbo com.howbuy.tms.high.orders.facade.search.querylatelyprebook.QueryLatelyPreBookResponse
*{
*    "preBookList": [
*        {
*            
*        }
*    ], 
*    "returnCode": "Z0000000", 
*    "totalCount": 0, 
*    "totalPage": 0, 
*    "txAcctNo": "1100875141"
*}
 * 
 */
public class QueryLatelyPreBookRequest extends OrderSearchBaseRequest {

    /**
     * serialVersionUID:
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = 9021827451939768703L;

    /**
     * 产品代码
     */
    private String fundCode;

    /**
     * 成单方式 1-纸质成单； 2-电子成单； 3-无纸化交易
     */
    private List<String> preType;

    /**
     * 交易类型 交易类型 1-购买 2-追加 3-赎回
     */
    private List<String> tradeType;

    /**
     * 预约状态 -未确认；2-已确认；4-已撤销
     */
    private List<String> prebookState;
    
    /**
     * 使用标识 1-未使用 2-已使用
     * 
     */
    private String useFlag;
    
    public QueryLatelyPreBookRequest() {
        super.setTxCode(TxCodes.QUERY_LATELY_PRE_BOOK);
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public List<String> getPreType() {
        return preType;
    }

    public void setPreType(List<String> preType) {
        this.preType = preType;
    }

    public List<String> getTradeType() {
        return tradeType;
    }

    public void setTradeType(List<String> tradeType) {
        this.tradeType = tradeType;
    }

    public List<String> getPrebookState() {
        return prebookState;
    }

    public void setPrebookState(List<String> prebookState) {
        this.prebookState = prebookState;
    }

    public String getUseFlag() {
        return useFlag;
    }

    public void setUseFlag(String useFlag) {
        this.useFlag = useFlag;
    }

}

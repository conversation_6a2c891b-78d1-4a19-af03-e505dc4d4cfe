/**
 *Copyright (c) 2018, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.high.orders.facade.search.querylatelyprebook;

import java.math.BigDecimal;
import java.util.Date;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;

/**
 * @description:(查询最近可使用预约单响应) 
 * @reason:
 * <AUTHOR>
 * @date 2018年1月4日 下午7:45:34
 * @since JDK 1.6
 */
public class QueryLatelyPreBookResponse extends OrderSearchBaseResponse{

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    
    private static final long serialVersionUID = -8787079826313070926L;
    
    /**
     * 预约单标识，CRM唯一
     */
    private String preId;
    /**
     * 一帐通号
     */
    private String hboneNo;
    
    /**
     * 客户号
     */
    private String custNo;
    /**
     * 姓名
     */
    private String custName;
    /**
     * 产品代码
     */
    private String fundCode;
    /**
     * 产品名称
     */
    private String fundName;
    
    /**
     * 交易类型  1-购买 2-追加 3-赎回
     */
    private String tradeType;
    /**
     * 证件类型
     */
    private String idType;
    /**
     * 证件号
     */
    private String idNo;
    /**
     * 预约单状态:1-未确认；2-已确认；4-已撤销
     */
    private String prebookState;
    /**
     * 无纸化预约单状态
     * 1-未确认；
     * 2-已确认；
     *3-驳回
     */
    private String nopaperState;
    /**
     * 预约金额
     */
    private BigDecimal ackAmt;
    /**
     * 预约份额
     */
    private BigDecimal sellVol;
    /**
     * 手续费
     */
    private BigDecimal fee;
    /**
     * 预约日期
     */
    private String creDt;
    
    /**
     * 预约折扣
     */
    private BigDecimal disCount;
    
    /**
     * 费率
     */
    private BigDecimal feeRate;

    /**
     * 需双录标识：0-无需双录；1-需双录;
     */
    private String doubleNeedFlag;

    /**
     * 处理标识：0-无需处理；1-未处理；2-已处理
     */
    private String doubleHandleFlag;

    /**
     * 双录处理时间，带时间yyyyMMddHH24miss
     */
    private Date doubleHandleDt;

    /**
     * 是否首次实缴预约 0-否 1-是
     */
    private String firstPreId;

    /**
     * 折扣类型：1-使用折扣率，2-使用折扣金额
     * {@link com.howbuy.crm.base.discount.DisCountUseTypeEnum}
     */
    private String discountUseType;

    /**
     * 折扣金额
     */
    private BigDecimal discountAmt;


    public String getDiscountUseType() {
        return discountUseType;
    }

    public void setDiscountUseType(String discountUseType) {
        this.discountUseType = discountUseType;
    }

    public BigDecimal getDiscountAmt() {
        return discountAmt;
    }

    public void setDiscountAmt(BigDecimal discountAmt) {
        this.discountAmt = discountAmt;
    }

    public String getPreId() {
        return preId;
    }
    public void setPreId(String preId) {
        this.preId = preId;
    }
    public String getHboneNo() {
        return hboneNo;
    }
    public void setHboneNo(String hboneNo) {
        this.hboneNo = hboneNo;
    }
    public String getCustNo() {
        return custNo;
    }
    public void setCustNo(String custNo) {
        this.custNo = custNo;
    }
    public String getCustName() {
        return custName;
    }
    public void setCustName(String custName) {
        this.custName = custName;
    }
    public String getFundCode() {
        return fundCode;
    }
    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }
    public String getFundName() {
        return fundName;
    }
    public void setFundName(String fundName) {
        this.fundName = fundName;
    }
    public String getTradeType() {
        return tradeType;
    }
    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }
    public String getIdType() {
        return idType;
    }
    public void setIdType(String idType) {
        this.idType = idType;
    }
    public String getIdNo() {
        return idNo;
    }
    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }
    public String getPrebookState() {
        return prebookState;
    }
    public void setPrebookState(String prebookState) {
        this.prebookState = prebookState;
    }
    public String getNopaperState() {
        return nopaperState;
    }
    public void setNopaperState(String nopaperState) {
        this.nopaperState = nopaperState;
    }
    public BigDecimal getAckAmt() {
        return ackAmt;
    }
    public void setAckAmt(BigDecimal ackAmt) {
        this.ackAmt = ackAmt;
    }
    public BigDecimal getSellVol() {
        return sellVol;
    }
    public void setSellVol(BigDecimal sellVol) {
        this.sellVol = sellVol;
    }
    public BigDecimal getFee() {
        return fee;
    }
    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }
    public String getCreDt() {
        return creDt;
    }
    public void setCreDt(String creDt) {
        this.creDt = creDt;
    }
    public BigDecimal getDisCount() {
        return disCount;
    }
    public void setDisCount(BigDecimal disCount) {
        this.disCount = disCount;
    }
    public BigDecimal getFeeRate() {
        return feeRate;
    }
    public void setFeeRate(BigDecimal feeRate) {
        this.feeRate = feeRate;
    }

    public String getDoubleNeedFlag() {
        return doubleNeedFlag;
    }

    public void setDoubleNeedFlag(String doubleNeedFlag) {
        this.doubleNeedFlag = doubleNeedFlag;
    }

    public String getDoubleHandleFlag() {
        return doubleHandleFlag;
    }

    public void setDoubleHandleFlag(String doubleHandleFlag) {
        this.doubleHandleFlag = doubleHandleFlag;
    }

    public Date getDoubleHandleDt() {
        return doubleHandleDt;
    }

    public void setDoubleHandleDt(Date doubleHandleDt) {
        this.doubleHandleDt = doubleHandleDt;
    }

    public String getFirstPreId() {
        return firstPreId;
    }

    public void setFirstPreId(String firstPreId) {
        this.firstPreId = firstPreId;
    }
}


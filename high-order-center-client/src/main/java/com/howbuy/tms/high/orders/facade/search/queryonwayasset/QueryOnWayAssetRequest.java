/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.high.orders.facade.search.queryonwayasset;

import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;

import java.util.List;

/**
 * @description:(查询客户在途资产 request)
 * @reason:
 * <AUTHOR>
 * @date 2017年7月6日 下午2:24:03
 * @since JDK 1.7
 */
public class QueryOnWayAssetRequest extends OrderSearchBaseRequest {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    
    private static final long serialVersionUID = -6734876226340331621L;
    
    /**
     * 协议类型，4-高端产品协议
     */
    private String protocolType = "4";
    /**
     * 分销机构代码列表
     */
    private List<String> disCodeList;

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryonwayasset.QueryOnWayAssetFacade.execute() 查询客户在途资产
     * @apiGroup high-order-center
     * @apiDescription 查询客户在途资产接口
     * 
     * @apiUse orderBaseRequest
     * @apiUse orderSearchBaseRequest
     * 
     * @apiParam {String} [protocolType]  协议类型  = 4-高端协议类型
     * @apiParam {List} [disCodeList]  分销机构代码列表
     * @apiParamExample {json} Request Example
     * dubbo com.howbuy.tms.high.orders.facade.search.queryonwayasset.QueryOnWayAssetRequest
     *{
     *    "appDt": "20170413", 
     *    "appTm": "143122", 
     *    "dataTrack": "0c7b031f-7e1f-4d7c-b1de-3eaaa735db9c", 
     *    "disCode": "HB000A001", 
     *    "operIp": "127.0.0.1", 
     *    "outletCode": "A20150120", 
     *    "pageNo": 1, 
     *    "pageSize": 20, 
     *    "productCode": "481009", 
     *    "protocolType": "4", 
     *    "txAcctNo": "1100875141", 
     *    "txChannel": "4", 
     *    "txCode": "Z330021"
     *}
     * 
     * @apiSuccess {Bigdecimal} buyOnWayAmt 买入在途金额
     * @apiSuccess {List} rdmOnWayVolList 赎回在途资产列表
     *
     * @apiSuccess (rdmOnWayVolList) {String} productCode 基金代码
     * @apiSuccess (rdmOnWayVolList) {String} productName 基金名称
     * @apiSuccess (rdmOnWayVolList) {Bigdecimal} onWayVol 在途份额
     * @apiSuccessExample {json} Response Example
     * dubbo com.howbuy.tms.high.orders.facade.search.queryonwayasset.QueryOnWayAssetResponse
    *{
    *    "rdmOnWayVolList": [
    *        {
    *            "productCode": "110612", 
    *            "onWayVol": 56745
    *        }
    *    ], 
    *    "buyOnWayAmt": "56256.12",
    *    "returnCode": "Z0000000", 
    *    "totalCount": 0, 
    *    "totalPage": 0, 
    *    "txAcctNo": "1100875141"
    *}
     * 
     */
    public QueryOnWayAssetRequest(){
        super.setTxCode(TxCodes.QUERY_CUST_ONWAY_BALANCE);
        setDisCode(DisCodeEnum.HM.getCode());
    }

    public String getProtocolType() {
        return protocolType;
    }

    public void setProtocolType(String protocolType) {
        this.protocolType = protocolType;
    }

    public List<String> getDisCodeList() {
        return disCodeList;
    }

    public void setDisCodeList(List<String> disCodeList) {
        this.disCodeList = disCodeList;
    }
}


/**
 *Copyright (c) 2018, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.high.orders.facade.search.queryprebookdtl;

import com.howbuy.tms.high.orders.facade.enums.TxCodes ;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;

/**
 * @description:(查询预约订单详情) 
 * @reason:
 * <AUTHOR>
 * @date 2018年1月4日 下午8:30:07
 * @since JDK 1.6
 */


/**
 * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryprebookdtl.QueryPreBookDtlFacade.execute() 查询预约单详情
 * @apiGroup high-order-center
 * @apiDescription 查询预约单详情
 * 
 * @apiUse orderBaseRequest
 * @apiUse orderSearchBaseRequest
 * 
 * @apiParam {String} [preId] 预约id
 * 
 * 
 * @apiParamExample {json} Request Example
 * dubbo com.howbuy.tms.high.orders.facade.search.queryprebookdtl.QueryPreBookDtlRequest
 *{
 *    "appDt": "20170413", 
 *    "appTm": "143122", 
 *    "dataTrack": "0c7b031f-7e1f-4d7c-b1de-3eaaa735db9c", 
 *    "disCode": "HB000A001", 
 *    "operIp": "127.0.0.1", 
 *    "outletCode": "A20150120", 
 *    "pageNo": 1, 
 *    "pageSize": 20
 *}
 * 
 * @apiSuccess {String} preId 预约ID
 * @apiSuccess {String} hboneNo 一帐通号
 * @apiSuccess {String} openStartDt  开放开始日
 * @apiSuccess {String} openEndDt 开放截止日
 * @apiSuccess {String} payEndDate 截止打款时间
 * @apiSuccess {String} supportAdvanceFlag 支持提前下单 <br> 0-不支持 1-支持
 * @apiSuccess {String} hboneNo 一帐通号
 * @apiSuccess {String} custNo 客户号
 * @apiSuccess {String} custName 客户姓名
 * @apiSuccess {String} fundCode 产品代码
 * @apiSuccess {String} fundName 产品名称
 * @apiSuccess {String} bankCode 银行代码
 * @apiSuccess {String} bankAcct 银行卡号
 * @apiSuccess {String} bankAddr 分支行
 * @apiSuccess {String} accountHolder 开户人
 * @apiSuccess {String} bankProv 银行卡省份
 * @apiSuccess {String} bankCity 银行卡城市
 * @apiSuccess {String} tradeType 交易类型   <br>1-购买 2-追加 3-赎回
 * @apiSuccess {String} idType  证件类型
 * @apiSuccess {String} idNo 证件号
 * @apiSuccess {String} prebookState 预约单状态<br>1-未确认；2-已确认；4-已撤销
 * @apiSuccess {String} nopaperState 无纸化预约单状态<br> 1-未确认； 2-已确认 3-驳回
 * @apiSuccess {BigDecimal} ackAmt 预约金额
 * @apiSuccess {BigDecimal} sellVol 预约份额
 * @apiSuccess {BigDecimal} discountRate 预约折扣
 * @apiSuccess {BigDecimal} discountfee 无折扣手续费
 * @apiSuccess {BigDecimal} fee 手续费
 * @apiSuccess {String} activityDiscountEndDate 活动折扣截止日
 * @apiSuccess {String} preType 预约类型 <br>1-纸质成单  2-电子成单 3-无纸化
 * @apiSuccess {String} openStartDt 开放开始日
 * @apiSuccess {String} openEndDt 开放截止日 
 * @apiSuccess {String} payEndDate 截止打款时间
 * @apiSuccess {String} supportAdvanceFlag <br>0-不支持 1-支持
 * @apiSuccess {String} investType <br> 0-机构 1-个人
 * @apiSuccess {String} mobile 手机号
 * @apiSuccess {String} validity 证件是否长期有效 <br> 1-长期 2-非长期
 * @apiSuccess {String} validityDt 证件有效截止日期
 * @apiSuccess {String} sex 性别
 * @apiSuccess {String} birth 出生日期
 * @apiSuccess {String} province 省份
 * @apiSuccess {String} city 城市 
 * @apiSuccess {String} post 邮编
 * @apiSuccess {String} tel 电话
 * @apiSuccess {String} email 电子邮箱
 * @apiSuccess {String} vocation 职业 <br>01-政府部门02-教科文03-金融04-商贸05-房地产05-房地产07-自由职业08-其它
 * @apiSuccess {String} education 学历
 * @apiSuccess {String} incomeOfFamily 家庭收入
 * @apiSuccess {String} addr 地址
 * @apiSuccess {String} preDueDt 预约截止日期
 * @apiSuccess {String} nature 机构客户性质 <br> 0:国企;1:民营 ;2:合资;3:其它
 * @apiSuccess {String} aptitude 机构客户资质  1:金融机构;2:金融机构产品;3:社会保障基金;<br>4:企业年金等养老基金;5:慈善基金等社会公益基金<br>6:合格境外机构投资者（QFII）;7:人民币合格境外机构投资者（RQFII）;0:其他
 * @apiSuccess {String} scopeBusiness 经营范围
 * @apiSuccess {String} actualController 实际控制人
 * @apiSuccess {String} prebookState  预约状态<br> 1-未确认2-已确认4-已撤销
 * @apiSuccess {String} modDt  修改日期
 * @apiSuccess {String} creDt 预约日期
 * 
 * 
 * @apiSuccessExample {json} Response Example
 * dubbo com.howbuy.tms.high.orders.facade.search.queryprebookdtl.QueryPreBookDtlResponse
*{
*    "preBookList": [
*        {
*            
*        }
*    ], 
*    "returnCode": "Z0000000", 
*    "totalCount": 0, 
*    "totalPage": 0, 
*    "txAcctNo": "1100875141"
*}
 * 
 */
public class QueryPreBookDtlRequest extends OrderSearchBaseRequest{
    
    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    
    private static final long serialVersionUID = -8233643710387717866L;
    
    /**
     * 预约id
     */
    private String preId;
    
    public QueryPreBookDtlRequest() {
        super.setTxCode(TxCodes.QUERY_PRE_BOOK_DTL);
    }
    
    public String getPreId() {
        return preId;
    }

    public void setPreId(String preId) {
        this.preId = preId;
    }

    
}


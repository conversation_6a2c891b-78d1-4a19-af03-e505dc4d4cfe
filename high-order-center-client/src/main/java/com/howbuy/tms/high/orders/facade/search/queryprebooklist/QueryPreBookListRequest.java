/**
 *Copyright (c) 2018, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.high.orders.facade.search.queryprebooklist;

import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import java.util.List;

/**
 * @description:(查询预约单列表) 
 * @reason:
 * <AUTHOR>
 * @date 2018年1月4日 下午6:40:37
 * @since JDK 1.6
 */

/**
 * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryprebooklist.QueryPreBookListFacade.execute() 查询预约单列表
 * @apiGroup high-order-center
 * @apiDescription 查询预约单列表
 * 
 * @apiUse orderBaseRequest
 * @apiUse orderSearchBaseRequest
 * 
 * @apiParam {List} [preType] 预约类型 <br>
 *           1-纸质成单2-电子成单3-无纸化交易
 * @apiParam {List} [tradeType] 交易类型 <br>
 *           1-购买 2-追加 3-赎回
 * @apiParam {String} [preBookState] 预约单状态 <br>
 *           1-未确认2-已确认4-已撤销
 * @apiParam {String} [noPaperState] 无纸化预约单状态 <br>
 *           1-未确认2-已确认3-驳回
 *@apiParam {String} [useFlag] 使用状态 <br> 1-未使用 2-已使用
 *@apiParam {String} [dueFlag] 过期状态 <br> 1-未过期 2-已过期
 * 
 * 
 * @apiParamExample {json} Request Example dubbo
 *                  com.howbuy.tms.high.orders.facade.search.queryprebooklist.QueryPreBookListRequest
 *                  { "appDt": "20170413", "appTm": "143122", "dataTrack":
 *                  "0c7b031f-7e1f-4d7c-b1de-3eaaa735db9c", "disCode":
 *                  "HB000A001", "operIp": "127.0.0.1", "outletCode":
 *                  "A20150120", "pageNo": 1, "pageSize": 20 }
 * 
 * @apiSuccess {List} preBookList 预约单列表
 * @apiSuccess (preBookList) {String} preId 预约ID
 * @apiSuccess (preBookList) {String} hboneNo 一帐通号
 * @apiSuccess (preBookList) {String} custNo 客户号
 * @apiSuccess (preBookList) {String} custName 客户姓名
 * @apiSuccess (preBookList) {String} fundCode 产品代码
 * @apiSuccess (preBookList) {String} fundName 产品名称
 * @apiSuccess (preBookList) {String} tradeType 交易类型 <br>
 *             1-购买 2-追加 3-赎回
 * @apiSuccess (preBookList) {String} idType 证件类型
 * @apiSuccess (preBookList) {String} idNo 证件号
 * @apiSuccess (preBookList) {String} prebookState 预约单状态<br>
 *             1-未确认；2-已确认；4-已撤销
 * @apiSuccess (preBookList) {String} ackAmt 预约金额
 * @apiSuccess (preBookList) {String} sellVol 预约份额
 * @apiSuccess (preBookList) {String} discount 预约折扣
 * @apiSuccess (preBookList) {String} fee 手续费
 * @apiSuccess (preBookList) {String} discountfee 无折扣手续费
 * @apiSuccess (preBookList) {String} activityDiscountEndDate 活动折扣截止日
 * @apiSuccess (preBookList) {String} preType 预约类型 <br>
 *             1-纸质成单 2-电子成单 3-无纸化
 * @apiSuccess (preBookList) {String} openStartDt 开放开始日
 * @apiSuccess (preBookList) {String} openEndDt 开放截止日
 * @apiSuccess (preBookList) {String} payEndDate 截止打款时间
 * @apiSuccess (preBookList) {String} supportAdvanceFlag <br>
 *             0-不支持 1-支持
 * @apiSuccess (preBookList) {String} investType <br>
 *             0-机构 1-个人
 * @apiSuccess (preBookList) {String} creDt 预约日期
 * @apiSuccess preBookList) {String} orderId
 * 
 * @apiSuccessExample {json} Response Example dubbo
 *                    com.howbuy.tms.high.orders.facade.search.queryprebooklist.QueryPreBookListResponse
 *                    { "preBookList": [ {
 * 
 *                    } ], "returnCode": "Z0000000", "totalCount": 0,
 *                    "totalPage": 0, "txAcctNo": "1100875141" }
 * 
 */
public class QueryPreBookListRequest extends OrderSearchBaseRequest {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = 8760516755846046479L;
    /**
     * 预约类型 1-纸质成单； 2-电子成单； 3-无纸化交易
     */
    private List<String> preType;
    /**
     * 交易类型 1-购买 2-追加 3-赎回
     */
    private List<String> tradeType;
    /**
     * 预约单状态:1-未确认；2-已确认；4-已撤销
     */
    private List<String> preBookState;

    /**
     * 客户姓名
     */
    private String custName;
    /**
     * 证件号
     */
    private String idNo;
    /**
     * 产品代码
     */
    private String fundCode;
    /**
     * 产品名称
     */
    private String fundName;
    /**
     * 预约号
     */
    private String preId;
    /**
     * 开始日期
     */
    private String startDt;
    /**
     * 结束日期
     */
    private String endDt;
    
    /**
     * 使用状态  1-未使用 2-已使用
     */
    private String useFlag;
    /**
     * 是否授权
     */
    @Deprecated
    private String isAuth= YesOrNoEnum.YES.getCode();

    /**
     * 不过滤香港产品,1:是,0:否
     */
    private String notFilterHkFund;

    /**
     * 不过滤好臻产品,1:是,0:否
     */
    private String notFilterHzFund;


    public QueryPreBookListRequest() {
        super.setTxCode(TxCodes.QUERY_PRE_BOOK_LIST);
    }

    public String getIsAuth() {
        return isAuth;
    }

    public String getNotFilterHkFund() {
        return notFilterHkFund;
    }

    public void setNotFilterHkFund(String notFilterHkFund) {
        this.notFilterHkFund = notFilterHkFund;
    }

    public String getNotFilterHzFund() {
        return notFilterHzFund;
    }

    public void setNotFilterHzFund(String notFilterHzFund) {
        this.notFilterHzFund = notFilterHzFund;
    }

    public void setIsAuth(String isAuth) {
        this.isAuth = isAuth;
    }

    public List<String> getPreType() {
        return preType;
    }

    public void setPreType(List<String> preType) {
        this.preType = preType;
    }

    public List<String> getTradeType() {
        return tradeType;
    }

    public void setTradeType(List<String> tradeType) {
        this.tradeType = tradeType;
    }

    public List<String> getPreBookState() {
        return preBookState;
    }

    public void setPreBookState(List<String> preBookState) {
        this.preBookState = preBookState;
    }


    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getPreId() {
        return preId;
    }

    public void setPreId(String preId) {
        this.preId = preId;
    }

    public String getStartDt() {
        return startDt;
    }

    public void setStartDt(String startDt) {
        this.startDt = startDt;
    }

    public String getEndDt() {
        return endDt;
    }

    public void setEndDt(String endDt) {
        this.endDt = endDt;
    }

    public String getUseFlag() {
        return useFlag;
    }

    public void setUseFlag(String useFlag) {
        this.useFlag = useFlag;
    }


}

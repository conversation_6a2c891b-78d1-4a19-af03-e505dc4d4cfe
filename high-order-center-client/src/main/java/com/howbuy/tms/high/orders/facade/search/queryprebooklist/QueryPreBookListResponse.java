/**
 *Copyright (c) 2018, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.high.orders.facade.search.queryprebooklist;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description:(预约单列表响应)
 * @reason:
 * <AUTHOR>
 * @date 2018年1月4日 下午6:55:10
 * @since JDK 1.6
 */
public class QueryPreBookListResponse extends OrderSearchBaseResponse {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = 9202481948324216619L;

    /**
     * 是否持有好臻产品 0:没有,1:有
     */
    private String hasHZProduct;
    /**
     * 是否持有好买香港产品  0:没有,1:有
     */
    private String hasHKProduct;

    private List<PreBookListBean> preBookList;

    public List<PreBookListBean> getPreBookList() {
        return preBookList;
    }

    public String getHasHZProduct() {
        return hasHZProduct;
    }

    public void setHasHZProduct(String hasHZProduct) {
        this.hasHZProduct = hasHZProduct;
    }

    public String getHasHKProduct() {
        return hasHKProduct;
    }

    public void setHasHKProduct(String hasHKProduct) {
        this.hasHKProduct = hasHKProduct;
    }

    public void setPreBookList(List<PreBookListBean> preBookList) {
        this.preBookList = preBookList;
    }

    public static class PreBookListBean implements Serializable {
        /**
         * serialVersionUID:TODO（用一句话描述这个变量表示什么）
         *
         * @since Ver 1.1
         */

        private static final long serialVersionUID = 3046595834783565955L;

        /**
         * 预约单标识，CRM唯一
         */
        private String preId;
        /**
         * 一帐通号
         */
        private String hboneNo;
        /**
         * 费用计算方式,0-外扣法；1-内扣法
         */
        private String feeCalMode;

        /**
         * 客户号
         */
        private String custNo;
        /**
         * 姓名
         */
        private String custName;
        /**
         * 产品代码
         */
        private String fundCode;
        /**
         * 产品名称
         */
        private String fundName;
        
        /**
         * 产品类别
         */
        private String fundType;

        /**
         * 交易类型 1-购买 2-追加 3-赎回
         */
        private String tradeType;
        /**
         * 证件类型
         */
        private String idType;
        /**
         * 证件号
         */
        private String idNo;
        /**
         * 预约单状态:1-未确认；2-已确认；4-已撤销
         */
        private String prebookState;
        /**
         * 无纸化预约单状态 1-未确认； 2-已确认； 3-驳回
         */
        private String nopaperState;
        /**
         * 预约金额
         */
        private BigDecimal ackAmt;
        /**
         * 预约份额
         */
        private BigDecimal sellVol;
        /**
         * 手续费
         */
        private BigDecimal fee;
        
        /**
         * 费率
         */
        private BigDecimal feeRate;

        /**
         * 预约折扣
         */
        private BigDecimal discount;

        /**
         * 无折扣手续费
         */
        private BigDecimal disCountFee;

        /**
         * 活动折扣截止日
         */
        private String activityDiscountEndDate;

        /**
         * 预约类型 1：纸质成单； 2：电子成单； 3：无纸化；
         */
        private String preType;

        /**
         * 开放开始日
         */
        private String openStartDt;
        /**
         * 开放截止日
         */
        private String openEndDt;

        /**
         * 截止打款时间
         */
        private String payEndDate;
        /**
         * 支持提前下单 0-不支持 1-支持
         */
        private String supportAdvanceFlag;

        /**
         * 客户投资者类型 0-机构 1-个人
         * 
         */
        private String investType;

        /**
         * 预约日期
         */
        private String creDt;

        /**
         * 手续费费率方式
         */
        private String feeRateMethod;

        /**
         * 中台业务码
         */
        private String mBusiCode;

        /**
         * 中台订单号
         */
        private String orderId;
        /**
         * 中台订单号
         */
        private String bankAcctNo;
        
        /**
         * 份额类型 A-前收费 B-后收费
         */
        private String shareClass;
        
        /**
         * 赎回开放日
         */
        private String redeemDate;

        /**
         *  需双录标识：0-无需双录；1-需双录;
         */
        private String doubleNeedFlag;

        /**
         * 处理标识：0-无需处理；1-未处理；2-已处理
         */
        private String doubleHandleFlag;

        /**
         * 双录处理时间，带时间yyyyMMddHH24miss
         */
        private Date doubleHandleDt;

        /**
         * 是否首次实缴预约 0-否 1-是
         */
        private String firstPreId;

        /**
         * 折扣类型：1-使用折扣率，2-使用折扣金额
         */
        private String discountUseType;

        /**
         *  折扣金额
         */
        private BigDecimal discountAmt;

        /**
         * 预约认缴金额
         */
        private BigDecimal appointSubsAmt;
        /**
         * 认缴金额
         */
        private BigDecimal subsAmt;

        /**
         * 产品信息来源 SMOP，DB（仅SMOP未配置时，取DB）
         */
        private String productSource;

        /**
         * 币种
         */
        private String currency;

        /**
         * 分销渠道
         */
        private String disCode;

        /**
         * 是否分次CALL款股权产品 0-否 1是
         */
        private String peDivideCallFlag;


        public String getDiscountUseType() {
            return discountUseType;
        }

        public void setDiscountUseType(String discountUseType) {
            this.discountUseType = discountUseType;
        }

        public BigDecimal getDiscountAmt() {
            return discountAmt;
        }

        public void setDiscountAmt(BigDecimal discountAmt) {
            this.discountAmt = discountAmt;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }

        public String getDisCode() {
            return disCode;
        }

        public void setDisCode(String disCode) {
            this.disCode = disCode;
        }

        public BigDecimal getAppointSubsAmt() {
            return appointSubsAmt;
        }

        public String getFeeCalMode() {
            return feeCalMode;
        }

        public void setFeeCalMode(String feeCalMode) {
            this.feeCalMode = feeCalMode;
        }

        public void setAppointSubsAmt(BigDecimal appointSubsAmt) {
            this.appointSubsAmt = appointSubsAmt;
        }

        public String getFeeRateMethod() {
            return feeRateMethod;
        }

        public void setFeeRateMethod(String feeRateMethod) {
            this.feeRateMethod = feeRateMethod;
        }

        public String getPeDivideCallFlag() {
            return peDivideCallFlag;
        }

        public void setPeDivideCallFlag(String peDivideCallFlag) {
            this.peDivideCallFlag = peDivideCallFlag;
        }

        public BigDecimal getDisCountFee() {
            return disCountFee;
        }

        public void setDisCountFee(BigDecimal disCountFee) {
            this.disCountFee = disCountFee;
        }

        public String getActivityDiscountEndDate() {
            return activityDiscountEndDate;
        }

        public void setActivityDiscountEndDate(String activityDiscountEndDate) {
            this.activityDiscountEndDate = activityDiscountEndDate;
        }

        public String getPreType() {
            return preType;
        }

        public void setPreType(String preType) {
            this.preType = preType;
        }

        public String getOpenStartDt() {
            return openStartDt;
        }

        public void setOpenStartDt(String openStartDt) {
            this.openStartDt = openStartDt;
        }

        public String getOpenEndDt() {
            return openEndDt;
        }

        public void setOpenEndDt(String openEndDt) {
            this.openEndDt = openEndDt;
        }

        public String getPayEndDate() {
            return payEndDate;
        }

        public void setPayEndDate(String payEndDate) {
            this.payEndDate = payEndDate;
        }

        public String getSupportAdvanceFlag() {
            return supportAdvanceFlag;
        }

        public void setSupportAdvanceFlag(String supportAdvanceFlag) {
            this.supportAdvanceFlag = supportAdvanceFlag;
        }

        public String getInvestType() {
            return investType;
        }

        public void setInvestType(String investType) {
            this.investType = investType;
        }

        public String getPreId() {
            return preId;
        }

        public void setPreId(String preId) {
            this.preId = preId;
        }

        public String getHboneNo() {
            return hboneNo;
        }

        public void setHboneNo(String hboneNo) {
            this.hboneNo = hboneNo;
        }

        public String getCustNo() {
            return custNo;
        }

        public void setCustNo(String custNo) {
            this.custNo = custNo;
        }

        public String getCustName() {
            return custName;
        }

        public void setCustName(String custName) {
            this.custName = custName;
        }

        public String getFundCode() {
            return fundCode;
        }

        public void setFundCode(String fundCode) {
            this.fundCode = fundCode;
        }

        public String getFundName() {
            return fundName;
        }

        public void setFundName(String fundName) {
            this.fundName = fundName;
        }

        public String getTradeType() {
            return tradeType;
        }

        public void setTradeType(String tradeType) {
            this.tradeType = tradeType;
        }

        public String getIdType() {
            return idType;
        }

        public void setIdType(String idType) {
            this.idType = idType;
        }

        public String getIdNo() {
            return idNo;
        }

        public void setIdNo(String idNo) {
            this.idNo = idNo;
        }

        public String getPrebookState() {
            return prebookState;
        }

        public void setPrebookState(String prebookState) {
            this.prebookState = prebookState;
        }

        public String getNopaperState() {
            return nopaperState;
        }

        public void setNopaperState(String nopaperState) {
            this.nopaperState = nopaperState;
        }

        public BigDecimal getAckAmt() {
            return ackAmt;
        }

        public void setAckAmt(BigDecimal ackAmt) {
            this.ackAmt = ackAmt;
        }

        public BigDecimal getSellVol() {
            return sellVol;
        }

        public void setSellVol(BigDecimal sellVol) {
            this.sellVol = sellVol;
        }

        public BigDecimal getFee() {
            return fee;
        }

        public void setFee(BigDecimal fee) {
            this.fee = fee;
        }

        public String getCreDt() {
            return creDt;
        }

        public void setCreDt(String creDt) {
            this.creDt = creDt;
        }

        public String getmBusiCode() {
            return mBusiCode;
        }

        public void setmBusiCode(String mBusiCode) {
            this.mBusiCode = mBusiCode;
        }

        public BigDecimal getDiscount() {
            return discount;
        }

        public void setDiscount(BigDecimal discount) {
            this.discount = discount;
        }

        public String getOrderId() {
            return orderId;
        }

        public void setOrderId(String orderId) {
            this.orderId = orderId;
        }

        public String getBankAcctNo() {
            return bankAcctNo;
        }

        public void setBankAcctNo(String bankAcctNo) {
            this.bankAcctNo = bankAcctNo;
        }

        public String getShareClass() {
            return shareClass;
        }

        public void setShareClass(String shareClass) {
            this.shareClass = shareClass;
        }

        public BigDecimal getFeeRate() {
            return feeRate;
        }

        public void setFeeRate(BigDecimal feeRate) {
            this.feeRate = feeRate;
        }

        public String getFundType() {
            return fundType;
        }

        public void setFundType(String fundType) {
            this.fundType = fundType;
        }

        public String getRedeemDate() {
            return redeemDate;
        }

        public void setRedeemDate(String redeemDate) {
            this.redeemDate = redeemDate;
        }

        public String getDoubleNeedFlag() {
            return doubleNeedFlag;
        }

        public void setDoubleNeedFlag(String doubleNeedFlag) {
            this.doubleNeedFlag = doubleNeedFlag;
        }

        public String getDoubleHandleFlag() {
            return doubleHandleFlag;
        }

        public void setDoubleHandleFlag(String doubleHandleFlag) {
            this.doubleHandleFlag = doubleHandleFlag;
        }

        public Date getDoubleHandleDt() {
            return doubleHandleDt;
        }

        public void setDoubleHandleDt(Date doubleHandleDt) {
            this.doubleHandleDt = doubleHandleDt;
        }

        public String getFirstPreId() {
            return firstPreId;
        }

        public void setFirstPreId(String firstPreId) {
            this.firstPreId = firstPreId;
        }

        public BigDecimal getSubsAmt() {
            return subsAmt;
        }

        public void setSubsAmt(BigDecimal subsAmt) {
            this.subsAmt = subsAmt;
        }

        public String getProductSource() {
            return productSource;
        }

        public void setProductSource(String productSource) {
            this.productSource = productSource;
        }
    }

}

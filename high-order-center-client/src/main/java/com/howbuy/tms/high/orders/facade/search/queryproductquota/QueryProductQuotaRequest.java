/**
 *Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.high.orders.facade.search.queryproductquota;

import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;

/**
 * @description:(查询产品额度信息)
 * @reason:
 * <AUTHOR>
 * @date 2017年7月6日 下午2:24:03
 * @since JDK 1.7
 */
public class QueryProductQuotaRequest extends OrderSearchBaseRequest {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    
    private static final long serialVersionUID = -1734876326340331621L;
    
    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryproductquota.QueryProductQuotaFacade.execute() 查询产品额度信息接口
     * @apiGroup high-order-center
     * @apiDescription 查询产品额度信息接口
     * 
     * @apiUse orderBaseRequest
     * @apiUse orderSearchBaseRequest
     * 
     * @apiParam {String[]} productCodeArr  产品代码数组
     * 
     * @apiParamExample {json} Request Example
     * dubbo com.howbuy.tms.high.orders.facade.search.queryproductquota.QueryProductQuotaRequest
     *{
     *    "appDt": "20170413", 
     *    "appTm": "143122", 
     *    "dataTrack": "0c7b031f-7e1f-4d7c-b1de-3eaaa735db9c", 
     *    "disCode": "HB000A001", 
     *    "operIp": "127.0.0.1", 
     *    "outletCode": "A20150120", 
     *    "pageNo": 1, 
     *    "pageSize": 20, 
     *    "productCodeArr": ["481009"], 
     *    "txAcctNo": "1100875141", 
     *    "txChannel": "4", 
     *    "txCode": "Z330021"
     *}
     * 
     * @apiSuccess {List} quotaBeanList 产品额度信息列表
     *
     * @apiSuccess (quotaBeanList) {Bigdecimal} totalAmt 产品总金额
     * @apiSuccess (quotaBeanList) {Long} totalPlaces 产品总人数
     * @apiSuccess (quotaBeanList) {Bigdecimal} costTotalAmt 产品已消耗总金额
     * @apiSuccess (quotaBeanList) {Long} costTotalPlaces 产品已消耗总人数
     * @apiSuccess (quotaBeanList) {String} inDirectBlackList 在直销黑名单中<br>0-非黑名单;1-黑名单
     * @apiSuccessExample {json} Response Example
     * dubbo com.howbuy.tms.high.orders.facade.search.queryproductquota.QueryProductQuotaResponse
     *{
     *    "quotaBeanList": [
     *        {
     *            "totalAmt": "110612", 
     *            "totalPlaces": 567425,
     *            "costTotalAmt": 56745,
     *            "costTotalPlaces": 56745
     *        }
     *    ], 
     *    "returnCode": "Z0000000", 
     *    "totalCount": 0, 
     *    "totalPage": 0, 
     *    "txAcctNo": "1100875141"
     *}
     * 
     */
    public QueryProductQuotaRequest(){
        super.setTxCode(TxCodes.QUERY_PRODUCT_QUOTA);
    }
    
    private String[] productCodeArr;

    public String[] getProductCodeArr() {
        return productCodeArr;
    }

    public void setProductCodeArr(String[] productCodeArr) {
        this.productCodeArr = productCodeArr;
    }

}


/**
 *Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.facade.search.queryredeemfundlist;

import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;

import java.util.List;

/**
 * 
 * @description:(赎回列表)
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2017年12月1日 上午9:29:15
 * @since JDK 1.6
 */
public class QueryRedeemFundListRequest extends OrderSearchBaseRequest {

    private static final long serialVersionUID = 247721317350139773L;

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryredeemfundlist.QueryRedeemFundListFacade.execute() 查询高端产品赎回列表
     * @apiGroup high-order-center
     * @apiDescription 查询高端产品赎回列表接口
     * 
     * @apiUse orderBaseRequest
     * @apiUse orderSearchBaseRequest
     * 
     * @apiParam {String} [productCode] 产品代码
     * @apiParam {String} [fundShareClass] 产品份额类型
     *                                          A-前收费；B-后收费
     * @apiParam {String} [protocolNo] 协议号
     * @apiParam {String} [protocolType]    =   4   协议类型，4-高端协议类型
     * @apiParam {String} [cpAcctNo] 资金账号
     * @apiParam {List} disCodeList 分销机构代码列表
     * 
     * @apiParamExample {json} Request Example
     * dubbo com.howbuy.tms.high.orders.facade.search.queryredeemfundlist.QueryRedeemFundListRequest
     *{
     *    "appDt": "20170413", 
     *    "appTm": "143122", 
     *    "dataTrack": "0c7b031f-7e1f-4d7c-b1de-3eaaa735db9c", 
     *    "disCode": "HB000A001", 
     *    "operIp": "127.0.0.1", 
     *    "outletCode": "*********", 
     *    "pageNo": 1, 
     *    "pageSize": 20, 
     *    "productCode": "481009", 
     *    "protocolType": "2", 
     *    "txAcctNo": "**********", 
     *    "txChannel": "4", 
     *    "txCode": "Z330016"
     *}
     * 
     * @apiUse orderBaseResponse
     * @apiUse orderSearchBaseResponse
     * 
     * @apiSuccess {RedeemBalanceBean} redeemBalanceBean 持仓明细信息
     * 
     * @apiSuccess (redeemBalanceBean) {List} redeemBalanceList 持仓明细信息列表
     * 
     * @apiSuccess (redeemBalanceList) {String} txAcctNo 交易账号
     * @apiSuccess (redeemBalanceList) {String} disCode 分销机构号
     * @apiSuccess (redeemBalanceList) {String} productCode 产品代码
     * @apiSuccess (redeemBalanceList) {String} fundShareClass 份额类型
     * @apiSuccess (redeemBalanceList) {String} productName 产品名称
     * @apiSuccess (redeemBalanceList) {String} productType 产品类型
     * @apiSuccess (redeemBalanceList) {BigDecimal} nav 净值
     * @apiSuccess (redeemBalanceList) {String} productStatus 产品状态
     *                                      0-交易； 1-发行； 2-发行成功； 3-发行失败； 4-停止交易； 5-停止申购； 6-停止赎回； 7-权益登记； 8-红利发放；9-基金封闭； a-基金终止
     * @apiSuccess (redeemBalanceList) {String} navDt 净值日期
     * @apiSuccess (redeemBalanceList) {String} protocolNo 协议号
     * @apiSuccess (redeemBalanceList) {String} cpAcctNo 资金账号
     * @apiSuccess (redeemBalanceList) {String} bankCode 银行代码
     * @apiSuccess (redeemBalanceList) {String} bankName 银行名称
     * @apiSuccess (redeemBalanceList) {String} bankAcctNo 银行卡号（后4位）
     * @apiSuccess (redeemBalanceList) {BigDecimal} balanceVol 总份额
     * @apiSuccess (redeemBalanceList) {BigDecimal} availVol 可用份额
     * @apiSuccess (redeemBalanceList) {BigDecimal} unconfirmedVol 待确认份额
     * @apiSuccess (redeemBalanceList) {BigDecimal} marketValue 市值
     * @apiSuccess (redeemBalanceList) {String} openRedeDt 开放赎回日
     * @apiSuccess (redeemBalanceList) {boolean} canSell 是否可赎回
     *                                          true-可赎回 false-不可赎回
     * @apiSuccess (redeemBalanceList) {String} productPinyinName 产品拼音码
     * @apiSuccess (redeemBalanceList) {String} openStartDt 开放开始日期
     * @apiSuccess (redeemBalanceList) {String} openEndDt 开放结束日期
     * @apiSuccess (redeemBalanceList) {String} appointStartDt 预约开始日期
     * @apiSuccess (redeemBalanceList) {String} appointEndDt 预约结束日期
     * @apiSuccess (redeemBalanceList) {String} bankAccount bankcode+"|"+accountId 拼接
     * @apiSuccess (redeemBalanceList) {String} isBook 是否支持预约
     * 
     * @apiSuccessExample {json} Response Example
     * dubbo com.howbuy.tms.high.orders.facade.search.queryredeemfundlist.QueryRedeemFundListResponse
    *{
    *    "redeemBalanceList": [
    *        {
    *            "buyStatus": "2", 
    *            "description": "成功", 
    *            "disCode": "HB000A001", 
    *            "nav": 1, 
    *            "navDt": "********", 
    *            "pageNo": 0, 
    *            "productCode": "481009", 
    *            "productName": "工银沪深300指数", 
    *            "productStatus": "0", 
    *            "productType": "0", 
    *            "returnCode": "********", 
    *            "totalCount": 0, 
    *            "totalPage": 0, 
    *            "txAcctNo": "**********"
    *            "availVol": 17821.78, 
    *            "balanceVol": 20792.08, 
    *            "bankCode": "106", 
    *            "bankName": "内蒙古自治区分行", 
    *            "cpAcctNo": "****************", 
    *            "marketValue": 17821.78, 
    *            "protocolNo": "1517020805354200000001141", 
    *            "unconfirmedAmt": 0, 
    *            "unconfirmedVol": 2970.3,
    *            "canSell": true,
    *            "productPinyinName": "xfcjj",
    *            "openStartDt": "********",
    *            "openEndDt": "********",
    *            "appointStartDt": "********",
    *            "appointEndDt": "********",
    *            "bankAccount": "104|1121",
    *            "isBook": "1"
    *        }
    *    ], 
    *    
    *}
     * 
     */
    public QueryRedeemFundListRequest() {
        this.setTxCode(TxCodes.QUERY_REDEEM_FUND_LIST);
        setDisCode(DisCodeEnum.HM.getCode());
    }

    /**
     * 产品代码
     */
    private String productCode;
    /**
     * 产品份额类型，A-前收费；B-后收费
     */
    private String fundShareClass;
    /**
     * 协议号
     */
    private String protocolNo;
    /**
     * 4-高端协议类型
     */
    private String protocolType = "4";
    /**
     * 资金账号
     */
    private String cpAcctNo;
    /**
     * 分销机构代码列表
     */
    private List<String> disCodeList;

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getFundShareClass() {
        return fundShareClass;
    }

    public void setFundShareClass(String fundShareClass) {
        this.fundShareClass = fundShareClass;
    }

    public String getProtocolNo() {
        return protocolNo;
    }

    public void setProtocolNo(String protocolNo) {
        this.protocolNo = protocolNo;
    }

    public String getProtocolType() {
        return protocolType;
    }

    public void setProtocolType(String protocolType) {
        this.protocolType = protocolType;
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }

    public List<String> getDisCodeList() {
        return disCodeList;
    }

    public void setDisCodeList(List<String> disCodeList) {
        this.disCodeList = disCodeList;
    }
}
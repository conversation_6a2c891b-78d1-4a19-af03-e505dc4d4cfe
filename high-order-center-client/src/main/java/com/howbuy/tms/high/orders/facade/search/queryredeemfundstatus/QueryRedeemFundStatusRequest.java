/**
 *Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.facade.search.queryredeemfundstatus;

import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import java.util.List;

/**
 * 
 * @description:(查询产品赎回状态)
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2017年11月28日 上午9:15:15
 * @since JDK 1.6
 */
public class QueryRedeemFundStatusRequest extends OrderSearchBaseRequest {

    private static final long serialVersionUID = 247721317350139773L;

    public QueryRedeemFundStatusRequest() {
        this.setTxCode(TxCodes.QUERY_REDEEM_FUND_STATUS);
        setDisCode(DisCodeEnum.HM.getCode());
    }

    /**
     * 产品代码
     */
    private List<String> productCodeList;

    /**
     * 分销机构代码列表
     */
    private List<String> disCodeList;

    public List<String> getProductCodeList() {
        return productCodeList;
    }

    public void setProductCodeList(List<String> productCodeList) {
        this.productCodeList = productCodeList;
    }

    public List<String> getDisCodeList() {
        return disCodeList;
    }

    public void setDisCodeList(List<String> disCodeList) {
        this.disCodeList = disCodeList;
    }
}
/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.facade.search.queryrepurchaseprotocol;

import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;

import java.util.List;

/**
 * @className QueryCustRepurchaseProtocolRequest
 * @description
 * <AUTHOR>
 * @date 2019/3/21 14:50
 */



public class QueryCustRepurchaseProtocolRequest extends OrderSearchBaseRequest {
    private static final long serialVersionUID = 6292251908121512775L;

    private List<String> fundCodes;

    public QueryCustRepurchaseProtocolRequest(){
        super.setTxCode(TxCodes.HIGH_QUERY_CUST_REPURCHASE_PROTOCOL);
    }

    public List<String> getFundCodes() {
        return fundCodes;
    }

    public void setFundCodes(List<String> fundCodes) {
        this.fundCodes = fundCodes;
    }
}

/**
 * Copyright (c) 2021, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.facade.search.querysaleorderlist;

import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;

/**
 * 查询保险订单列表（高端重疾险）
 * <AUTHOR>
 * @date 2021/12/30 13:45
 * @since JDK 1.8
 */
public class QuerySaleOrderListRequest extends OrderSearchBaseRequest {

    private static final long serialVersionUID = -2135638049436528688L;

    public QuerySaleOrderListRequest(){
        super.setTxCode(TxCodes.QUERY_SALE_ORDER_LIST);
    }

    private String orderStatusType;
    private String companyCode;

    public String getOrderStatusType() {
        return orderStatusType;
    }

    public void setOrderStatusType(String orderStatusType) {
        this.orderStatusType = orderStatusType;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }
}
/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.high.orders.facade.search.querysignelecstatus;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.validate.MyValidation;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import java.math.BigDecimal;

/**
 * Description:查询是否需要签署电子签名/电子合同接口请求参数
 * 
 * @reason:
 * <AUTHOR>
 * @date 2017年4月13日 下午6:18:43
 * @since JDK 1.7
 */
public class QuerySignElecStatusRequest extends OrderSearchBaseRequest {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = 3792774217963054067L;	

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.querysignelecstatus.QuerySignElecStatusFacade.execute() 查询是否需要签署电子签名/电子合同接口
     * @apiGroup high-order-center
     * @apiDescription 查询是否需要签署电子签名/电子合同接口
     * 
     * @apiUse orderBaseRequest
     * @apiUse orderSearchBaseRequest
     * 
     * @apiParam {String} fundCode 基金代码
     * @apiParam {String} fundShareClass 份额类型
     *                                       A-前收费；B-后收费
     * @apiParam {BigDecimal} appAmt 申请金额
     * @apiParam {BigDecimal} esitmateFee 预估手续费
     * 
     * @apiParamExample {json} Request Example dubbo
     * com.howbuy.tms.high.orders.facade.search.querysignelecstatus.QuerySignElecStatusRequest
     * 
     * @apiUse orderBaseResponse
     * @apiUse orderSearchBaseResponse
     * 
     * @apiSuccess {String} suppleSubsFlag 追加购买标识:<br> 1-首次购买; 2-追加
     * @apiSuccess {String} esignatureFlag 是否需要电子签名标识:<br> 1-需要; 2-不需要
     * @apiSuccess {String} econtractFlag 是否需要电子合同标识:<br> 1-需要; 2-不需要
     * 
     * @apiSuccessExample {json} Response Example dubbo
     * com.howbuy.tms.high.orders.facade.search.querysignelecstatus.QuerySignElecStatusResponse
     * 
     */
    public QuerySignElecStatusRequest() {
        setTxCode(TxCodes.QUERY_SIGN_ELEC_STATUS);
    }

    /**
     * 基金代码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金代码", isRequired = true, max = 6)
    private String fundCode;
    /**
     * 份额类型：A-前收费；B-后收费
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "份额类型", isRequired = true, max = 1)
    private String fundShareClass;
    /**
     * 申请金额
     */
    @MyValidation(validatorType = ValidatorTypeEnum.Money, fieldName = "申请金额", isRequired = true)
    private BigDecimal appAmt;
    /**
     * 预估手续费
     */
    @MyValidation(validatorType = ValidatorTypeEnum.Money, fieldName = "预估手续费", isRequired = true)
    private BigDecimal esitmateFee;
    
    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundShareClass() {
        return fundShareClass;
    }

    public void setFundShareClass(String fundShareClass) {
        this.fundShareClass = fundShareClass;
    }

    public BigDecimal getAppAmt() {
        return appAmt;
    }

    public void setAppAmt(BigDecimal appAmt) {
        this.appAmt = appAmt;
    }

    public BigDecimal getEsitmateFee() {
        return esitmateFee;
    }

    public void setEsitmateFee(BigDecimal esitmateFee) {
        this.esitmateFee = esitmateFee;
    }

}

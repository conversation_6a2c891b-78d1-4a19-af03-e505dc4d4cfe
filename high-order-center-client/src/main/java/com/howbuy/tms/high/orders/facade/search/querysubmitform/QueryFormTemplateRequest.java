package com.howbuy.tms.high.orders.facade.search.querysubmitform;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.validate.MyValidation;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import lombok.Data;

/**
 * @Description:查询表单模板请求
 * @Author: yun.lu
 * Date: 2024/1/24 20:12
 */
@Data
public class QueryFormTemplateRequest extends OrderSearchBaseRequest {
    /**
     * 模版id
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "模版id", isRequired = true)
    private String templateId;

    public QueryFormTemplateRequest() {
        setTxCode(TxCodes.QUERY_FORM_TEMPLATE);
    }
}

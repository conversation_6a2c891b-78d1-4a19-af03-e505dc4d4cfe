/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.high.orders.facade.search.querysupplestatus;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.validate.MyValidation;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;

/**
 * Description:查询交易追加状态接口请求参数
 * 
 * @reason:
 * <AUTHOR>
 * @date 2017年4月13日 下午6:18:43
 * @since JDK 1.7
 */
public class QuerySuppleStatusRequest extends OrderSearchBaseRequest {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = 3792774317963051067L;

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.querysupplestatus.QuerySuppleStatusFacade.execute() 查询交易追加状态接口
     * @apiGroup high-order-center
     * @apiDescription 查询交易追加状态接口
     * 
     * @apiUse orderBaseRequest
     * @apiUse orderSearchBaseRequest
     * 
     * @apiParam {String} fundCode 基金代码
     * @apiParam {String} fundShareClass 份额类型
     *                                   A-前收费；B-后收费
     * @apiParamExample {json} Request Example    
     * dubbo com.howbuy.tms.high.orders.facade.search.querysupplestatus.QuerySuppleStatusRequest
     * 
     * @apiUse orderBaseResponse
     * @apiUse orderSearchBaseResponse
     * 
     * @apiSuccess {String} suppleSubsStatus 追加购买状态:<br> 1-首次购买; 2-追加购买
     * @apiSuccess {String} firstOrderDes 首单描述:<br> 1-真首单; 2-伪首单
     * @apiSuccessExample {json} Response Example    
     * dubbo com.howbuy.tms.high.orders.facade.search.querysupplestatus.QuerySuppleStatusResponse
     * 
     */
    public QuerySuppleStatusRequest() {
        setTxCode(TxCodes.QUERY_SUPPLE_STATUS);
    }

    /**
     * 基金代码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金代码", isRequired = true, max = 6)
    private String fundCode;
    /**
     * 份额类型：A-前收费；B-后收费
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "份额类型", isRequired = true, max = 1)
    private String fundShareClass;

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundShareClass() {
        return fundShareClass;
    }

    public void setFundShareClass(String fundShareClass) {
        this.fundShareClass = fundShareClass;
    }

}

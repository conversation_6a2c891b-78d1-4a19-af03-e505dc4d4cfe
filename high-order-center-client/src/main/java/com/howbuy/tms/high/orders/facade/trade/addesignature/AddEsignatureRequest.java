/**
 *Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.high.orders.facade.trade.addesignature;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.idempotent.IdempotentSupport;
import com.howbuy.tms.common.validate.MyValidation;
import com.howbuy.tms.high.orders.facade.common.OrderTradeBaseRequest;

/**
 * Description:新增电子签名接口请求参数
 * 
 * @reason:
 * <AUTHOR>
 * @date 2017年4月13日 下午5:32:15
 * @since JDK 1.7
 */
public class AddEsignatureRequest extends OrderTradeBaseRequest implements IdempotentSupport {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = -8560621647427566061L;
    
    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.trade.addesignature.AddEsignatureFacade.execute() 新增电子签名接口
     * @apiGroup high-order-center
     * @apiDescription 新增电子签名接口
     * 
     * @apiUse orderBaseRequest
     * @apiUse orderTradeBaseRequest
     * 
     * @apiParam {String} dealNo 订单号
     * @apiParam {String} productCode 产品代码
     * @apiParam {String} fundShareClass 份额类型
     *                                  A-前收费；B-后收费
     * @apiParam {Long} signatureValue 默认签名值
     * 
     * @apiParamExample {json} Request Example
     * dubbo com.howbuy.tms.high.orders.facade.trade.addesignature.AddEsignatureRequest
     *{
    *    "appDt": "20170413", 
    *    "appTm": "195753", 
    *    "dataTrack": "36e36d27-93fb-4c09-b25c-dc55226c1a0b", 
    *    "disCode": "HB000A001", 
    *    "externalDealNo": "9631100c-5ee7-43c3-abbf-ae0c0e50a13b", 
    *    "fundShareClass": "A", 
    *    "operIp": "127.0.0.1", 
    *    "outletCode": "A20150120", 
    *    "pageNo": 1, 
    *    "pageSize": 20, 
    *    "productCode": "481008", 
    *    "signatureValue": 0, 
    *    "txAcctNo": "1100875141", 
    *    "txChannel": "4"
    *}
     * 
     * @apiUse orderBaseResponse
     * @apiUse orderTradeBaseResponse
     * 
     * @apiSuccess {String} signatureSid 电子签名流水号
     * @apiSuccess {String} txAcctNo 交易账号
     * @apiSuccess {String} productCode 产品代码
     * @apiSuccess {String} fundShareClass 份额类型
     *                                     A-前收费；B-后收费
     * @apiSuccess {Long} singnatureValue 签名结果值
     * @apiSuccess {Date} createDtm 创建日期时间
     * 
     * @apiSuccessExample {json} Response Example
     * dubbo com.howbuy.tms.high.orders.facade.trade.addesignature.AddEsignatureResponse
     *{
    *    "createDtm": 1492084673972, 
    *    "description": "成功", 
    *    "fundShareClass": "A", 
    *    "pageNo": 0, 
    *    "productCode": "481008", 
    *    "returnCode": "Z0000000", 
    *    "signatureSid": "9917041319575300002001141", 
    *    "singnatureValue": 0, 
    *    "totalCount": 0, 
    *    "totalPage": 0, 
    *    "txAcctNo": "1100875141"
    *}
     * 
     */
    public AddEsignatureRequest(){
        setTxCode(TxCodes.HIGH_FUND_ADD_ESIGNATURE);
    }
    /**
     * 中台订单号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "订单号", isRequired = true, max = 32)
    private String dealNo;
    /**
     * 产品代码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "产品代码", isRequired = true, max = 10)
    private String productCode;
    /**
     * 份额类型
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "份额类型", isRequired = true, max = 1)
    private String fundShareClass;
    /**
     * 默认签名值
     */
    @MyValidation(validatorType = ValidatorTypeEnum.Digit, fieldName = "默认签名值", isRequired = true, max = 10)
    private long signatureValue;


    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getFundShareClass() {
        return fundShareClass;
    }

    public void setFundShareClass(String fundShareClass) {
        this.fundShareClass = fundShareClass;
    }

    public long getSignatureValue() {
        return signatureValue;
    }

    public void setSignatureValue(long signatureValue) {
        this.signatureValue = signatureValue;
    }

    public String getDealNo() {
        return dealNo;
    }

    public void setDealNo(String dealNo) {
        this.dealNo = dealNo;
    }

    @Override
    public String toIdempotentString() {

        StringBuilder idempotent = new StringBuilder(100);
        idempotent.append(getTxAcctNo());
        idempotent.append(getProductCode());
        idempotent.append(getFundShareClass());
        idempotent.append(getDealNo());

        return idempotent.toString();

    }

}


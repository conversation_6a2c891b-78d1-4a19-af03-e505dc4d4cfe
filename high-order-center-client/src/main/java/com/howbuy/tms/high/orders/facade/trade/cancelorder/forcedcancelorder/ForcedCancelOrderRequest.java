/**
 *Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.high.orders.facade.trade.cancelorder.forcedcancelorder;

import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.idempotent.IdempotentSupport;
import com.howbuy.tms.high.orders.facade.trade.cancelorder.BaseCancelOrderRequest;

/**
 * @description:(强制撤单接口 request)
 * @reason:
 * <AUTHOR>
 * @date 2017年3月29日 下午6:44:01
 * @since JDK 1.7
 */
public class ForcedCancelOrderRequest extends BaseCancelOrderRequest implements IdempotentSupport {
    
    private static final long serialVersionUID = 3384340851062215174L;
    /**
	 * @api {DUBBO} com.howbuy.tms.high.orders.facade.trade.cancelorder.forcedcancelorder.ForcedCancelOrderFacade.execute() 强制撤单接口
	 * @apiGroup high-order-center
	 * @apiDescription 强制撤单接口
	 * 
	 * @apiUse orderBaseRequest
	 * @apiUse baseCancelOrderRequest
	 * @apiUse orderTradeBaseRequest
	 * 
	 * @apiParamExample {json} Request Example
	 * dubbo com.howbuy.tms.high.orders.facade.trade.cancelorder.forcedcancelorder.ForcedCancelOrderRequest
	 * 
     * @apiUse orderBaseResponse
     * @apiUse orderTradeBaseResponse 
     * @apiUse orderTradeBaseResponse
	 * 
	 * @apiSuccessExample {json} Response Example
	 * dubbo com.howbuy.tms.high.orders.facade.trade.cancelorder.forcedcancelorder.ForcedCancelOrderResponse
	 * 
	 */
    public ForcedCancelOrderRequest() {
        this.setTxCode(TxCodes.HIGH_FUND_FORCED_CANCEL);
    }
    
    @Override
    public String toIdempotentString() {

        StringBuilder idempotent = new StringBuilder(50);
        idempotent.append(getDealNo());
        idempotent.append(getTxAcctNo());
        if (null != getExternalDealNo()) {
            idempotent.append(getExternalDealNo());
        } else {
            idempotent.append(getAppDt());
            idempotent.append(getShortAppTm());
        }

        return idempotent.toString();

    }
    
}
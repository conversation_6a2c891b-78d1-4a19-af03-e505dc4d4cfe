/**
 *Copyright (c) 2018, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.high.orders.facade.trade.sendauthmsg;

import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.high.orders.facade.common.OrderTradeBaseRequest;
import com.howbuy.tms.high.orders.facade.trade.sendauthmsg.bean.ConvenientVrifyForMobileBean;
import com.howbuy.tms.high.orders.facade.trade.sendauthmsg.bean.MessageCenterConterxtBean;
import com.howbuy.tms.high.orders.facade.trade.sendauthmsg.bean.QuickCardAuthContextBean;

import java.util.Set;

/**
 * @description:(发送鉴权信息请求) 
 * @reason:
 * <AUTHOR>
 * @date 2018年8月31日 下午9:02:14
 * @since JDK 1.6
 */
public class SendAuthMsgRequest extends OrderTradeBaseRequest{

	/**
	 * serialVersionUID:TODO（用一句话描述这个变量表示什么）
	 *
	 * @since Ver 1.1
	 */
	
	private static final long serialVersionUID = 5366511924226175958L;
	
	public SendAuthMsgRequest(){
		setTxCode(TxCodes.HIGH_SEND_AUTH_MSG);
	}
	
	/**
	 * 发送鉴权信息标识id
	 */
	private String sendMsgReqId;
	
	/**
	 * 发送鉴权信息类型
	 * 1-快捷鉴权
	 * 2-修改银行预留手机号
	 * 3-好买鉴权
	 * 4-e签宝鉴权
	 */
	private String authMsgType;
	
	/**
	 * 手机号是否存在
	 */
	private boolean mobileExit;

	/**
	 * 银行代码
	 **/
	private String bankCode;
	
	/**
	 * 快捷鉴权参数
	 * 
	 */
	private QuickCardAuthContextBean quickCardAuthContextBean;
	
	/**
	 *修改银行预留手机号
	 */
	private ConvenientVrifyForMobileBean convenientVrifyForMobileBean;

    /**
     * 好买发送鉴权信息参数
     */
	private MessageCenterConterxtBean messageCenterConterxtBean;

	/**
	 * 支持快捷鉴权
	 */
    private  boolean supportQuickAuth;

    private String idType;

    private String idNo;

    private String mobile;

    private String custName;

	public String getAuthMsgType() {
		return authMsgType;
	}

	public void setAuthMsgType(String authMsgType) {
		this.authMsgType = authMsgType;
	}

	public boolean isMobileExit() {
		return mobileExit;
	}

	public void setMobileExit(boolean mobileExit) {
		this.mobileExit = mobileExit;
	}

	public QuickCardAuthContextBean getQuickCardAuthContextBean() {
		return quickCardAuthContextBean;
	}

	public void setQuickCardAuthContextBean(
			QuickCardAuthContextBean quickCardAuthContextBean) {
		this.quickCardAuthContextBean = quickCardAuthContextBean;
	}

	public ConvenientVrifyForMobileBean getConvenientVrifyForMobileBean() {
		return convenientVrifyForMobileBean;
	}

	public void setConvenientVrifyForMobileBean(
			ConvenientVrifyForMobileBean convenientVrifyForMobileBean) {
		this.convenientVrifyForMobileBean = convenientVrifyForMobileBean;
	}

	public MessageCenterConterxtBean getMessageCenterConterxtBean() {
		return messageCenterConterxtBean;
	}

	public void setMessageCenterConterxtBean(
			MessageCenterConterxtBean messageCenterConterxtBean) {
		this.messageCenterConterxtBean = messageCenterConterxtBean;
	}

	public String getSendMsgReqId() {
		return sendMsgReqId;
	}

	public void setSendMsgReqId(String sendMsgReqId) {
		this.sendMsgReqId = sendMsgReqId;
	}

	public String getBankCode() {
		return bankCode;
	}

	public void setBankCode(String bankCode) {
		this.bankCode = bankCode;
	}

	public boolean isSupportQuickAuth() {
		return supportQuickAuth;
	}

	public void setSupportQuickAuth(boolean supportQuickAuth) {
		this.supportQuickAuth = supportQuickAuth;
	}

	public String getIdType() {
		return idType;
	}

	public void setIdType(String idType) {
		this.idType = idType;
	}

	public String getIdNo() {
		return idNo;
	}

	public void setIdNo(String idNo) {
		this.idNo = idNo;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getCustName() {
		return custName;
	}

	public void setCustName(String custName) {
		this.custName = custName;
	}
}



/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.trade.sharemerge.sharemergevalidate;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.validate.MyValidation;
import com.howbuy.tms.high.orders.facade.trade.sharemerge.BaseShareMergeRequest;

/**
 * 
 * @description:份额合并校验request
 * @reason:
 * <AUTHOR>
 * @date 2018年5月8日 下午2:07:33
 * @since JDK 1.6
 */
public class HighShareMergeValidateRequest extends BaseShareMergeRequest {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    private static final long serialVersionUID = 2935383430427896039L;

    public HighShareMergeValidateRequest() {
        setTxCode(TxCodes.QUERY_SHARE_MERGE_VALIDATE);
    }

    /**
     * 经办人证件号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "经办人证件号", isRequired = false, max = 32)
    private String transactorIdNo;
    /**
     * 经办人证件类型
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "经办人证件类型", isRequired = false, max = 1)
    private String transactorIdType;
    /**
     * 经办人姓名
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "经办人姓名", isRequired = false, max = 180)
    private String transactorName;
    /**
     * 操作员编号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "操作员编号", isRequired = true, max = 100)
    private String operatorNo;
    /**
     * 投资顾问代码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "投资顾问代码", isRequired = true, max = 100)
    private String consCode;

    public String getTransactorIdNo() {
        return transactorIdNo;
    }

    public void setTransactorIdNo(String transactorIdNo) {
        this.transactorIdNo = transactorIdNo;
    }

    public String getTransactorIdType() {
        return transactorIdType;
    }

    public void setTransactorIdType(String transactorIdType) {
        this.transactorIdType = transactorIdType;
    }

    public String getTransactorName() {
        return transactorName;
    }

    public void setTransactorName(String transactorName) {
        this.transactorName = transactorName;
    }

    public String getOperatorNo() {
        return operatorNo;
    }

    public void setOperatorNo(String operatorNo) {
        this.operatorNo = operatorNo;
    }

    public String getConsCode() {
        return consCode;
    }

    public void setConsCode(String consCode) {
        this.consCode = consCode;
    }

}

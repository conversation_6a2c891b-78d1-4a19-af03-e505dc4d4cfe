/**
 * Copyright (c) 2020, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.facade.trade.submitsupsignagreement;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.common.validate.MyValidation;
import com.howbuy.tms.high.orders.facade.common.OrderTradeBaseRequest;

import java.util.List;

/**
 * 签署待补签协议
 * <AUTHOR>
 * @date 2020/12/10 16:59
 * @since JDK 1.8
 */
public class SubmitSupSignAgreementRequest extends OrderTradeBaseRequest {

    private static final long serialVersionUID = 3519785762044247401L;

    /**
     * @api {post} dubbo:com.howbuy.tms.high.orders.facade.trade.submitsupsignagreement.SubmitSupSignAgreementFacade.execute() 签署待补签协议接口
     * @apiGroup high-order-center
     * @apiDescription 签署待补签协议接口
     *
     * @apiUse orderTradeBaseRequest
     * @apiUse orderBaseRequest
     * @apiParam {String} fundCode 基金代码
     * @apiParam {List} agreementCodeList 签署协议代码列表
     *
     * @apiParamExample {json} Request Example
     * dubbo com.howbuy.tms.high.orders.facade.trade.submitsupsignagreement.SubmitSupSignAgreementRequest
     *
     * @apiUse orderBaseResponse
     *
     * @apiSuccessExample {json} Response Example
     * dubbo com.howbuy.tms.high.orders.facade.trade.submitsupsignagreement.SubmitSupSignAgreementResponse
     * {
     *   "description": "成功",
     *   "returnCode": "Z0000000",
     * }
     *
     */
    public SubmitSupSignAgreementRequest(){
        setTxCode(TxCodes.SUBMIT_SUP_SIGN_AGREEMENT);
    }

    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金代码", isRequired = true, max = 6)
    private String fundCode;

    /**
     * 好臻产品,手写签名id
     */
    private String sealId;
    private List<String> agreementCodeList;

    public String getSealId() {
        return sealId;
    }

    public void setSealId(String sealId) {
        this.sealId = sealId;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public List<String> getAgreementCodeList() {
        return agreementCodeList;
    }

    public void setAgreementCodeList(List<String> agreementCodeList) {
        this.agreementCodeList = agreementCodeList;
    }
}
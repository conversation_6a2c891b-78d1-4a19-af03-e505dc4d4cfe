/**
 * Copyright (c) 2021, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.facade.trade.subsorpur;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.common.idempotent.IdempotentSupport;
import com.howbuy.tms.common.validate.MyValidation;
import com.howbuy.tms.high.orders.facade.common.OrderTradeBaseRequest;
import com.howbuy.tms.high.orders.facade.trade.subsorpur.bean.PayInfoBean;

import java.math.BigDecimal;
import java.util.List;

/**
 * 请在此添加描述
 *
 * <AUTHOR>
 * @date 2021/5/25 14:17
 * @since JDK 1.8
 */

/**
 * @apiDefine baseMergeSubsOrPurRequest 公募基金认申购共有请求参数(req)
 * @apiGroup high-order-center
 *
 * @apiParam {BigDecimal} appAmt 申请金额
 * @apiParam {BigDecimal} esitmateFee 预估手续费
 * @apiParam {String} riskFlag 风险确认标记<br> 1-确认；0-未确认
 * @apiParam {String} fundCode 基金代码
 * @apiParam {String} fundShareClass 份额类型<br> A-前收费；B-后收费
 * @apiParam {String} protocolType 协议类型<br> 1-普通公募协议；2-普通公募智能投顾协议；3-暴力定投协议；4-高端公募协议
 * @apiParam {String} [protocolNo] 协议号
 * @apiParam {String} appointmentDealNo 预约订单号
 * @apiParam {String} sealId 印章id
 * @apiParam {String} [transactorIdNo] 经办人证件号
 * @apiParam {String} [transactorIdType] 经办人证件类型
 * @apiParam {String} [transactorName] 经办人姓名
 * @apiParam {String} [operatorNo] 操作员编号
 * @apiParam {String} [consCode] 投资顾问代码
 * @apiParam {BigDecimal} [subsAmt] 认缴金额
 * @apiParam {list} payList 支付列表
 *
 * @apiParam (payList) {String} cpAcctNo 资金账号
 * @apiParam (payList) {String} paymentType 支付方式<br> 01-自划款；04-银行卡代扣；06-储蓄罐支付
 * @apiParam (payList) {BigDecimal} payAmt 支付金额
 * @apiParam (payList) {String} [subBankName] 支行名称
 *
 */
public class BaseMergeSubsOrPurRequest extends OrderTradeBaseRequest implements IdempotentSupport {

    private static final long serialVersionUID = 8945219819808252769L;

    /**
     * 申请金额
     */
    @MyValidation(validatorType = ValidatorTypeEnum.Money, fieldName = "申请金额", isRequired = true)
    private BigDecimal appAmt;
    /**
     * 预估手续费
     */
    private BigDecimal esitmateFee;
    /**
     * 风险确认标记：1-确认，0-未确认
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "风险确认标记", isRequired = true, max = 1)
    private String riskFlag = "0";
    /**
     * 基金代码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金代码", isRequired = true, max = 6)
    private String fundCode;
    /**
     * 份额类型：A-前收费；B-后收费
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "份额类型", isRequired = true, max = 1)
    private String fundShareClass;
    /**
     * 协议类型：1-普通公募协议；2-普通公募智能投顾协议；3-暴力定投协议；4-高端公募协议
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "协议类型", isRequired = true, max = 2)
    private String protocolType;
    /**
     * 协议号
     */
    private String protocolNo;
    /**
     * 预约订单号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "预约订单号", isRequired = true, max = 32)
    private String appointmentDealNo;
    /**
     * 经办人证件号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "经办人证件号", isRequired = false, max = 32)
    private String transactorIdNo;
    /**
     * 经办人证件类型
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "经办人证件类型", isRequired = false, max = 1)
    private String transactorIdType;
    /**
     * 经办人姓名
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "经办人姓名", isRequired = false, max = 180)
    private String transactorName;
    /**
     * 操作员编号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "操作员编号", isRequired = false, max = 100)
    private String operatorNo;
    /**
     * 投资顾问代码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "投资顾问代码", isRequired = false, max = 100)
    private String consCode;
    /**
     * 认缴金额
     */
    @MyValidation(validatorType = ValidatorTypeEnum.Money, fieldName = "认缴金额", isRequired = false)
    private BigDecimal subsAmt;
    /**
     * 支付列表
     */
    private List<PayInfoBean> payList;
    /**
     * 是否高端定投 0-否 1-是
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "是否高端定投", isRequired = false)
    private String highFundInvPlanFlag;
    /**
     * 是否高端定投第二次扣款 0-否 1-是
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "是否高端定投第二次扣款", isRequired = false)
    private String isHighFundInvPlanSecond;

    /**
     * 印章id
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "印章id", isRequired = false)
    private String sealId;

    /**
     * 补充表单单号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "补充表单单号", isRequired = false)
    private String formNo;

    /**
     * 服务主体
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "服务主体", isRequired = false)
    private String serviceEntityName;

    /**
     * 基金从业资格编号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金从业资格编号", isRequired = false)
    private String fpqcCode;

    /**
     * 风测提醒确定时间
     */
    private String riskHintConfirmDtm;

    /**
     * 投资者类型认证确认时间 yyyyMMddHHmmss
     */
    private String investorQualifiedConfirmDtm;

    public String getRiskHintConfirmDtm() {
        return riskHintConfirmDtm;
    }

    public void setRiskHintConfirmDtm(String riskHintConfirmDtm) {
        this.riskHintConfirmDtm = riskHintConfirmDtm;
    }

    public String getInvestorQualifiedConfirmDtm() {
        return investorQualifiedConfirmDtm;
    }

    public void setInvestorQualifiedConfirmDtm(String investorQualifiedConfirmDtm) {
        this.investorQualifiedConfirmDtm = investorQualifiedConfirmDtm;
    }

    public String getServiceEntityName() {
        return serviceEntityName;
    }

    public void setServiceEntityName(String serviceEntityName) {
        this.serviceEntityName = serviceEntityName;
    }

    public String getFpqcCode() {
        return fpqcCode;
    }

    public void setFpqcCode(String fpqcCode) {
        this.fpqcCode = fpqcCode;
    }
    public String getSealId() {
        return sealId;
    }

    public void setSealId(String sealId) {
        this.sealId = sealId;
    }

    public String getFormNo() {
        return formNo;
    }

    public void setFormNo(String formNo) {
        this.formNo = formNo;
    }

    public BigDecimal getAppAmt() {
        return appAmt;
    }

    public void setAppAmt(BigDecimal appAmt) {
        this.appAmt = appAmt;
    }

    public BigDecimal getEsitmateFee() {
        return esitmateFee;
    }

    public void setEsitmateFee(BigDecimal esitmateFee) {
        this.esitmateFee = esitmateFee;
    }

    public String getRiskFlag() {
        return riskFlag;
    }

    public void setRiskFlag(String riskFlag) {
        this.riskFlag = riskFlag;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundShareClass() {
        return fundShareClass;
    }

    public void setFundShareClass(String fundShareClass) {
        this.fundShareClass = fundShareClass;
    }

    public String getProtocolType() {
        return protocolType;
    }

    public void setProtocolType(String protocolType) {
        this.protocolType = protocolType;
    }

    public String getProtocolNo() {
        return protocolNo;
    }

    public void setProtocolNo(String protocolNo) {
        this.protocolNo = protocolNo;
    }

    public String getAppointmentDealNo() {
        return appointmentDealNo;
    }

    public void setAppointmentDealNo(String appointmentDealNo) {
        this.appointmentDealNo = appointmentDealNo;
    }

    public String getTransactorIdNo() {
        return transactorIdNo;
    }

    public void setTransactorIdNo(String transactorIdNo) {
        this.transactorIdNo = transactorIdNo;
    }

    public String getTransactorIdType() {
        return transactorIdType;
    }

    public void setTransactorIdType(String transactorIdType) {
        this.transactorIdType = transactorIdType;
    }

    public String getTransactorName() {
        return transactorName;
    }

    public void setTransactorName(String transactorName) {
        this.transactorName = transactorName;
    }

    public String getOperatorNo() {
        return operatorNo;
    }

    public void setOperatorNo(String operatorNo) {
        this.operatorNo = operatorNo;
    }

    public String getConsCode() {
        return consCode;
    }

    public void setConsCode(String consCode) {
        this.consCode = consCode;
    }

    public BigDecimal getSubsAmt() {
        return subsAmt;
    }

    public void setSubsAmt(BigDecimal subsAmt) {
        this.subsAmt = subsAmt;
    }

    public List<PayInfoBean> getPayList() {
        return payList;
    }

    public void setPayList(List<PayInfoBean> payList) {
        this.payList = payList;
    }

    public String getHighFundInvPlanFlag() {
        return highFundInvPlanFlag;
    }

    public void setHighFundInvPlanFlag(String highFundInvPlanFlag) {
        this.highFundInvPlanFlag = highFundInvPlanFlag;
    }

    public String getIsHighFundInvPlanSecond() {
        return isHighFundInvPlanSecond;
    }

    public void setIsHighFundInvPlanSecond(String isHighFundInvPlanSecond) {
        this.isHighFundInvPlanSecond = isHighFundInvPlanSecond;
    }

    @Override
    public String toIdempotentString() {
        StringBuilder idempotent = new StringBuilder(100);
        idempotent.append(getTxAcctNo());
        idempotent.append(getFundCode());
        idempotent.append(getProtocolType());
        idempotent.append(getAppAmt());
        idempotent.append(getRiskFlag());
        if (payList != null) {
            idempotent.append(payList.size());
        }
        if (null != getExternalDealNo()) {
            idempotent.append(getExternalDealNo());
        } else {
            idempotent.append(getAppDt());
            idempotent.append(getShortAppTm());
        }

        return idempotent.toString();
    }
}
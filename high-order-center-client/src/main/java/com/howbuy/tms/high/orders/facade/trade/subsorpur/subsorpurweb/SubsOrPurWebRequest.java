/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.high.orders.facade.trade.subsorpur.subsorpurweb;

import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.high.orders.facade.trade.subsorpur.BaseSubsOrPurRequest;

/**
 * @description:(网上个人客户基金认申购接口——请求参数)
 * @reason:
 * <AUTHOR>
 * @date 2017年4月1日 上午11:08:26
 * @since JDK 1.7
 */
public class SubsOrPurWebRequest extends BaseSubsOrPurRequest {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = -5971054226717614668L;

    /**
     * @api {dubbo} com.howbuy.tms.high.orders.facade.trade.subsorpur.subsorpurweb.SubsOrPurWebFacade.execute() 网上个人客户基金认申购接口
     * @apiGroup high-order-center
     * @apiDescription 网上个人客户基金认申购接口
     * 
     * @apiUse orderBaseRequest
     * @apiUse orderTradeBaseRequest
     * @apiUse baseSubsOrPurRequest
     * 
     * @apiParam {String} txPwd  交易密码  
     * 
     * @apiParamExample {json} Request Example
     * dubbo com.howbuy.tms.high.orders.facade.trade.subsorpur.subsorpurweb.SubsOrPurWebRequest
     * 
     * @apiUse orderBaseResponse
     * @apiUse orderTradeBaseResponse
     * @apiUse baseSubsOrPurResponse
     * 
     * @apiSuccessExample {json} Response Example
     * dubbo com.howbuy.tms.high.orders.facade.trade.subsorpur.subsorpurweb.SubsOrPurWebResponse
     * 
     */
    public SubsOrPurWebRequest() {
        setTxCode(TxCodes.HIGH_SUBSORPUR_WEB);
    }

    /**
     * 交易密码
     */
    private String txPwd;
    
    public String getTxPwd() {
        return txPwd;
    }

    public void setTxPwd(String txPwd) {
        this.txPwd = txPwd;
    }

}


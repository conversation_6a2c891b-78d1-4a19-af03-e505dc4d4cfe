package com.howbuy.tms.high.orders.facade.trade.validCustomerInfoStrongly;

import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.high.orders.facade.common.OrderTradeBaseRequest;
import lombok.Data;

/**
 * @Description:反洗钱强控入参
 * @Author: yun.lu
 * Date: 2023/11/27 13:18
 */
@Data
public class ValidCustomerInfoStronglyRequest extends OrderTradeBaseRequest {
    /**
     * ta编码
     */
    private String taCode;
    /**
     * 是否需要按照ta判断反洗钱强控,1:按照ta强控,0:不按照ta判断是否反洗钱强控
     */
    private String checkByTa;
    public ValidCustomerInfoStronglyRequest() {
        this.setTxCode(TxCodes.VALID_CUSTOMER_INFO_STRONGLY);
    }
}

package com.howbuy.tms.high.orders.dao.po;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CmCusttradeDirectTmpPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public CmCusttradeDirectTmpPoExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAppserialnoIsNull() {
            addCriterion("appserialno is null");
            return (Criteria) this;
        }

        public Criteria andAppserialnoIsNotNull() {
            addCriterion("appserialno is not null");
            return (Criteria) this;
        }

        public Criteria andAppserialnoEqualTo(String value) {
            addCriterion("appserialno =", value, "appserialno");
            return (Criteria) this;
        }

        public Criteria andAppserialnoNotEqualTo(String value) {
            addCriterion("appserialno <>", value, "appserialno");
            return (Criteria) this;
        }

        public Criteria andAppserialnoGreaterThan(String value) {
            addCriterion("appserialno >", value, "appserialno");
            return (Criteria) this;
        }

        public Criteria andAppserialnoGreaterThanOrEqualTo(String value) {
            addCriterion("appserialno >=", value, "appserialno");
            return (Criteria) this;
        }

        public Criteria andAppserialnoLessThan(String value) {
            addCriterion("appserialno <", value, "appserialno");
            return (Criteria) this;
        }

        public Criteria andAppserialnoLessThanOrEqualTo(String value) {
            addCriterion("appserialno <=", value, "appserialno");
            return (Criteria) this;
        }

        public Criteria andAppserialnoLike(String value) {
            addCriterion("appserialno like", value, "appserialno");
            return (Criteria) this;
        }

        public Criteria andAppserialnoNotLike(String value) {
            addCriterion("appserialno not like", value, "appserialno");
            return (Criteria) this;
        }

        public Criteria andAppserialnoIn(List<String> values) {
            addCriterion("appserialno in", values, "appserialno");
            return (Criteria) this;
        }

        public Criteria andAppserialnoNotIn(List<String> values) {
            addCriterion("appserialno not in", values, "appserialno");
            return (Criteria) this;
        }

        public Criteria andAppserialnoBetween(String value1, String value2) {
            addCriterion("appserialno between", value1, value2, "appserialno");
            return (Criteria) this;
        }

        public Criteria andAppserialnoNotBetween(String value1, String value2) {
            addCriterion("appserialno not between", value1, value2, "appserialno");
            return (Criteria) this;
        }

        public Criteria andTradedtIsNull() {
            addCriterion("tradedt is null");
            return (Criteria) this;
        }

        public Criteria andTradedtIsNotNull() {
            addCriterion("tradedt is not null");
            return (Criteria) this;
        }

        public Criteria andTradedtEqualTo(String value) {
            addCriterion("tradedt =", value, "tradedt");
            return (Criteria) this;
        }

        public Criteria andTradedtNotEqualTo(String value) {
            addCriterion("tradedt <>", value, "tradedt");
            return (Criteria) this;
        }

        public Criteria andTradedtGreaterThan(String value) {
            addCriterion("tradedt >", value, "tradedt");
            return (Criteria) this;
        }

        public Criteria andTradedtGreaterThanOrEqualTo(String value) {
            addCriterion("tradedt >=", value, "tradedt");
            return (Criteria) this;
        }

        public Criteria andTradedtLessThan(String value) {
            addCriterion("tradedt <", value, "tradedt");
            return (Criteria) this;
        }

        public Criteria andTradedtLessThanOrEqualTo(String value) {
            addCriterion("tradedt <=", value, "tradedt");
            return (Criteria) this;
        }

        public Criteria andTradedtLike(String value) {
            addCriterion("tradedt like", value, "tradedt");
            return (Criteria) this;
        }

        public Criteria andTradedtNotLike(String value) {
            addCriterion("tradedt not like", value, "tradedt");
            return (Criteria) this;
        }

        public Criteria andTradedtIn(List<String> values) {
            addCriterion("tradedt in", values, "tradedt");
            return (Criteria) this;
        }

        public Criteria andTradedtNotIn(List<String> values) {
            addCriterion("tradedt not in", values, "tradedt");
            return (Criteria) this;
        }

        public Criteria andTradedtBetween(String value1, String value2) {
            addCriterion("tradedt between", value1, value2, "tradedt");
            return (Criteria) this;
        }

        public Criteria andTradedtNotBetween(String value1, String value2) {
            addCriterion("tradedt not between", value1, value2, "tradedt");
            return (Criteria) this;
        }

        public Criteria andHbonenoIsNull() {
            addCriterion("hboneno is null");
            return (Criteria) this;
        }

        public Criteria andHbonenoIsNotNull() {
            addCriterion("hboneno is not null");
            return (Criteria) this;
        }

        public Criteria andHbonenoEqualTo(String value) {
            addCriterion("hboneno =", value, "hboneno");
            return (Criteria) this;
        }

        public Criteria andHbonenoNotEqualTo(String value) {
            addCriterion("hboneno <>", value, "hboneno");
            return (Criteria) this;
        }

        public Criteria andHbonenoGreaterThan(String value) {
            addCriterion("hboneno >", value, "hboneno");
            return (Criteria) this;
        }

        public Criteria andHbonenoGreaterThanOrEqualTo(String value) {
            addCriterion("hboneno >=", value, "hboneno");
            return (Criteria) this;
        }

        public Criteria andHbonenoLessThan(String value) {
            addCriterion("hboneno <", value, "hboneno");
            return (Criteria) this;
        }

        public Criteria andHbonenoLessThanOrEqualTo(String value) {
            addCriterion("hboneno <=", value, "hboneno");
            return (Criteria) this;
        }

        public Criteria andHbonenoLike(String value) {
            addCriterion("hboneno like", value, "hboneno");
            return (Criteria) this;
        }

        public Criteria andHbonenoNotLike(String value) {
            addCriterion("hboneno not like", value, "hboneno");
            return (Criteria) this;
        }

        public Criteria andHbonenoIn(List<String> values) {
            addCriterion("hboneno in", values, "hboneno");
            return (Criteria) this;
        }

        public Criteria andHbonenoNotIn(List<String> values) {
            addCriterion("hboneno not in", values, "hboneno");
            return (Criteria) this;
        }

        public Criteria andHbonenoBetween(String value1, String value2) {
            addCriterion("hboneno between", value1, value2, "hboneno");
            return (Criteria) this;
        }

        public Criteria andHbonenoNotBetween(String value1, String value2) {
            addCriterion("hboneno not between", value1, value2, "hboneno");
            return (Criteria) this;
        }

        public Criteria andFundcodeIsNull() {
            addCriterion("fundcode is null");
            return (Criteria) this;
        }

        public Criteria andFundcodeIsNotNull() {
            addCriterion("fundcode is not null");
            return (Criteria) this;
        }

        public Criteria andFundcodeEqualTo(String value) {
            addCriterion("fundcode =", value, "fundcode");
            return (Criteria) this;
        }

        public Criteria andFundcodeNotEqualTo(String value) {
            addCriterion("fundcode <>", value, "fundcode");
            return (Criteria) this;
        }

        public Criteria andFundcodeGreaterThan(String value) {
            addCriterion("fundcode >", value, "fundcode");
            return (Criteria) this;
        }

        public Criteria andFundcodeGreaterThanOrEqualTo(String value) {
            addCriterion("fundcode >=", value, "fundcode");
            return (Criteria) this;
        }

        public Criteria andFundcodeLessThan(String value) {
            addCriterion("fundcode <", value, "fundcode");
            return (Criteria) this;
        }

        public Criteria andFundcodeLessThanOrEqualTo(String value) {
            addCriterion("fundcode <=", value, "fundcode");
            return (Criteria) this;
        }

        public Criteria andFundcodeLike(String value) {
            addCriterion("fundcode like", value, "fundcode");
            return (Criteria) this;
        }

        public Criteria andFundcodeNotLike(String value) {
            addCriterion("fundcode not like", value, "fundcode");
            return (Criteria) this;
        }

        public Criteria andFundcodeIn(List<String> values) {
            addCriterion("fundcode in", values, "fundcode");
            return (Criteria) this;
        }

        public Criteria andFundcodeNotIn(List<String> values) {
            addCriterion("fundcode not in", values, "fundcode");
            return (Criteria) this;
        }

        public Criteria andFundcodeBetween(String value1, String value2) {
            addCriterion("fundcode between", value1, value2, "fundcode");
            return (Criteria) this;
        }

        public Criteria andFundcodeNotBetween(String value1, String value2) {
            addCriterion("fundcode not between", value1, value2, "fundcode");
            return (Criteria) this;
        }

        public Criteria andFundnameIsNull() {
            addCriterion("fundname is null");
            return (Criteria) this;
        }

        public Criteria andFundnameIsNotNull() {
            addCriterion("fundname is not null");
            return (Criteria) this;
        }

        public Criteria andFundnameEqualTo(String value) {
            addCriterion("fundname =", value, "fundname");
            return (Criteria) this;
        }

        public Criteria andFundnameNotEqualTo(String value) {
            addCriterion("fundname <>", value, "fundname");
            return (Criteria) this;
        }

        public Criteria andFundnameGreaterThan(String value) {
            addCriterion("fundname >", value, "fundname");
            return (Criteria) this;
        }

        public Criteria andFundnameGreaterThanOrEqualTo(String value) {
            addCriterion("fundname >=", value, "fundname");
            return (Criteria) this;
        }

        public Criteria andFundnameLessThan(String value) {
            addCriterion("fundname <", value, "fundname");
            return (Criteria) this;
        }

        public Criteria andFundnameLessThanOrEqualTo(String value) {
            addCriterion("fundname <=", value, "fundname");
            return (Criteria) this;
        }

        public Criteria andFundnameLike(String value) {
            addCriterion("fundname like", value, "fundname");
            return (Criteria) this;
        }

        public Criteria andFundnameNotLike(String value) {
            addCriterion("fundname not like", value, "fundname");
            return (Criteria) this;
        }

        public Criteria andFundnameIn(List<String> values) {
            addCriterion("fundname in", values, "fundname");
            return (Criteria) this;
        }

        public Criteria andFundnameNotIn(List<String> values) {
            addCriterion("fundname not in", values, "fundname");
            return (Criteria) this;
        }

        public Criteria andFundnameBetween(String value1, String value2) {
            addCriterion("fundname between", value1, value2, "fundname");
            return (Criteria) this;
        }

        public Criteria andFundnameNotBetween(String value1, String value2) {
            addCriterion("fundname not between", value1, value2, "fundname");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNull() {
            addCriterion("currency is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNotNull() {
            addCriterion("currency is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyEqualTo(String value) {
            addCriterion("currency =", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotEqualTo(String value) {
            addCriterion("currency <>", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThan(String value) {
            addCriterion("currency >", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThanOrEqualTo(String value) {
            addCriterion("currency >=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThan(String value) {
            addCriterion("currency <", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThanOrEqualTo(String value) {
            addCriterion("currency <=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLike(String value) {
            addCriterion("currency like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotLike(String value) {
            addCriterion("currency not like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyIn(List<String> values) {
            addCriterion("currency in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotIn(List<String> values) {
            addCriterion("currency not in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyBetween(String value1, String value2) {
            addCriterion("currency between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotBetween(String value1, String value2) {
            addCriterion("currency not between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andBusicodeIsNull() {
            addCriterion("busicode is null");
            return (Criteria) this;
        }

        public Criteria andBusicodeIsNotNull() {
            addCriterion("busicode is not null");
            return (Criteria) this;
        }

        public Criteria andBusicodeEqualTo(String value) {
            addCriterion("busicode =", value, "busicode");
            return (Criteria) this;
        }

        public Criteria andBusicodeNotEqualTo(String value) {
            addCriterion("busicode <>", value, "busicode");
            return (Criteria) this;
        }

        public Criteria andBusicodeGreaterThan(String value) {
            addCriterion("busicode >", value, "busicode");
            return (Criteria) this;
        }

        public Criteria andBusicodeGreaterThanOrEqualTo(String value) {
            addCriterion("busicode >=", value, "busicode");
            return (Criteria) this;
        }

        public Criteria andBusicodeLessThan(String value) {
            addCriterion("busicode <", value, "busicode");
            return (Criteria) this;
        }

        public Criteria andBusicodeLessThanOrEqualTo(String value) {
            addCriterion("busicode <=", value, "busicode");
            return (Criteria) this;
        }

        public Criteria andBusicodeLike(String value) {
            addCriterion("busicode like", value, "busicode");
            return (Criteria) this;
        }

        public Criteria andBusicodeNotLike(String value) {
            addCriterion("busicode not like", value, "busicode");
            return (Criteria) this;
        }

        public Criteria andBusicodeIn(List<String> values) {
            addCriterion("busicode in", values, "busicode");
            return (Criteria) this;
        }

        public Criteria andBusicodeNotIn(List<String> values) {
            addCriterion("busicode not in", values, "busicode");
            return (Criteria) this;
        }

        public Criteria andBusicodeBetween(String value1, String value2) {
            addCriterion("busicode between", value1, value2, "busicode");
            return (Criteria) this;
        }

        public Criteria andBusicodeNotBetween(String value1, String value2) {
            addCriterion("busicode not between", value1, value2, "busicode");
            return (Criteria) this;
        }

        public Criteria andAppamtIsNull() {
            addCriterion("appamt is null");
            return (Criteria) this;
        }

        public Criteria andAppamtIsNotNull() {
            addCriterion("appamt is not null");
            return (Criteria) this;
        }

        public Criteria andAppamtEqualTo(BigDecimal value) {
            addCriterion("appamt =", value, "appamt");
            return (Criteria) this;
        }

        public Criteria andAppamtNotEqualTo(BigDecimal value) {
            addCriterion("appamt <>", value, "appamt");
            return (Criteria) this;
        }

        public Criteria andAppamtGreaterThan(BigDecimal value) {
            addCriterion("appamt >", value, "appamt");
            return (Criteria) this;
        }

        public Criteria andAppamtGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("appamt >=", value, "appamt");
            return (Criteria) this;
        }

        public Criteria andAppamtLessThan(BigDecimal value) {
            addCriterion("appamt <", value, "appamt");
            return (Criteria) this;
        }

        public Criteria andAppamtLessThanOrEqualTo(BigDecimal value) {
            addCriterion("appamt <=", value, "appamt");
            return (Criteria) this;
        }

        public Criteria andAppamtIn(List<BigDecimal> values) {
            addCriterion("appamt in", values, "appamt");
            return (Criteria) this;
        }

        public Criteria andAppamtNotIn(List<BigDecimal> values) {
            addCriterion("appamt not in", values, "appamt");
            return (Criteria) this;
        }

        public Criteria andAppamtBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("appamt between", value1, value2, "appamt");
            return (Criteria) this;
        }

        public Criteria andAppamtNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("appamt not between", value1, value2, "appamt");
            return (Criteria) this;
        }

        public Criteria andAppvolIsNull() {
            addCriterion("appvol is null");
            return (Criteria) this;
        }

        public Criteria andAppvolIsNotNull() {
            addCriterion("appvol is not null");
            return (Criteria) this;
        }

        public Criteria andAppvolEqualTo(BigDecimal value) {
            addCriterion("appvol =", value, "appvol");
            return (Criteria) this;
        }

        public Criteria andAppvolNotEqualTo(BigDecimal value) {
            addCriterion("appvol <>", value, "appvol");
            return (Criteria) this;
        }

        public Criteria andAppvolGreaterThan(BigDecimal value) {
            addCriterion("appvol >", value, "appvol");
            return (Criteria) this;
        }

        public Criteria andAppvolGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("appvol >=", value, "appvol");
            return (Criteria) this;
        }

        public Criteria andAppvolLessThan(BigDecimal value) {
            addCriterion("appvol <", value, "appvol");
            return (Criteria) this;
        }

        public Criteria andAppvolLessThanOrEqualTo(BigDecimal value) {
            addCriterion("appvol <=", value, "appvol");
            return (Criteria) this;
        }

        public Criteria andAppvolIn(List<BigDecimal> values) {
            addCriterion("appvol in", values, "appvol");
            return (Criteria) this;
        }

        public Criteria andAppvolNotIn(List<BigDecimal> values) {
            addCriterion("appvol not in", values, "appvol");
            return (Criteria) this;
        }

        public Criteria andAppvolBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("appvol between", value1, value2, "appvol");
            return (Criteria) this;
        }

        public Criteria andAppvolNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("appvol not between", value1, value2, "appvol");
            return (Criteria) this;
        }

        public Criteria andAckamtIsNull() {
            addCriterion("ackamt is null");
            return (Criteria) this;
        }

        public Criteria andAckamtIsNotNull() {
            addCriterion("ackamt is not null");
            return (Criteria) this;
        }

        public Criteria andAckamtEqualTo(BigDecimal value) {
            addCriterion("ackamt =", value, "ackamt");
            return (Criteria) this;
        }

        public Criteria andAckamtNotEqualTo(BigDecimal value) {
            addCriterion("ackamt <>", value, "ackamt");
            return (Criteria) this;
        }

        public Criteria andAckamtGreaterThan(BigDecimal value) {
            addCriterion("ackamt >", value, "ackamt");
            return (Criteria) this;
        }

        public Criteria andAckamtGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("ackamt >=", value, "ackamt");
            return (Criteria) this;
        }

        public Criteria andAckamtLessThan(BigDecimal value) {
            addCriterion("ackamt <", value, "ackamt");
            return (Criteria) this;
        }

        public Criteria andAckamtLessThanOrEqualTo(BigDecimal value) {
            addCriterion("ackamt <=", value, "ackamt");
            return (Criteria) this;
        }

        public Criteria andAckamtIn(List<BigDecimal> values) {
            addCriterion("ackamt in", values, "ackamt");
            return (Criteria) this;
        }

        public Criteria andAckamtNotIn(List<BigDecimal> values) {
            addCriterion("ackamt not in", values, "ackamt");
            return (Criteria) this;
        }

        public Criteria andAckamtBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ackamt between", value1, value2, "ackamt");
            return (Criteria) this;
        }

        public Criteria andAckamtNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ackamt not between", value1, value2, "ackamt");
            return (Criteria) this;
        }

        public Criteria andAckvolIsNull() {
            addCriterion("ackvol is null");
            return (Criteria) this;
        }

        public Criteria andAckvolIsNotNull() {
            addCriterion("ackvol is not null");
            return (Criteria) this;
        }

        public Criteria andAckvolEqualTo(BigDecimal value) {
            addCriterion("ackvol =", value, "ackvol");
            return (Criteria) this;
        }

        public Criteria andAckvolNotEqualTo(BigDecimal value) {
            addCriterion("ackvol <>", value, "ackvol");
            return (Criteria) this;
        }

        public Criteria andAckvolGreaterThan(BigDecimal value) {
            addCriterion("ackvol >", value, "ackvol");
            return (Criteria) this;
        }

        public Criteria andAckvolGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("ackvol >=", value, "ackvol");
            return (Criteria) this;
        }

        public Criteria andAckvolLessThan(BigDecimal value) {
            addCriterion("ackvol <", value, "ackvol");
            return (Criteria) this;
        }

        public Criteria andAckvolLessThanOrEqualTo(BigDecimal value) {
            addCriterion("ackvol <=", value, "ackvol");
            return (Criteria) this;
        }

        public Criteria andAckvolIn(List<BigDecimal> values) {
            addCriterion("ackvol in", values, "ackvol");
            return (Criteria) this;
        }

        public Criteria andAckvolNotIn(List<BigDecimal> values) {
            addCriterion("ackvol not in", values, "ackvol");
            return (Criteria) this;
        }

        public Criteria andAckvolBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ackvol between", value1, value2, "ackvol");
            return (Criteria) this;
        }

        public Criteria andAckvolNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ackvol not between", value1, value2, "ackvol");
            return (Criteria) this;
        }

        public Criteria andDiscrateofcommIsNull() {
            addCriterion("discrateofcomm is null");
            return (Criteria) this;
        }

        public Criteria andDiscrateofcommIsNotNull() {
            addCriterion("discrateofcomm is not null");
            return (Criteria) this;
        }

        public Criteria andDiscrateofcommEqualTo(BigDecimal value) {
            addCriterion("discrateofcomm =", value, "discrateofcomm");
            return (Criteria) this;
        }

        public Criteria andDiscrateofcommNotEqualTo(BigDecimal value) {
            addCriterion("discrateofcomm <>", value, "discrateofcomm");
            return (Criteria) this;
        }

        public Criteria andDiscrateofcommGreaterThan(BigDecimal value) {
            addCriterion("discrateofcomm >", value, "discrateofcomm");
            return (Criteria) this;
        }

        public Criteria andDiscrateofcommGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("discrateofcomm >=", value, "discrateofcomm");
            return (Criteria) this;
        }

        public Criteria andDiscrateofcommLessThan(BigDecimal value) {
            addCriterion("discrateofcomm <", value, "discrateofcomm");
            return (Criteria) this;
        }

        public Criteria andDiscrateofcommLessThanOrEqualTo(BigDecimal value) {
            addCriterion("discrateofcomm <=", value, "discrateofcomm");
            return (Criteria) this;
        }

        public Criteria andDiscrateofcommIn(List<BigDecimal> values) {
            addCriterion("discrateofcomm in", values, "discrateofcomm");
            return (Criteria) this;
        }

        public Criteria andDiscrateofcommNotIn(List<BigDecimal> values) {
            addCriterion("discrateofcomm not in", values, "discrateofcomm");
            return (Criteria) this;
        }

        public Criteria andDiscrateofcommBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("discrateofcomm between", value1, value2, "discrateofcomm");
            return (Criteria) this;
        }

        public Criteria andDiscrateofcommNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("discrateofcomm not between", value1, value2, "discrateofcomm");
            return (Criteria) this;
        }

        public Criteria andNavIsNull() {
            addCriterion("nav is null");
            return (Criteria) this;
        }

        public Criteria andNavIsNotNull() {
            addCriterion("nav is not null");
            return (Criteria) this;
        }

        public Criteria andNavEqualTo(BigDecimal value) {
            addCriterion("nav =", value, "nav");
            return (Criteria) this;
        }

        public Criteria andNavNotEqualTo(BigDecimal value) {
            addCriterion("nav <>", value, "nav");
            return (Criteria) this;
        }

        public Criteria andNavGreaterThan(BigDecimal value) {
            addCriterion("nav >", value, "nav");
            return (Criteria) this;
        }

        public Criteria andNavGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("nav >=", value, "nav");
            return (Criteria) this;
        }

        public Criteria andNavLessThan(BigDecimal value) {
            addCriterion("nav <", value, "nav");
            return (Criteria) this;
        }

        public Criteria andNavLessThanOrEqualTo(BigDecimal value) {
            addCriterion("nav <=", value, "nav");
            return (Criteria) this;
        }

        public Criteria andNavIn(List<BigDecimal> values) {
            addCriterion("nav in", values, "nav");
            return (Criteria) this;
        }

        public Criteria andNavNotIn(List<BigDecimal> values) {
            addCriterion("nav not in", values, "nav");
            return (Criteria) this;
        }

        public Criteria andNavBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("nav between", value1, value2, "nav");
            return (Criteria) this;
        }

        public Criteria andNavNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("nav not between", value1, value2, "nav");
            return (Criteria) this;
        }

        public Criteria andDivmodeIsNull() {
            addCriterion("divmode is null");
            return (Criteria) this;
        }

        public Criteria andDivmodeIsNotNull() {
            addCriterion("divmode is not null");
            return (Criteria) this;
        }

        public Criteria andDivmodeEqualTo(String value) {
            addCriterion("divmode =", value, "divmode");
            return (Criteria) this;
        }

        public Criteria andDivmodeNotEqualTo(String value) {
            addCriterion("divmode <>", value, "divmode");
            return (Criteria) this;
        }

        public Criteria andDivmodeGreaterThan(String value) {
            addCriterion("divmode >", value, "divmode");
            return (Criteria) this;
        }

        public Criteria andDivmodeGreaterThanOrEqualTo(String value) {
            addCriterion("divmode >=", value, "divmode");
            return (Criteria) this;
        }

        public Criteria andDivmodeLessThan(String value) {
            addCriterion("divmode <", value, "divmode");
            return (Criteria) this;
        }

        public Criteria andDivmodeLessThanOrEqualTo(String value) {
            addCriterion("divmode <=", value, "divmode");
            return (Criteria) this;
        }

        public Criteria andDivmodeLike(String value) {
            addCriterion("divmode like", value, "divmode");
            return (Criteria) this;
        }

        public Criteria andDivmodeNotLike(String value) {
            addCriterion("divmode not like", value, "divmode");
            return (Criteria) this;
        }

        public Criteria andDivmodeIn(List<String> values) {
            addCriterion("divmode in", values, "divmode");
            return (Criteria) this;
        }

        public Criteria andDivmodeNotIn(List<String> values) {
            addCriterion("divmode not in", values, "divmode");
            return (Criteria) this;
        }

        public Criteria andDivmodeBetween(String value1, String value2) {
            addCriterion("divmode between", value1, value2, "divmode");
            return (Criteria) this;
        }

        public Criteria andDivmodeNotBetween(String value1, String value2) {
            addCriterion("divmode not between", value1, value2, "divmode");
            return (Criteria) this;
        }

        public Criteria andFeeIsNull() {
            addCriterion("fee is null");
            return (Criteria) this;
        }

        public Criteria andFeeIsNotNull() {
            addCriterion("fee is not null");
            return (Criteria) this;
        }

        public Criteria andFeeEqualTo(BigDecimal value) {
            addCriterion("fee =", value, "fee");
            return (Criteria) this;
        }

        public Criteria andFeeNotEqualTo(BigDecimal value) {
            addCriterion("fee <>", value, "fee");
            return (Criteria) this;
        }

        public Criteria andFeeGreaterThan(BigDecimal value) {
            addCriterion("fee >", value, "fee");
            return (Criteria) this;
        }

        public Criteria andFeeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("fee >=", value, "fee");
            return (Criteria) this;
        }

        public Criteria andFeeLessThan(BigDecimal value) {
            addCriterion("fee <", value, "fee");
            return (Criteria) this;
        }

        public Criteria andFeeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("fee <=", value, "fee");
            return (Criteria) this;
        }

        public Criteria andFeeIn(List<BigDecimal> values) {
            addCriterion("fee in", values, "fee");
            return (Criteria) this;
        }

        public Criteria andFeeNotIn(List<BigDecimal> values) {
            addCriterion("fee not in", values, "fee");
            return (Criteria) this;
        }

        public Criteria andFeeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("fee between", value1, value2, "fee");
            return (Criteria) this;
        }

        public Criteria andFeeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("fee not between", value1, value2, "fee");
            return (Criteria) this;
        }

        public Criteria andApplyAppserialnoIsNull() {
            addCriterion("apply_appserialno is null");
            return (Criteria) this;
        }

        public Criteria andApplyAppserialnoIsNotNull() {
            addCriterion("apply_appserialno is not null");
            return (Criteria) this;
        }

        public Criteria andApplyAppserialnoEqualTo(String value) {
            addCriterion("apply_appserialno =", value, "applyAppserialno");
            return (Criteria) this;
        }

        public Criteria andApplyAppserialnoNotEqualTo(String value) {
            addCriterion("apply_appserialno <>", value, "applyAppserialno");
            return (Criteria) this;
        }

        public Criteria andApplyAppserialnoGreaterThan(String value) {
            addCriterion("apply_appserialno >", value, "applyAppserialno");
            return (Criteria) this;
        }

        public Criteria andApplyAppserialnoGreaterThanOrEqualTo(String value) {
            addCriterion("apply_appserialno >=", value, "applyAppserialno");
            return (Criteria) this;
        }

        public Criteria andApplyAppserialnoLessThan(String value) {
            addCriterion("apply_appserialno <", value, "applyAppserialno");
            return (Criteria) this;
        }

        public Criteria andApplyAppserialnoLessThanOrEqualTo(String value) {
            addCriterion("apply_appserialno <=", value, "applyAppserialno");
            return (Criteria) this;
        }

        public Criteria andApplyAppserialnoLike(String value) {
            addCriterion("apply_appserialno like", value, "applyAppserialno");
            return (Criteria) this;
        }

        public Criteria andApplyAppserialnoNotLike(String value) {
            addCriterion("apply_appserialno not like", value, "applyAppserialno");
            return (Criteria) this;
        }

        public Criteria andApplyAppserialnoIn(List<String> values) {
            addCriterion("apply_appserialno in", values, "applyAppserialno");
            return (Criteria) this;
        }

        public Criteria andApplyAppserialnoNotIn(List<String> values) {
            addCriterion("apply_appserialno not in", values, "applyAppserialno");
            return (Criteria) this;
        }

        public Criteria andApplyAppserialnoBetween(String value1, String value2) {
            addCriterion("apply_appserialno between", value1, value2, "applyAppserialno");
            return (Criteria) this;
        }

        public Criteria andApplyAppserialnoNotBetween(String value1, String value2) {
            addCriterion("apply_appserialno not between", value1, value2, "applyAppserialno");
            return (Criteria) this;
        }

        public Criteria andDiscodeIsNull() {
            addCriterion("discode is null");
            return (Criteria) this;
        }

        public Criteria andDiscodeIsNotNull() {
            addCriterion("discode is not null");
            return (Criteria) this;
        }

        public Criteria andDiscodeEqualTo(String value) {
            addCriterion("discode =", value, "discode");
            return (Criteria) this;
        }

        public Criteria andDiscodeNotEqualTo(String value) {
            addCriterion("discode <>", value, "discode");
            return (Criteria) this;
        }

        public Criteria andDiscodeGreaterThan(String value) {
            addCriterion("discode >", value, "discode");
            return (Criteria) this;
        }

        public Criteria andDiscodeGreaterThanOrEqualTo(String value) {
            addCriterion("discode >=", value, "discode");
            return (Criteria) this;
        }

        public Criteria andDiscodeLessThan(String value) {
            addCriterion("discode <", value, "discode");
            return (Criteria) this;
        }

        public Criteria andDiscodeLessThanOrEqualTo(String value) {
            addCriterion("discode <=", value, "discode");
            return (Criteria) this;
        }

        public Criteria andDiscodeLike(String value) {
            addCriterion("discode like", value, "discode");
            return (Criteria) this;
        }

        public Criteria andDiscodeNotLike(String value) {
            addCriterion("discode not like", value, "discode");
            return (Criteria) this;
        }

        public Criteria andDiscodeIn(List<String> values) {
            addCriterion("discode in", values, "discode");
            return (Criteria) this;
        }

        public Criteria andDiscodeNotIn(List<String> values) {
            addCriterion("discode not in", values, "discode");
            return (Criteria) this;
        }

        public Criteria andDiscodeBetween(String value1, String value2) {
            addCriterion("discode between", value1, value2, "discode");
            return (Criteria) this;
        }

        public Criteria andDiscodeNotBetween(String value1, String value2) {
            addCriterion("discode not between", value1, value2, "discode");
            return (Criteria) this;
        }

        public Criteria andCredtIsNull() {
            addCriterion("credt is null");
            return (Criteria) this;
        }

        public Criteria andCredtIsNotNull() {
            addCriterion("credt is not null");
            return (Criteria) this;
        }

        public Criteria andCredtEqualTo(String value) {
            addCriterion("credt =", value, "credt");
            return (Criteria) this;
        }

        public Criteria andCredtNotEqualTo(String value) {
            addCriterion("credt <>", value, "credt");
            return (Criteria) this;
        }

        public Criteria andCredtGreaterThan(String value) {
            addCriterion("credt >", value, "credt");
            return (Criteria) this;
        }

        public Criteria andCredtGreaterThanOrEqualTo(String value) {
            addCriterion("credt >=", value, "credt");
            return (Criteria) this;
        }

        public Criteria andCredtLessThan(String value) {
            addCriterion("credt <", value, "credt");
            return (Criteria) this;
        }

        public Criteria andCredtLessThanOrEqualTo(String value) {
            addCriterion("credt <=", value, "credt");
            return (Criteria) this;
        }

        public Criteria andCredtLike(String value) {
            addCriterion("credt like", value, "credt");
            return (Criteria) this;
        }

        public Criteria andCredtNotLike(String value) {
            addCriterion("credt not like", value, "credt");
            return (Criteria) this;
        }

        public Criteria andCredtIn(List<String> values) {
            addCriterion("credt in", values, "credt");
            return (Criteria) this;
        }

        public Criteria andCredtNotIn(List<String> values) {
            addCriterion("credt not in", values, "credt");
            return (Criteria) this;
        }

        public Criteria andCredtBetween(String value1, String value2) {
            addCriterion("credt between", value1, value2, "credt");
            return (Criteria) this;
        }

        public Criteria andCredtNotBetween(String value1, String value2) {
            addCriterion("credt not between", value1, value2, "credt");
            return (Criteria) this;
        }

        public Criteria andModdtIsNull() {
            addCriterion("moddt is null");
            return (Criteria) this;
        }

        public Criteria andModdtIsNotNull() {
            addCriterion("moddt is not null");
            return (Criteria) this;
        }

        public Criteria andModdtEqualTo(String value) {
            addCriterion("moddt =", value, "moddt");
            return (Criteria) this;
        }

        public Criteria andModdtNotEqualTo(String value) {
            addCriterion("moddt <>", value, "moddt");
            return (Criteria) this;
        }

        public Criteria andModdtGreaterThan(String value) {
            addCriterion("moddt >", value, "moddt");
            return (Criteria) this;
        }

        public Criteria andModdtGreaterThanOrEqualTo(String value) {
            addCriterion("moddt >=", value, "moddt");
            return (Criteria) this;
        }

        public Criteria andModdtLessThan(String value) {
            addCriterion("moddt <", value, "moddt");
            return (Criteria) this;
        }

        public Criteria andModdtLessThanOrEqualTo(String value) {
            addCriterion("moddt <=", value, "moddt");
            return (Criteria) this;
        }

        public Criteria andModdtLike(String value) {
            addCriterion("moddt like", value, "moddt");
            return (Criteria) this;
        }

        public Criteria andModdtNotLike(String value) {
            addCriterion("moddt not like", value, "moddt");
            return (Criteria) this;
        }

        public Criteria andModdtIn(List<String> values) {
            addCriterion("moddt in", values, "moddt");
            return (Criteria) this;
        }

        public Criteria andModdtNotIn(List<String> values) {
            addCriterion("moddt not in", values, "moddt");
            return (Criteria) this;
        }

        public Criteria andModdtBetween(String value1, String value2) {
            addCriterion("moddt between", value1, value2, "moddt");
            return (Criteria) this;
        }

        public Criteria andModdtNotBetween(String value1, String value2) {
            addCriterion("moddt not between", value1, value2, "moddt");
            return (Criteria) this;
        }

        public Criteria andBankacctIsNull() {
            addCriterion("bankacct is null");
            return (Criteria) this;
        }

        public Criteria andBankacctIsNotNull() {
            addCriterion("bankacct is not null");
            return (Criteria) this;
        }

        public Criteria andBankacctEqualTo(String value) {
            addCriterion("bankacct =", value, "bankacct");
            return (Criteria) this;
        }

        public Criteria andBankacctNotEqualTo(String value) {
            addCriterion("bankacct <>", value, "bankacct");
            return (Criteria) this;
        }

        public Criteria andBankacctGreaterThan(String value) {
            addCriterion("bankacct >", value, "bankacct");
            return (Criteria) this;
        }

        public Criteria andBankacctGreaterThanOrEqualTo(String value) {
            addCriterion("bankacct >=", value, "bankacct");
            return (Criteria) this;
        }

        public Criteria andBankacctLessThan(String value) {
            addCriterion("bankacct <", value, "bankacct");
            return (Criteria) this;
        }

        public Criteria andBankacctLessThanOrEqualTo(String value) {
            addCriterion("bankacct <=", value, "bankacct");
            return (Criteria) this;
        }

        public Criteria andBankacctLike(String value) {
            addCriterion("bankacct like", value, "bankacct");
            return (Criteria) this;
        }

        public Criteria andBankacctNotLike(String value) {
            addCriterion("bankacct not like", value, "bankacct");
            return (Criteria) this;
        }

        public Criteria andBankacctIn(List<String> values) {
            addCriterion("bankacct in", values, "bankacct");
            return (Criteria) this;
        }

        public Criteria andBankacctNotIn(List<String> values) {
            addCriterion("bankacct not in", values, "bankacct");
            return (Criteria) this;
        }

        public Criteria andBankacctBetween(String value1, String value2) {
            addCriterion("bankacct between", value1, value2, "bankacct");
            return (Criteria) this;
        }

        public Criteria andBankacctNotBetween(String value1, String value2) {
            addCriterion("bankacct not between", value1, value2, "bankacct");
            return (Criteria) this;
        }

        public Criteria andRatedtIsNull() {
            addCriterion("ratedt is null");
            return (Criteria) this;
        }

        public Criteria andRatedtIsNotNull() {
            addCriterion("ratedt is not null");
            return (Criteria) this;
        }

        public Criteria andRatedtEqualTo(String value) {
            addCriterion("ratedt =", value, "ratedt");
            return (Criteria) this;
        }

        public Criteria andRatedtNotEqualTo(String value) {
            addCriterion("ratedt <>", value, "ratedt");
            return (Criteria) this;
        }

        public Criteria andRatedtGreaterThan(String value) {
            addCriterion("ratedt >", value, "ratedt");
            return (Criteria) this;
        }

        public Criteria andRatedtGreaterThanOrEqualTo(String value) {
            addCriterion("ratedt >=", value, "ratedt");
            return (Criteria) this;
        }

        public Criteria andRatedtLessThan(String value) {
            addCriterion("ratedt <", value, "ratedt");
            return (Criteria) this;
        }

        public Criteria andRatedtLessThanOrEqualTo(String value) {
            addCriterion("ratedt <=", value, "ratedt");
            return (Criteria) this;
        }

        public Criteria andRatedtLike(String value) {
            addCriterion("ratedt like", value, "ratedt");
            return (Criteria) this;
        }

        public Criteria andRatedtNotLike(String value) {
            addCriterion("ratedt not like", value, "ratedt");
            return (Criteria) this;
        }

        public Criteria andRatedtIn(List<String> values) {
            addCriterion("ratedt in", values, "ratedt");
            return (Criteria) this;
        }

        public Criteria andRatedtNotIn(List<String> values) {
            addCriterion("ratedt not in", values, "ratedt");
            return (Criteria) this;
        }

        public Criteria andRatedtBetween(String value1, String value2) {
            addCriterion("ratedt between", value1, value2, "ratedt");
            return (Criteria) this;
        }

        public Criteria andRatedtNotBetween(String value1, String value2) {
            addCriterion("ratedt not between", value1, value2, "ratedt");
            return (Criteria) this;
        }

        public Criteria andPaydtIsNull() {
            addCriterion("paydt is null");
            return (Criteria) this;
        }

        public Criteria andPaydtIsNotNull() {
            addCriterion("paydt is not null");
            return (Criteria) this;
        }

        public Criteria andPaydtEqualTo(String value) {
            addCriterion("paydt =", value, "paydt");
            return (Criteria) this;
        }

        public Criteria andPaydtNotEqualTo(String value) {
            addCriterion("paydt <>", value, "paydt");
            return (Criteria) this;
        }

        public Criteria andPaydtGreaterThan(String value) {
            addCriterion("paydt >", value, "paydt");
            return (Criteria) this;
        }

        public Criteria andPaydtGreaterThanOrEqualTo(String value) {
            addCriterion("paydt >=", value, "paydt");
            return (Criteria) this;
        }

        public Criteria andPaydtLessThan(String value) {
            addCriterion("paydt <", value, "paydt");
            return (Criteria) this;
        }

        public Criteria andPaydtLessThanOrEqualTo(String value) {
            addCriterion("paydt <=", value, "paydt");
            return (Criteria) this;
        }

        public Criteria andPaydtLike(String value) {
            addCriterion("paydt like", value, "paydt");
            return (Criteria) this;
        }

        public Criteria andPaydtNotLike(String value) {
            addCriterion("paydt not like", value, "paydt");
            return (Criteria) this;
        }

        public Criteria andPaydtIn(List<String> values) {
            addCriterion("paydt in", values, "paydt");
            return (Criteria) this;
        }

        public Criteria andPaydtNotIn(List<String> values) {
            addCriterion("paydt not in", values, "paydt");
            return (Criteria) this;
        }

        public Criteria andPaydtBetween(String value1, String value2) {
            addCriterion("paydt between", value1, value2, "paydt");
            return (Criteria) this;
        }

        public Criteria andPaydtNotBetween(String value1, String value2) {
            addCriterion("paydt not between", value1, value2, "paydt");
            return (Criteria) this;
        }

        public Criteria andMjjDmIsNull() {
            addCriterion("mjjdm is null");
            return (Criteria) this;
        }

        public Criteria andMjjDmIsNotNull() {
            addCriterion("mjjdm is not null");
            return (Criteria) this;
        }

        public Criteria andMjjDmEqualTo(String value) {
            addCriterion("mjjdm =", value, "mjjDm");
            return (Criteria) this;
        }

        public Criteria andMjjDmNotEqualTo(String value) {
            addCriterion("mjjdm <>", value, "mjjDm");
            return (Criteria) this;
        }

        public Criteria andMjjDmGreaterThan(String value) {
            addCriterion("mjjdm >", value, "mjjDm");
            return (Criteria) this;
        }

        public Criteria andMjjDmGreaterThanOrEqualTo(String value) {
            addCriterion("mjjdm >=", value, "mjjDm");
            return (Criteria) this;
        }

        public Criteria andMjjDmLessThan(String value) {
            addCriterion("mjjdm <", value, "mjjDm");
            return (Criteria) this;
        }

        public Criteria andMjjDmLessThanOrEqualTo(String value) {
            addCriterion("mjjdm <=", value, "mjjDm");
            return (Criteria) this;
        }

        public Criteria andMjjDmLike(String value) {
            addCriterion("mjjdm like", value, "mjjDm");
            return (Criteria) this;
        }

        public Criteria andMjjDmNotLike(String value) {
            addCriterion("mjjdm not like", value, "mjjDm");
            return (Criteria) this;
        }

        public Criteria andMjjDmIn(List<String> values) {
            addCriterion("mjjdm in", values, "mjjDm");
            return (Criteria) this;
        }

        public Criteria andMjjDmNotIn(List<String> values) {
            addCriterion("mjjdm not in", values, "mjjDm");
            return (Criteria) this;
        }

        public Criteria andMjjDmBetween(String value1, String value2) {
            addCriterion("mjjdm between", value1, value2, "mjjDm");
            return (Criteria) this;
        }

        public Criteria andMjjDmNotBetween(String value1, String value2) {
            addCriterion("mjjdm not between", value1, value2, "mjjDm");
            return (Criteria) this;
        }

        public Criteria andOperatetypeIsNull() {
            addCriterion("operatetype is null");
            return (Criteria) this;
        }

        public Criteria andOperatetypeIsNotNull() {
            addCriterion("operatetype is not null");
            return (Criteria) this;
        }

        public Criteria andOperatetypeEqualTo(String value) {
            addCriterion("operatetype =", value, "operatetype");
            return (Criteria) this;
        }

        public Criteria andOperatetypeNotEqualTo(String value) {
            addCriterion("operatetype <>", value, "operatetype");
            return (Criteria) this;
        }

        public Criteria andOperatetypeGreaterThan(String value) {
            addCriterion("operatetype >", value, "operatetype");
            return (Criteria) this;
        }

        public Criteria andOperatetypeGreaterThanOrEqualTo(String value) {
            addCriterion("operatetype >=", value, "operatetype");
            return (Criteria) this;
        }

        public Criteria andOperatetypeLessThan(String value) {
            addCriterion("operatetype <", value, "operatetype");
            return (Criteria) this;
        }

        public Criteria andOperatetypeLessThanOrEqualTo(String value) {
            addCriterion("operatetype <=", value, "operatetype");
            return (Criteria) this;
        }

        public Criteria andOperatetypeLike(String value) {
            addCriterion("operatetype like", value, "operatetype");
            return (Criteria) this;
        }

        public Criteria andOperatetypeNotLike(String value) {
            addCriterion("operatetype not like", value, "operatetype");
            return (Criteria) this;
        }

        public Criteria andOperatetypeIn(List<String> values) {
            addCriterion("operatetype in", values, "operatetype");
            return (Criteria) this;
        }

        public Criteria andOperatetypeNotIn(List<String> values) {
            addCriterion("operatetype not in", values, "operatetype");
            return (Criteria) this;
        }

        public Criteria andOperatetypeBetween(String value1, String value2) {
            addCriterion("operatetype between", value1, value2, "operatetype");
            return (Criteria) this;
        }

        public Criteria andOperatetypeNotBetween(String value1, String value2) {
            addCriterion("operatetype not between", value1, value2, "operatetype");
            return (Criteria) this;
        }

        public Criteria andVersionNoIsNull() {
            addCriterion("versionno is null");
            return (Criteria) this;
        }

        public Criteria andVersionNoIsNotNull() {
            addCriterion("versionno is not null");
            return (Criteria) this;
        }

        public Criteria andVersionNoEqualTo(String value) {
            addCriterion("versionno =", value, "versionNo");
            return (Criteria) this;
        }

        public Criteria andVersionNoNotEqualTo(String value) {
            addCriterion("versionno <>", value, "versionNo");
            return (Criteria) this;
        }

        public Criteria andVersionNoGreaterThan(String value) {
            addCriterion("versionno >", value, "versionNo");
            return (Criteria) this;
        }

        public Criteria andVersionNoGreaterThanOrEqualTo(String value) {
            addCriterion("versionno >=", value, "versionNo");
            return (Criteria) this;
        }

        public Criteria andVersionNoLessThan(String value) {
            addCriterion("versionno <", value, "versionNo");
            return (Criteria) this;
        }

        public Criteria andVersionNoLessThanOrEqualTo(String value) {
            addCriterion("versionno <=", value, "versionNo");
            return (Criteria) this;
        }

        public Criteria andVersionNoLike(String value) {
            addCriterion("versionno like", value, "versionNo");
            return (Criteria) this;
        }

        public Criteria andVersionNoNotLike(String value) {
            addCriterion("versionno not like", value, "versionNo");
            return (Criteria) this;
        }

        public Criteria andVersionNoIn(List<String> values) {
            addCriterion("versionno in", values, "versionNo");
            return (Criteria) this;
        }

        public Criteria andVersionNoNotIn(List<String> values) {
            addCriterion("versionno not in", values, "versionNo");
            return (Criteria) this;
        }

        public Criteria andVersionNoBetween(String value1, String value2) {
            addCriterion("versionno between", value1, value2, "versionNo");
            return (Criteria) this;
        }

        public Criteria andVersionNoNotBetween(String value1, String value2) {
            addCriterion("versionno not between", value1, value2, "versionNo");
            return (Criteria) this;
        }

        public Criteria andImportDtIsNull() {
            addCriterion("importdt is null");
            return (Criteria) this;
        }

        public Criteria andImportDtIsNotNull() {
            addCriterion("importdt is not null");
            return (Criteria) this;
        }

        public Criteria andImportDtEqualTo(String value) {
            addCriterion("importdt =", value, "importDt");
            return (Criteria) this;
        }

        public Criteria andImportDtNotEqualTo(String value) {
            addCriterion("importdt <>", value, "importDt");
            return (Criteria) this;
        }

        public Criteria andImportDtGreaterThan(String value) {
            addCriterion("importdt >", value, "importDt");
            return (Criteria) this;
        }

        public Criteria andImportDtGreaterThanOrEqualTo(String value) {
            addCriterion("importdt >=", value, "importDt");
            return (Criteria) this;
        }

        public Criteria andImportDtLessThan(String value) {
            addCriterion("importdt <", value, "importDt");
            return (Criteria) this;
        }

        public Criteria andImportDtLessThanOrEqualTo(String value) {
            addCriterion("importdt <=", value, "importDt");
            return (Criteria) this;
        }

        public Criteria andImportDtLike(String value) {
            addCriterion("importdt like", value, "importDt");
            return (Criteria) this;
        }

        public Criteria andImportDtNotLike(String value) {
            addCriterion("importdt not like", value, "importDt");
            return (Criteria) this;
        }

        public Criteria andImportDtIn(List<String> values) {
            addCriterion("importdt in", values, "importDt");
            return (Criteria) this;
        }

        public Criteria andImportDtNotIn(List<String> values) {
            addCriterion("importdt not in", values, "importDt");
            return (Criteria) this;
        }

        public Criteria andImportDtBetween(String value1, String value2) {
            addCriterion("importdt between", value1, value2, "importDt");
            return (Criteria) this;
        }

        public Criteria andImportDtNotBetween(String value1, String value2) {
            addCriterion("importdt not between", value1, value2, "importDt");
            return (Criteria) this;
        }

        public Criteria andIsVolTansferIsNull() {
            addCriterion("is_vol_tansfer is null");
            return (Criteria) this;
        }

        public Criteria andIsVolTansferIsNotNull() {
            addCriterion("is_vol_tansfer is not null");
            return (Criteria) this;
        }

        public Criteria andIsVolTansferEqualTo(String value) {
            addCriterion("is_vol_tansfer =", value, "isVolTansfer");
            return (Criteria) this;
        }

        public Criteria andIsVolTansferNotEqualTo(String value) {
            addCriterion("is_vol_tansfer <>", value, "isVolTansfer");
            return (Criteria) this;
        }

        public Criteria andIsVolTansferGreaterThan(String value) {
            addCriterion("is_vol_tansfer >", value, "isVolTansfer");
            return (Criteria) this;
        }

        public Criteria andIsVolTansferGreaterThanOrEqualTo(String value) {
            addCriterion("is_vol_tansfer >=", value, "isVolTansfer");
            return (Criteria) this;
        }

        public Criteria andIsVolTansferLessThan(String value) {
            addCriterion("is_vol_tansfer <", value, "isVolTansfer");
            return (Criteria) this;
        }

        public Criteria andIsVolTansferLessThanOrEqualTo(String value) {
            addCriterion("is_vol_tansfer <=", value, "isVolTansfer");
            return (Criteria) this;
        }

        public Criteria andIsVolTansferLike(String value) {
            addCriterion("is_vol_tansfer like", value, "isVolTansfer");
            return (Criteria) this;
        }

        public Criteria andIsVolTansferNotLike(String value) {
            addCriterion("is_vol_tansfer not like", value, "isVolTansfer");
            return (Criteria) this;
        }

        public Criteria andIsVolTansferIn(List<String> values) {
            addCriterion("is_vol_tansfer in", values, "isVolTansfer");
            return (Criteria) this;
        }

        public Criteria andIsVolTansferNotIn(List<String> values) {
            addCriterion("is_vol_tansfer not in", values, "isVolTansfer");
            return (Criteria) this;
        }

        public Criteria andIsVolTansferBetween(String value1, String value2) {
            addCriterion("is_vol_tansfer between", value1, value2, "isVolTansfer");
            return (Criteria) this;
        }

        public Criteria andIsVolTansferNotBetween(String value1, String value2) {
            addCriterion("is_vol_tansfer not between", value1, value2, "isVolTansfer");
            return (Criteria) this;
        }

        public Criteria andPaystateIsNull() {
            addCriterion("paystate is null");
            return (Criteria) this;
        }

        public Criteria andPaystateIsNotNull() {
            addCriterion("paystate is not null");
            return (Criteria) this;
        }

        public Criteria andPaystateEqualTo(String value) {
            addCriterion("paystate =", value, "paystate");
            return (Criteria) this;
        }

        public Criteria andPaystateNotEqualTo(String value) {
            addCriterion("paystate <>", value, "paystate");
            return (Criteria) this;
        }

        public Criteria andPaystateGreaterThan(String value) {
            addCriterion("paystate >", value, "paystate");
            return (Criteria) this;
        }

        public Criteria andPaystateGreaterThanOrEqualTo(String value) {
            addCriterion("paystate >=", value, "paystate");
            return (Criteria) this;
        }

        public Criteria andPaystateLessThan(String value) {
            addCriterion("paystate <", value, "paystate");
            return (Criteria) this;
        }

        public Criteria andPaystateLessThanOrEqualTo(String value) {
            addCriterion("paystate <=", value, "paystate");
            return (Criteria) this;
        }

        public Criteria andPaystateLike(String value) {
            addCriterion("paystate like", value, "paystate");
            return (Criteria) this;
        }

        public Criteria andPaystateNotLike(String value) {
            addCriterion("paystate not like", value, "paystate");
            return (Criteria) this;
        }

        public Criteria andPaystateIn(List<String> values) {
            addCriterion("paystate in", values, "paystate");
            return (Criteria) this;
        }

        public Criteria andPaystateNotIn(List<String> values) {
            addCriterion("paystate not in", values, "paystate");
            return (Criteria) this;
        }

        public Criteria andPaystateBetween(String value1, String value2) {
            addCriterion("paystate between", value1, value2, "paystate");
            return (Criteria) this;
        }

        public Criteria andPaystateNotBetween(String value1, String value2) {
            addCriterion("paystate not between", value1, value2, "paystate");
            return (Criteria) this;
        }

        public Criteria andOrderstateIsNull() {
            addCriterion("orderstate is null");
            return (Criteria) this;
        }

        public Criteria andOrderstateIsNotNull() {
            addCriterion("orderstate is not null");
            return (Criteria) this;
        }

        public Criteria andOrderstateEqualTo(String value) {
            addCriterion("orderstate =", value, "orderstate");
            return (Criteria) this;
        }

        public Criteria andOrderstateNotEqualTo(String value) {
            addCriterion("orderstate <>", value, "orderstate");
            return (Criteria) this;
        }

        public Criteria andOrderstateGreaterThan(String value) {
            addCriterion("orderstate >", value, "orderstate");
            return (Criteria) this;
        }

        public Criteria andOrderstateGreaterThanOrEqualTo(String value) {
            addCriterion("orderstate >=", value, "orderstate");
            return (Criteria) this;
        }

        public Criteria andOrderstateLessThan(String value) {
            addCriterion("orderstate <", value, "orderstate");
            return (Criteria) this;
        }

        public Criteria andOrderstateLessThanOrEqualTo(String value) {
            addCriterion("orderstate <=", value, "orderstate");
            return (Criteria) this;
        }

        public Criteria andOrderstateLike(String value) {
            addCriterion("orderstate like", value, "orderstate");
            return (Criteria) this;
        }

        public Criteria andOrderstateNotLike(String value) {
            addCriterion("orderstate not like", value, "orderstate");
            return (Criteria) this;
        }

        public Criteria andOrderstateIn(List<String> values) {
            addCriterion("orderstate in", values, "orderstate");
            return (Criteria) this;
        }

        public Criteria andOrderstateNotIn(List<String> values) {
            addCriterion("orderstate not in", values, "orderstate");
            return (Criteria) this;
        }

        public Criteria andOrderstateBetween(String value1, String value2) {
            addCriterion("orderstate between", value1, value2, "orderstate");
            return (Criteria) this;
        }

        public Criteria andOrderstateNotBetween(String value1, String value2) {
            addCriterion("orderstate not between", value1, value2, "orderstate");
            return (Criteria) this;
        }

        public Criteria andPaydtmIsNull() {
            addCriterion("paydtm is null");
            return (Criteria) this;
        }

        public Criteria andPaydtmIsNotNull() {
            addCriterion("paydtm is not null");
            return (Criteria) this;
        }

        public Criteria andPaydtmEqualTo(String value) {
            addCriterion("paydtm =", value, "paydtm");
            return (Criteria) this;
        }

        public Criteria andPaydtmNotEqualTo(String value) {
            addCriterion("paydtm <>", value, "paydtm");
            return (Criteria) this;
        }

        public Criteria andPaydtmGreaterThan(String value) {
            addCriterion("paydtm >", value, "paydtm");
            return (Criteria) this;
        }

        public Criteria andPaydtmGreaterThanOrEqualTo(String value) {
            addCriterion("paydtm >=", value, "paydtm");
            return (Criteria) this;
        }

        public Criteria andPaydtmLessThan(String value) {
            addCriterion("paydtm <", value, "paydtm");
            return (Criteria) this;
        }

        public Criteria andPaydtmLessThanOrEqualTo(String value) {
            addCriterion("paydtm <=", value, "paydtm");
            return (Criteria) this;
        }

        public Criteria andPaydtmLike(String value) {
            addCriterion("paydtm like", value, "paydtm");
            return (Criteria) this;
        }

        public Criteria andPaydtmNotLike(String value) {
            addCriterion("paydtm not like", value, "paydtm");
            return (Criteria) this;
        }

        public Criteria andPaydtmIn(List<String> values) {
            addCriterion("paydtm in", values, "paydtm");
            return (Criteria) this;
        }

        public Criteria andPaydtmNotIn(List<String> values) {
            addCriterion("paydtm not in", values, "paydtm");
            return (Criteria) this;
        }

        public Criteria andPaydtmBetween(String value1, String value2) {
            addCriterion("paydtm between", value1, value2, "paydtm");
            return (Criteria) this;
        }

        public Criteria andPaydtmNotBetween(String value1, String value2) {
            addCriterion("paydtm not between", value1, value2, "paydtm");
            return (Criteria) this;
        }

        public Criteria andRealpayamtIsNull() {
            addCriterion("realpayamt is null");
            return (Criteria) this;
        }

        public Criteria andRealpayamtIsNotNull() {
            addCriterion("realpayamt is not null");
            return (Criteria) this;
        }

        public Criteria andRealpayamtEqualTo(BigDecimal value) {
            addCriterion("realpayamt =", value, "realpayamt");
            return (Criteria) this;
        }

        public Criteria andRealpayamtNotEqualTo(BigDecimal value) {
            addCriterion("realpayamt <>", value, "realpayamt");
            return (Criteria) this;
        }

        public Criteria andRealpayamtGreaterThan(BigDecimal value) {
            addCriterion("realpayamt >", value, "realpayamt");
            return (Criteria) this;
        }

        public Criteria andRealpayamtGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("realpayamt >=", value, "realpayamt");
            return (Criteria) this;
        }

        public Criteria andRealpayamtLessThan(BigDecimal value) {
            addCriterion("realpayamt <", value, "realpayamt");
            return (Criteria) this;
        }

        public Criteria andRealpayamtLessThanOrEqualTo(BigDecimal value) {
            addCriterion("realpayamt <=", value, "realpayamt");
            return (Criteria) this;
        }

        public Criteria andRealpayamtIn(List<BigDecimal> values) {
            addCriterion("realpayamt in", values, "realpayamt");
            return (Criteria) this;
        }

        public Criteria andRealpayamtNotIn(List<BigDecimal> values) {
            addCriterion("realpayamt not in", values, "realpayamt");
            return (Criteria) this;
        }

        public Criteria andRealpayamtBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("realpayamt between", value1, value2, "realpayamt");
            return (Criteria) this;
        }

        public Criteria andRealpayamtNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("realpayamt not between", value1, value2, "realpayamt");
            return (Criteria) this;
        }

        public Criteria andPrebookstateIsNull() {
            addCriterion("prebookstate is null");
            return (Criteria) this;
        }

        public Criteria andPrebookstateIsNotNull() {
            addCriterion("prebookstate is not null");
            return (Criteria) this;
        }

        public Criteria andPrebookstateEqualTo(String value) {
            addCriterion("prebookstate =", value, "prebookstate");
            return (Criteria) this;
        }

        public Criteria andPrebookstateNotEqualTo(String value) {
            addCriterion("prebookstate <>", value, "prebookstate");
            return (Criteria) this;
        }

        public Criteria andPrebookstateGreaterThan(String value) {
            addCriterion("prebookstate >", value, "prebookstate");
            return (Criteria) this;
        }

        public Criteria andPrebookstateGreaterThanOrEqualTo(String value) {
            addCriterion("prebookstate >=", value, "prebookstate");
            return (Criteria) this;
        }

        public Criteria andPrebookstateLessThan(String value) {
            addCriterion("prebookstate <", value, "prebookstate");
            return (Criteria) this;
        }

        public Criteria andPrebookstateLessThanOrEqualTo(String value) {
            addCriterion("prebookstate <=", value, "prebookstate");
            return (Criteria) this;
        }

        public Criteria andPrebookstateLike(String value) {
            addCriterion("prebookstate like", value, "prebookstate");
            return (Criteria) this;
        }

        public Criteria andPrebookstateNotLike(String value) {
            addCriterion("prebookstate not like", value, "prebookstate");
            return (Criteria) this;
        }

        public Criteria andPrebookstateIn(List<String> values) {
            addCriterion("prebookstate in", values, "prebookstate");
            return (Criteria) this;
        }

        public Criteria andPrebookstateNotIn(List<String> values) {
            addCriterion("prebookstate not in", values, "prebookstate");
            return (Criteria) this;
        }

        public Criteria andPrebookstateBetween(String value1, String value2) {
            addCriterion("prebookstate between", value1, value2, "prebookstate");
            return (Criteria) this;
        }

        public Criteria andPrebookstateNotBetween(String value1, String value2) {
            addCriterion("prebookstate not between", value1, value2, "prebookstate");
            return (Criteria) this;
        }

        public Criteria andIsHkProductIsNull() {
            addCriterion("is_hk_product is null");
            return (Criteria) this;
        }

        public Criteria andIsHkProductIsNotNull() {
            addCriterion("is_hk_product is not null");
            return (Criteria) this;
        }

        public Criteria andIsHkProductEqualTo(String value) {
            addCriterion("is_hk_product =", value, "isHkProduct");
            return (Criteria) this;
        }

        public Criteria andIsHkProductNotEqualTo(String value) {
            addCriterion("is_hk_product <>", value, "isHkProduct");
            return (Criteria) this;
        }

        public Criteria andIsHkProductGreaterThan(String value) {
            addCriterion("is_hk_product >", value, "isHkProduct");
            return (Criteria) this;
        }

        public Criteria andIsHkProductGreaterThanOrEqualTo(String value) {
            addCriterion("is_hk_product >=", value, "isHkProduct");
            return (Criteria) this;
        }

        public Criteria andIsHkProductLessThan(String value) {
            addCriterion("is_hk_product <", value, "isHkProduct");
            return (Criteria) this;
        }

        public Criteria andIsHkProductLessThanOrEqualTo(String value) {
            addCriterion("is_hk_product <=", value, "isHkProduct");
            return (Criteria) this;
        }

        public Criteria andIsHkProductLike(String value) {
            addCriterion("is_hk_product like", value, "isHkProduct");
            return (Criteria) this;
        }

        public Criteria andIsHkProductNotLike(String value) {
            addCriterion("is_hk_product not like", value, "isHkProduct");
            return (Criteria) this;
        }

        public Criteria andIsHkProductIn(List<String> values) {
            addCriterion("is_hk_product in", values, "isHkProduct");
            return (Criteria) this;
        }

        public Criteria andIsHkProductNotIn(List<String> values) {
            addCriterion("is_hk_product not in", values, "isHkProduct");
            return (Criteria) this;
        }

        public Criteria andIsHkProductBetween(String value1, String value2) {
            addCriterion("is_hk_product between", value1, value2, "isHkProduct");
            return (Criteria) this;
        }

        public Criteria andIsHkProductNotBetween(String value1, String value2) {
            addCriterion("is_hk_product not between", value1, value2, "isHkProduct");
            return (Criteria) this;
        }

        public Criteria andTransferPriceIsNull() {
            addCriterion("transfer_price is null");
            return (Criteria) this;
        }

        public Criteria andTransferPriceIsNotNull() {
            addCriterion("transfer_price is not null");
            return (Criteria) this;
        }

        public Criteria andTransferPriceEqualTo(BigDecimal value) {
            addCriterion("transfer_price =", value, "transferPrice");
            return (Criteria) this;
        }

        public Criteria andTransferPriceNotEqualTo(BigDecimal value) {
            addCriterion("transfer_price <>", value, "transferPrice");
            return (Criteria) this;
        }

        public Criteria andTransferPriceGreaterThan(BigDecimal value) {
            addCriterion("transfer_price >", value, "transferPrice");
            return (Criteria) this;
        }

        public Criteria andTransferPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("transfer_price >=", value, "transferPrice");
            return (Criteria) this;
        }

        public Criteria andTransferPriceLessThan(BigDecimal value) {
            addCriterion("transfer_price <", value, "transferPrice");
            return (Criteria) this;
        }

        public Criteria andTransferPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("transfer_price <=", value, "transferPrice");
            return (Criteria) this;
        }

        public Criteria andTransferPriceIn(List<BigDecimal> values) {
            addCriterion("transfer_price in", values, "transferPrice");
            return (Criteria) this;
        }

        public Criteria andTransferPriceNotIn(List<BigDecimal> values) {
            addCriterion("transfer_price not in", values, "transferPrice");
            return (Criteria) this;
        }

        public Criteria andTransferPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("transfer_price between", value1, value2, "transferPrice");
            return (Criteria) this;
        }

        public Criteria andTransferPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("transfer_price not between", value1, value2, "transferPrice");
            return (Criteria) this;
        }

        public Criteria andIsNoTradeTransferIsNull() {
            addCriterion("is_no_trade_transfer is null");
            return (Criteria) this;
        }

        public Criteria andIsNoTradeTransferIsNotNull() {
            addCriterion("is_no_trade_transfer is not null");
            return (Criteria) this;
        }

        public Criteria andIsNoTradeTransferEqualTo(String value) {
            addCriterion("is_no_trade_transfer =", value, "isNoTradeTransfer");
            return (Criteria) this;
        }

        public Criteria andIsNoTradeTransferNotEqualTo(String value) {
            addCriterion("is_no_trade_transfer <>", value, "isNoTradeTransfer");
            return (Criteria) this;
        }

        public Criteria andIsNoTradeTransferGreaterThan(String value) {
            addCriterion("is_no_trade_transfer >", value, "isNoTradeTransfer");
            return (Criteria) this;
        }

        public Criteria andIsNoTradeTransferGreaterThanOrEqualTo(String value) {
            addCriterion("is_no_trade_transfer >=", value, "isNoTradeTransfer");
            return (Criteria) this;
        }

        public Criteria andIsNoTradeTransferLessThan(String value) {
            addCriterion("is_no_trade_transfer <", value, "isNoTradeTransfer");
            return (Criteria) this;
        }

        public Criteria andIsNoTradeTransferLessThanOrEqualTo(String value) {
            addCriterion("is_no_trade_transfer <=", value, "isNoTradeTransfer");
            return (Criteria) this;
        }

        public Criteria andIsNoTradeTransferLike(String value) {
            addCriterion("is_no_trade_transfer like", value, "isNoTradeTransfer");
            return (Criteria) this;
        }

        public Criteria andIsNoTradeTransferNotLike(String value) {
            addCriterion("is_no_trade_transfer not like", value, "isNoTradeTransfer");
            return (Criteria) this;
        }

        public Criteria andIsNoTradeTransferIn(List<String> values) {
            addCriterion("is_no_trade_transfer in", values, "isNoTradeTransfer");
            return (Criteria) this;
        }

        public Criteria andIsNoTradeTransferNotIn(List<String> values) {
            addCriterion("is_no_trade_transfer not in", values, "isNoTradeTransfer");
            return (Criteria) this;
        }

        public Criteria andIsNoTradeTransferBetween(String value1, String value2) {
            addCriterion("is_no_trade_transfer between", value1, value2, "isNoTradeTransfer");
            return (Criteria) this;
        }

        public Criteria andIsNoTradeTransferNotBetween(String value1, String value2) {
            addCriterion("is_no_trade_transfer not between", value1, value2, "isNoTradeTransfer");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andAppDtIsNull() {
            addCriterion("app_dt is null");
            return (Criteria) this;
        }

        public Criteria andAppDtIsNotNull() {
            addCriterion("app_dt is not null");
            return (Criteria) this;
        }

        public Criteria andAppDtEqualTo(String value) {
            addCriterion("app_dt =", value, "appDt");
            return (Criteria) this;
        }

        public Criteria andAppDtNotEqualTo(String value) {
            addCriterion("app_dt <>", value, "appDt");
            return (Criteria) this;
        }

        public Criteria andAppDtGreaterThan(String value) {
            addCriterion("app_dt >", value, "appDt");
            return (Criteria) this;
        }

        public Criteria andAppDtGreaterThanOrEqualTo(String value) {
            addCriterion("app_dt >=", value, "appDt");
            return (Criteria) this;
        }

        public Criteria andAppDtLessThan(String value) {
            addCriterion("app_dt <", value, "appDt");
            return (Criteria) this;
        }

        public Criteria andAppDtLessThanOrEqualTo(String value) {
            addCriterion("app_dt <=", value, "appDt");
            return (Criteria) this;
        }

        public Criteria andAppDtLike(String value) {
            addCriterion("app_dt like", value, "appDt");
            return (Criteria) this;
        }

        public Criteria andAppDtNotLike(String value) {
            addCriterion("app_dt not like", value, "appDt");
            return (Criteria) this;
        }

        public Criteria andAppDtIn(List<String> values) {
            addCriterion("app_dt in", values, "appDt");
            return (Criteria) this;
        }

        public Criteria andAppDtNotIn(List<String> values) {
            addCriterion("app_dt not in", values, "appDt");
            return (Criteria) this;
        }

        public Criteria andAppDtBetween(String value1, String value2) {
            addCriterion("app_dt between", value1, value2, "appDt");
            return (Criteria) this;
        }

        public Criteria andAppDtNotBetween(String value1, String value2) {
            addCriterion("app_dt not between", value1, value2, "appDt");
            return (Criteria) this;
        }

        public Criteria andAppTimeIsNull() {
            addCriterion("app_time is null");
            return (Criteria) this;
        }

        public Criteria andAppTimeIsNotNull() {
            addCriterion("app_time is not null");
            return (Criteria) this;
        }

        public Criteria andAppTimeEqualTo(String value) {
            addCriterion("app_time =", value, "appTime");
            return (Criteria) this;
        }

        public Criteria andAppTimeNotEqualTo(String value) {
            addCriterion("app_time <>", value, "appTime");
            return (Criteria) this;
        }

        public Criteria andAppTimeGreaterThan(String value) {
            addCriterion("app_time >", value, "appTime");
            return (Criteria) this;
        }

        public Criteria andAppTimeGreaterThanOrEqualTo(String value) {
            addCriterion("app_time >=", value, "appTime");
            return (Criteria) this;
        }

        public Criteria andAppTimeLessThan(String value) {
            addCriterion("app_time <", value, "appTime");
            return (Criteria) this;
        }

        public Criteria andAppTimeLessThanOrEqualTo(String value) {
            addCriterion("app_time <=", value, "appTime");
            return (Criteria) this;
        }

        public Criteria andAppTimeLike(String value) {
            addCriterion("app_time like", value, "appTime");
            return (Criteria) this;
        }

        public Criteria andAppTimeNotLike(String value) {
            addCriterion("app_time not like", value, "appTime");
            return (Criteria) this;
        }

        public Criteria andAppTimeIn(List<String> values) {
            addCriterion("app_time in", values, "appTime");
            return (Criteria) this;
        }

        public Criteria andAppTimeNotIn(List<String> values) {
            addCriterion("app_time not in", values, "appTime");
            return (Criteria) this;
        }

        public Criteria andAppTimeBetween(String value1, String value2) {
            addCriterion("app_time between", value1, value2, "appTime");
            return (Criteria) this;
        }

        public Criteria andAppTimeNotBetween(String value1, String value2) {
            addCriterion("app_time not between", value1, value2, "appTime");
            return (Criteria) this;
        }

        public Criteria andPayTypeIsNull() {
            addCriterion("pay_type is null");
            return (Criteria) this;
        }

        public Criteria andPayTypeIsNotNull() {
            addCriterion("pay_type is not null");
            return (Criteria) this;
        }

        public Criteria andPayTypeEqualTo(String value) {
            addCriterion("pay_type =", value, "payType");
            return (Criteria) this;
        }

        public Criteria andPayTypeNotEqualTo(String value) {
            addCriterion("pay_type <>", value, "payType");
            return (Criteria) this;
        }

        public Criteria andPayTypeGreaterThan(String value) {
            addCriterion("pay_type >", value, "payType");
            return (Criteria) this;
        }

        public Criteria andPayTypeGreaterThanOrEqualTo(String value) {
            addCriterion("pay_type >=", value, "payType");
            return (Criteria) this;
        }

        public Criteria andPayTypeLessThan(String value) {
            addCriterion("pay_type <", value, "payType");
            return (Criteria) this;
        }

        public Criteria andPayTypeLessThanOrEqualTo(String value) {
            addCriterion("pay_type <=", value, "payType");
            return (Criteria) this;
        }

        public Criteria andPayTypeLike(String value) {
            addCriterion("pay_type like", value, "payType");
            return (Criteria) this;
        }

        public Criteria andPayTypeNotLike(String value) {
            addCriterion("pay_type not like", value, "payType");
            return (Criteria) this;
        }

        public Criteria andPayTypeIn(List<String> values) {
            addCriterion("pay_type in", values, "payType");
            return (Criteria) this;
        }

        public Criteria andPayTypeNotIn(List<String> values) {
            addCriterion("pay_type not in", values, "payType");
            return (Criteria) this;
        }

        public Criteria andPayTypeBetween(String value1, String value2) {
            addCriterion("pay_type between", value1, value2, "payType");
            return (Criteria) this;
        }

        public Criteria andPayTypeNotBetween(String value1, String value2) {
            addCriterion("pay_type not between", value1, value2, "payType");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionIsNull() {
            addCriterion("redeem_direction is null");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionIsNotNull() {
            addCriterion("redeem_direction is not null");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionEqualTo(String value) {
            addCriterion("redeem_direction =", value, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionNotEqualTo(String value) {
            addCriterion("redeem_direction <>", value, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionGreaterThan(String value) {
            addCriterion("redeem_direction >", value, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionGreaterThanOrEqualTo(String value) {
            addCriterion("redeem_direction >=", value, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionLessThan(String value) {
            addCriterion("redeem_direction <", value, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionLessThanOrEqualTo(String value) {
            addCriterion("redeem_direction <=", value, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionLike(String value) {
            addCriterion("redeem_direction like", value, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionNotLike(String value) {
            addCriterion("redeem_direction not like", value, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionIn(List<String> values) {
            addCriterion("redeem_direction in", values, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionNotIn(List<String> values) {
            addCriterion("redeem_direction not in", values, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionBetween(String value1, String value2) {
            addCriterion("redeem_direction between", value1, value2, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionNotBetween(String value1, String value2) {
            addCriterion("redeem_direction not between", value1, value2, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemTypeIsNull() {
            addCriterion("redeem_type is null");
            return (Criteria) this;
        }

        public Criteria andRedeemTypeIsNotNull() {
            addCriterion("redeem_type is not null");
            return (Criteria) this;
        }

        public Criteria andRedeemTypeEqualTo(String value) {
            addCriterion("redeem_type =", value, "redeemType");
            return (Criteria) this;
        }

        public Criteria andRedeemTypeNotEqualTo(String value) {
            addCriterion("redeem_type <>", value, "redeemType");
            return (Criteria) this;
        }

        public Criteria andRedeemTypeGreaterThan(String value) {
            addCriterion("redeem_type >", value, "redeemType");
            return (Criteria) this;
        }

        public Criteria andRedeemTypeGreaterThanOrEqualTo(String value) {
            addCriterion("redeem_type >=", value, "redeemType");
            return (Criteria) this;
        }

        public Criteria andRedeemTypeLessThan(String value) {
            addCriterion("redeem_type <", value, "redeemType");
            return (Criteria) this;
        }

        public Criteria andRedeemTypeLessThanOrEqualTo(String value) {
            addCriterion("redeem_type <=", value, "redeemType");
            return (Criteria) this;
        }

        public Criteria andRedeemTypeLike(String value) {
            addCriterion("redeem_type like", value, "redeemType");
            return (Criteria) this;
        }

        public Criteria andRedeemTypeNotLike(String value) {
            addCriterion("redeem_type not like", value, "redeemType");
            return (Criteria) this;
        }

        public Criteria andRedeemTypeIn(List<String> values) {
            addCriterion("redeem_type in", values, "redeemType");
            return (Criteria) this;
        }

        public Criteria andRedeemTypeNotIn(List<String> values) {
            addCriterion("redeem_type not in", values, "redeemType");
            return (Criteria) this;
        }

        public Criteria andRedeemTypeBetween(String value1, String value2) {
            addCriterion("redeem_type between", value1, value2, "redeemType");
            return (Criteria) this;
        }

        public Criteria andRedeemTypeNotBetween(String value1, String value2) {
            addCriterion("redeem_type not between", value1, value2, "redeemType");
            return (Criteria) this;
        }

        public Criteria andNewTradeDtIsNull() {
            addCriterion("NEW_TRADE_DT is null");
            return (Criteria) this;
        }

        public Criteria andNewTradeDtIsNotNull() {
            addCriterion("NEW_TRADE_DT is not null");
            return (Criteria) this;
        }

        public Criteria andNewTradeDtEqualTo(String value) {
            addCriterion("NEW_TRADE_DT =", value, "newTradeDt");
            return (Criteria) this;
        }

        public Criteria andNewTradeDtNotEqualTo(String value) {
            addCriterion("NEW_TRADE_DT <>", value, "newTradeDt");
            return (Criteria) this;
        }

        public Criteria andNewTradeDtGreaterThan(String value) {
            addCriterion("NEW_TRADE_DT >", value, "newTradeDt");
            return (Criteria) this;
        }

        public Criteria andNewTradeDtGreaterThanOrEqualTo(String value) {
            addCriterion("NEW_TRADE_DT >=", value, "newTradeDt");
            return (Criteria) this;
        }

        public Criteria andNewTradeDtLessThan(String value) {
            addCriterion("NEW_TRADE_DT <", value, "newTradeDt");
            return (Criteria) this;
        }

        public Criteria andNewTradeDtLessThanOrEqualTo(String value) {
            addCriterion("NEW_TRADE_DT <=", value, "newTradeDt");
            return (Criteria) this;
        }

        public Criteria andNewTradeDtLike(String value) {
            addCriterion("NEW_TRADE_DT like", value, "newTradeDt");
            return (Criteria) this;
        }

        public Criteria andNewTradeDtNotLike(String value) {
            addCriterion("NEW_TRADE_DT not like", value, "newTradeDt");
            return (Criteria) this;
        }

        public Criteria andNewTradeDtIn(List<String> values) {
            addCriterion("NEW_TRADE_DT in", values, "newTradeDt");
            return (Criteria) this;
        }

        public Criteria andNewTradeDtNotIn(List<String> values) {
            addCriterion("NEW_TRADE_DT not in", values, "newTradeDt");
            return (Criteria) this;
        }

        public Criteria andNewTradeDtBetween(String value1, String value2) {
            addCriterion("NEW_TRADE_DT between", value1, value2, "newTradeDt");
            return (Criteria) this;
        }

        public Criteria andNewTradeDtNotBetween(String value1, String value2) {
            addCriterion("NEW_TRADE_DT not between", value1, value2, "newTradeDt");
            return (Criteria) this;
        }

        public Criteria andAckDtIsNull() {
            addCriterion("ack_dt is null");
            return (Criteria) this;
        }

        public Criteria andAckDtIsNotNull() {
            addCriterion("ack_dt is not null");
            return (Criteria) this;
        }

        public Criteria andAckDtEqualTo(String value) {
            addCriterion("ack_dt =", value, "ackDt");
            return (Criteria) this;
        }

        public Criteria andAckDtNotEqualTo(String value) {
            addCriterion("ack_dt <>", value, "ackDt");
            return (Criteria) this;
        }

        public Criteria andAckDtGreaterThan(String value) {
            addCriterion("ack_dt >", value, "ackDt");
            return (Criteria) this;
        }

        public Criteria andAckDtGreaterThanOrEqualTo(String value) {
            addCriterion("ack_dt >=", value, "ackDt");
            return (Criteria) this;
        }

        public Criteria andAckDtLessThan(String value) {
            addCriterion("ack_dt <", value, "ackDt");
            return (Criteria) this;
        }

        public Criteria andAckDtLessThanOrEqualTo(String value) {
            addCriterion("ack_dt <=", value, "ackDt");
            return (Criteria) this;
        }

        public Criteria andAckDtLike(String value) {
            addCriterion("ack_dt like", value, "ackDt");
            return (Criteria) this;
        }

        public Criteria andAckDtNotLike(String value) {
            addCriterion("ack_dt not like", value, "ackDt");
            return (Criteria) this;
        }

        public Criteria andAckDtIn(List<String> values) {
            addCriterion("ack_dt in", values, "ackDt");
            return (Criteria) this;
        }

        public Criteria andAckDtNotIn(List<String> values) {
            addCriterion("ack_dt not in", values, "ackDt");
            return (Criteria) this;
        }

        public Criteria andAckDtBetween(String value1, String value2) {
            addCriterion("ack_dt between", value1, value2, "ackDt");
            return (Criteria) this;
        }

        public Criteria andAckDtNotBetween(String value1, String value2) {
            addCriterion("ack_dt not between", value1, value2, "ackDt");
            return (Criteria) this;
        }

        public Criteria andTransferInFundCodeIsNull() {
            addCriterion("transfer_in_fund_code is null");
            return (Criteria) this;
        }

        public Criteria andTransferInFundCodeIsNotNull() {
            addCriterion("transfer_in_fund_code is not null");
            return (Criteria) this;
        }

        public Criteria andTransferInFundCodeEqualTo(String value) {
            addCriterion("transfer_in_fund_code =", value, "transferInFundCode");
            return (Criteria) this;
        }

        public Criteria andTransferInFundCodeNotEqualTo(String value) {
            addCriterion("transfer_in_fund_code <>", value, "transferInFundCode");
            return (Criteria) this;
        }

        public Criteria andTransferInFundCodeGreaterThan(String value) {
            addCriterion("transfer_in_fund_code >", value, "transferInFundCode");
            return (Criteria) this;
        }

        public Criteria andTransferInFundCodeGreaterThanOrEqualTo(String value) {
            addCriterion("transfer_in_fund_code >=", value, "transferInFundCode");
            return (Criteria) this;
        }

        public Criteria andTransferInFundCodeLessThan(String value) {
            addCriterion("transfer_in_fund_code <", value, "transferInFundCode");
            return (Criteria) this;
        }

        public Criteria andTransferInFundCodeLessThanOrEqualTo(String value) {
            addCriterion("transfer_in_fund_code <=", value, "transferInFundCode");
            return (Criteria) this;
        }

        public Criteria andTransferInFundCodeLike(String value) {
            addCriterion("transfer_in_fund_code like", value, "transferInFundCode");
            return (Criteria) this;
        }

        public Criteria andTransferInFundCodeNotLike(String value) {
            addCriterion("transfer_in_fund_code not like", value, "transferInFundCode");
            return (Criteria) this;
        }

        public Criteria andTransferInFundCodeIn(List<String> values) {
            addCriterion("transfer_in_fund_code in", values, "transferInFundCode");
            return (Criteria) this;
        }

        public Criteria andTransferInFundCodeNotIn(List<String> values) {
            addCriterion("transfer_in_fund_code not in", values, "transferInFundCode");
            return (Criteria) this;
        }

        public Criteria andTransferInFundCodeBetween(String value1, String value2) {
            addCriterion("transfer_in_fund_code between", value1, value2, "transferInFundCode");
            return (Criteria) this;
        }

        public Criteria andTransferInFundCodeNotBetween(String value1, String value2) {
            addCriterion("transfer_in_fund_code not between", value1, value2, "transferInFundCode");
            return (Criteria) this;
        }

        public Criteria andTransferInFundNameIsNull() {
            addCriterion("transfer_in_fund_name is null");
            return (Criteria) this;
        }

        public Criteria andTransferInFundNameIsNotNull() {
            addCriterion("transfer_in_fund_name is not null");
            return (Criteria) this;
        }

        public Criteria andTransferInFundNameEqualTo(String value) {
            addCriterion("transfer_in_fund_name =", value, "transferInFundName");
            return (Criteria) this;
        }

        public Criteria andTransferInFundNameNotEqualTo(String value) {
            addCriterion("transfer_in_fund_name <>", value, "transferInFundName");
            return (Criteria) this;
        }

        public Criteria andTransferInFundNameGreaterThan(String value) {
            addCriterion("transfer_in_fund_name >", value, "transferInFundName");
            return (Criteria) this;
        }

        public Criteria andTransferInFundNameGreaterThanOrEqualTo(String value) {
            addCriterion("transfer_in_fund_name >=", value, "transferInFundName");
            return (Criteria) this;
        }

        public Criteria andTransferInFundNameLessThan(String value) {
            addCriterion("transfer_in_fund_name <", value, "transferInFundName");
            return (Criteria) this;
        }

        public Criteria andTransferInFundNameLessThanOrEqualTo(String value) {
            addCriterion("transfer_in_fund_name <=", value, "transferInFundName");
            return (Criteria) this;
        }

        public Criteria andTransferInFundNameLike(String value) {
            addCriterion("transfer_in_fund_name like", value, "transferInFundName");
            return (Criteria) this;
        }

        public Criteria andTransferInFundNameNotLike(String value) {
            addCriterion("transfer_in_fund_name not like", value, "transferInFundName");
            return (Criteria) this;
        }

        public Criteria andTransferInFundNameIn(List<String> values) {
            addCriterion("transfer_in_fund_name in", values, "transferInFundName");
            return (Criteria) this;
        }

        public Criteria andTransferInFundNameNotIn(List<String> values) {
            addCriterion("transfer_in_fund_name not in", values, "transferInFundName");
            return (Criteria) this;
        }

        public Criteria andTransferInFundNameBetween(String value1, String value2) {
            addCriterion("transfer_in_fund_name between", value1, value2, "transferInFundName");
            return (Criteria) this;
        }

        public Criteria andTransferInFundNameNotBetween(String value1, String value2) {
            addCriterion("transfer_in_fund_name not between", value1, value2, "transferInFundName");
            return (Criteria) this;
        }

        public Criteria andTransferInAckVolIsNull() {
            addCriterion("transfer_in_ack_vol is null");
            return (Criteria) this;
        }

        public Criteria andTransferInAckVolIsNotNull() {
            addCriterion("transfer_in_ack_vol is not null");
            return (Criteria) this;
        }

        public Criteria andTransferInAckVolEqualTo(BigDecimal value) {
            addCriterion("transfer_in_ack_vol =", value, "transferInAckVol");
            return (Criteria) this;
        }

        public Criteria andTransferInAckVolNotEqualTo(BigDecimal value) {
            addCriterion("transfer_in_ack_vol <>", value, "transferInAckVol");
            return (Criteria) this;
        }

        public Criteria andTransferInAckVolGreaterThan(BigDecimal value) {
            addCriterion("transfer_in_ack_vol >", value, "transferInAckVol");
            return (Criteria) this;
        }

        public Criteria andTransferInAckVolGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("transfer_in_ack_vol >=", value, "transferInAckVol");
            return (Criteria) this;
        }

        public Criteria andTransferInAckVolLessThan(BigDecimal value) {
            addCriterion("transfer_in_ack_vol <", value, "transferInAckVol");
            return (Criteria) this;
        }

        public Criteria andTransferInAckVolLessThanOrEqualTo(BigDecimal value) {
            addCriterion("transfer_in_ack_vol <=", value, "transferInAckVol");
            return (Criteria) this;
        }

        public Criteria andTransferInAckVolIn(List<BigDecimal> values) {
            addCriterion("transfer_in_ack_vol in", values, "transferInAckVol");
            return (Criteria) this;
        }

        public Criteria andTransferInAckVolNotIn(List<BigDecimal> values) {
            addCriterion("transfer_in_ack_vol not in", values, "transferInAckVol");
            return (Criteria) this;
        }

        public Criteria andTransferInAckVolBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("transfer_in_ack_vol between", value1, value2, "transferInAckVol");
            return (Criteria) this;
        }

        public Criteria andTransferInAckVolNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("transfer_in_ack_vol not between", value1, value2, "transferInAckVol");
            return (Criteria) this;
        }

        public Criteria andTransferInAckAmtIsNull() {
            addCriterion("transfer_in_ack_amt is null");
            return (Criteria) this;
        }

        public Criteria andTransferInAckAmtIsNotNull() {
            addCriterion("transfer_in_ack_amt is not null");
            return (Criteria) this;
        }

        public Criteria andTransferInAckAmtEqualTo(BigDecimal value) {
            addCriterion("transfer_in_ack_amt =", value, "transferInAckAmt");
            return (Criteria) this;
        }

        public Criteria andTransferInAckAmtNotEqualTo(BigDecimal value) {
            addCriterion("transfer_in_ack_amt <>", value, "transferInAckAmt");
            return (Criteria) this;
        }

        public Criteria andTransferInAckAmtGreaterThan(BigDecimal value) {
            addCriterion("transfer_in_ack_amt >", value, "transferInAckAmt");
            return (Criteria) this;
        }

        public Criteria andTransferInAckAmtGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("transfer_in_ack_amt >=", value, "transferInAckAmt");
            return (Criteria) this;
        }

        public Criteria andTransferInAckAmtLessThan(BigDecimal value) {
            addCriterion("transfer_in_ack_amt <", value, "transferInAckAmt");
            return (Criteria) this;
        }

        public Criteria andTransferInAckAmtLessThanOrEqualTo(BigDecimal value) {
            addCriterion("transfer_in_ack_amt <=", value, "transferInAckAmt");
            return (Criteria) this;
        }

        public Criteria andTransferInAckAmtIn(List<BigDecimal> values) {
            addCriterion("transfer_in_ack_amt in", values, "transferInAckAmt");
            return (Criteria) this;
        }

        public Criteria andTransferInAckAmtNotIn(List<BigDecimal> values) {
            addCriterion("transfer_in_ack_amt not in", values, "transferInAckAmt");
            return (Criteria) this;
        }

        public Criteria andTransferInAckAmtBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("transfer_in_ack_amt between", value1, value2, "transferInAckAmt");
            return (Criteria) this;
        }

        public Criteria andTransferInAckAmtNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("transfer_in_ack_amt not between", value1, value2, "transferInAckAmt");
            return (Criteria) this;
        }

        public Criteria andTransferInMainFundCodeIsNull() {
            addCriterion("transfer_in_main_fund_code is null");
            return (Criteria) this;
        }

        public Criteria andTransferInMainFundCodeIsNotNull() {
            addCriterion("transfer_in_main_fund_code is not null");
            return (Criteria) this;
        }

        public Criteria andTransferInMainFundCodeEqualTo(String value) {
            addCriterion("transfer_in_main_fund_code =", value, "transferInMainFundCode");
            return (Criteria) this;
        }

        public Criteria andTransferInMainFundCodeNotEqualTo(String value) {
            addCriterion("transfer_in_main_fund_code <>", value, "transferInMainFundCode");
            return (Criteria) this;
        }

        public Criteria andTransferInMainFundCodeGreaterThan(String value) {
            addCriterion("transfer_in_main_fund_code >", value, "transferInMainFundCode");
            return (Criteria) this;
        }

        public Criteria andTransferInMainFundCodeGreaterThanOrEqualTo(String value) {
            addCriterion("transfer_in_main_fund_code >=", value, "transferInMainFundCode");
            return (Criteria) this;
        }

        public Criteria andTransferInMainFundCodeLessThan(String value) {
            addCriterion("transfer_in_main_fund_code <", value, "transferInMainFundCode");
            return (Criteria) this;
        }

        public Criteria andTransferInMainFundCodeLessThanOrEqualTo(String value) {
            addCriterion("transfer_in_main_fund_code <=", value, "transferInMainFundCode");
            return (Criteria) this;
        }

        public Criteria andTransferInMainFundCodeLike(String value) {
            addCriterion("transfer_in_main_fund_code like", value, "transferInMainFundCode");
            return (Criteria) this;
        }

        public Criteria andTransferInMainFundCodeNotLike(String value) {
            addCriterion("transfer_in_main_fund_code not like", value, "transferInMainFundCode");
            return (Criteria) this;
        }

        public Criteria andTransferInMainFundCodeIn(List<String> values) {
            addCriterion("transfer_in_main_fund_code in", values, "transferInMainFundCode");
            return (Criteria) this;
        }

        public Criteria andTransferInMainFundCodeNotIn(List<String> values) {
            addCriterion("transfer_in_main_fund_code not in", values, "transferInMainFundCode");
            return (Criteria) this;
        }

        public Criteria andTransferInMainFundCodeBetween(String value1, String value2) {
            addCriterion("transfer_in_main_fund_code between", value1, value2, "transferInMainFundCode");
            return (Criteria) this;
        }

        public Criteria andTransferInMainFundCodeNotBetween(String value1, String value2) {
            addCriterion("transfer_in_main_fund_code not between", value1, value2, "transferInMainFundCode");
            return (Criteria) this;
        }

        public Criteria andTransferInAckNavIsNull() {
            addCriterion("transfer_in_ack_nav is null");
            return (Criteria) this;
        }

        public Criteria andTransferInAckNavIsNotNull() {
            addCriterion("transfer_in_ack_nav is not null");
            return (Criteria) this;
        }

        public Criteria andTransferInAckNavEqualTo(BigDecimal value) {
            addCriterion("transfer_in_ack_nav =", value, "transferInAckNav");
            return (Criteria) this;
        }

        public Criteria andTransferInAckNavNotEqualTo(BigDecimal value) {
            addCriterion("transfer_in_ack_nav <>", value, "transferInAckNav");
            return (Criteria) this;
        }

        public Criteria andTransferInAckNavGreaterThan(BigDecimal value) {
            addCriterion("transfer_in_ack_nav >", value, "transferInAckNav");
            return (Criteria) this;
        }

        public Criteria andTransferInAckNavGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("transfer_in_ack_nav >=", value, "transferInAckNav");
            return (Criteria) this;
        }

        public Criteria andTransferInAckNavLessThan(BigDecimal value) {
            addCriterion("transfer_in_ack_nav <", value, "transferInAckNav");
            return (Criteria) this;
        }

        public Criteria andTransferInAckNavLessThanOrEqualTo(BigDecimal value) {
            addCriterion("transfer_in_ack_nav <=", value, "transferInAckNav");
            return (Criteria) this;
        }

        public Criteria andTransferInAckNavIn(List<BigDecimal> values) {
            addCriterion("transfer_in_ack_nav in", values, "transferInAckNav");
            return (Criteria) this;
        }

        public Criteria andTransferInAckNavNotIn(List<BigDecimal> values) {
            addCriterion("transfer_in_ack_nav not in", values, "transferInAckNav");
            return (Criteria) this;
        }

        public Criteria andTransferInAckNavBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("transfer_in_ack_nav between", value1, value2, "transferInAckNav");
            return (Criteria) this;
        }

        public Criteria andTransferInAckNavNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("transfer_in_ack_nav not between", value1, value2, "transferInAckNav");
            return (Criteria) this;
        }

        public Criteria andBalanceFactorIsNull() {
            addCriterion("balance_factor is null");
            return (Criteria) this;
        }

        public Criteria andBalanceFactorIsNotNull() {
            addCriterion("balance_factor is not null");
            return (Criteria) this;
        }

        public Criteria andBalanceFactorEqualTo(BigDecimal value) {
            addCriterion("balance_factor =", value, "balanceFactor");
            return (Criteria) this;
        }

        public Criteria andBalanceFactorNotEqualTo(BigDecimal value) {
            addCriterion("balance_factor <>", value, "balanceFactor");
            return (Criteria) this;
        }

        public Criteria andBalanceFactorGreaterThan(BigDecimal value) {
            addCriterion("balance_factor >", value, "balanceFactor");
            return (Criteria) this;
        }

        public Criteria andBalanceFactorGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("balance_factor >=", value, "balanceFactor");
            return (Criteria) this;
        }

        public Criteria andBalanceFactorLessThan(BigDecimal value) {
            addCriterion("balance_factor <", value, "balanceFactor");
            return (Criteria) this;
        }

        public Criteria andBalanceFactorLessThanOrEqualTo(BigDecimal value) {
            addCriterion("balance_factor <=", value, "balanceFactor");
            return (Criteria) this;
        }

        public Criteria andBalanceFactorIn(List<BigDecimal> values) {
            addCriterion("balance_factor in", values, "balanceFactor");
            return (Criteria) this;
        }

        public Criteria andBalanceFactorNotIn(List<BigDecimal> values) {
            addCriterion("balance_factor not in", values, "balanceFactor");
            return (Criteria) this;
        }

        public Criteria andBalanceFactorBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("balance_factor between", value1, value2, "balanceFactor");
            return (Criteria) this;
        }

        public Criteria andBalanceFactorNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("balance_factor not between", value1, value2, "balanceFactor");
            return (Criteria) this;
        }

        public Criteria andTransferInBalanceFactorIsNull() {
            addCriterion("transfer_in_balance_factor is null");
            return (Criteria) this;
        }

        public Criteria andTransferInBalanceFactorIsNotNull() {
            addCriterion("transfer_in_balance_factor is not null");
            return (Criteria) this;
        }

        public Criteria andTransferInBalanceFactorEqualTo(BigDecimal value) {
            addCriterion("transfer_in_balance_factor =", value, "transferInBalanceFactor");
            return (Criteria) this;
        }

        public Criteria andTransferInBalanceFactorNotEqualTo(BigDecimal value) {
            addCriterion("transfer_in_balance_factor <>", value, "transferInBalanceFactor");
            return (Criteria) this;
        }

        public Criteria andTransferInBalanceFactorGreaterThan(BigDecimal value) {
            addCriterion("transfer_in_balance_factor >", value, "transferInBalanceFactor");
            return (Criteria) this;
        }

        public Criteria andTransferInBalanceFactorGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("transfer_in_balance_factor >=", value, "transferInBalanceFactor");
            return (Criteria) this;
        }

        public Criteria andTransferInBalanceFactorLessThan(BigDecimal value) {
            addCriterion("transfer_in_balance_factor <", value, "transferInBalanceFactor");
            return (Criteria) this;
        }

        public Criteria andTransferInBalanceFactorLessThanOrEqualTo(BigDecimal value) {
            addCriterion("transfer_in_balance_factor <=", value, "transferInBalanceFactor");
            return (Criteria) this;
        }

        public Criteria andTransferInBalanceFactorIn(List<BigDecimal> values) {
            addCriterion("transfer_in_balance_factor in", values, "transferInBalanceFactor");
            return (Criteria) this;
        }

        public Criteria andTransferInBalanceFactorNotIn(List<BigDecimal> values) {
            addCriterion("transfer_in_balance_factor not in", values, "transferInBalanceFactor");
            return (Criteria) this;
        }

        public Criteria andTransferInBalanceFactorBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("transfer_in_balance_factor between", value1, value2, "transferInBalanceFactor");
            return (Criteria) this;
        }

        public Criteria andTransferInBalanceFactorNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("transfer_in_balance_factor not between", value1, value2, "transferInBalanceFactor");
            return (Criteria) this;
        }

        public Criteria andAckNavDtIsNull() {
            addCriterion("ack_nav_dt is null");
            return (Criteria) this;
        }

        public Criteria andAckNavDtIsNotNull() {
            addCriterion("ack_nav_dt is not null");
            return (Criteria) this;
        }

        public Criteria andAckNavDtEqualTo(String value) {
            addCriterion("ack_nav_dt =", value, "ackNavDt");
            return (Criteria) this;
        }

        public Criteria andAckNavDtNotEqualTo(String value) {
            addCriterion("ack_nav_dt <>", value, "ackNavDt");
            return (Criteria) this;
        }

        public Criteria andAckNavDtGreaterThan(String value) {
            addCriterion("ack_nav_dt >", value, "ackNavDt");
            return (Criteria) this;
        }

        public Criteria andAckNavDtGreaterThanOrEqualTo(String value) {
            addCriterion("ack_nav_dt >=", value, "ackNavDt");
            return (Criteria) this;
        }

        public Criteria andAckNavDtLessThan(String value) {
            addCriterion("ack_nav_dt <", value, "ackNavDt");
            return (Criteria) this;
        }

        public Criteria andAckNavDtLessThanOrEqualTo(String value) {
            addCriterion("ack_nav_dt <=", value, "ackNavDt");
            return (Criteria) this;
        }

        public Criteria andAckNavDtLike(String value) {
            addCriterion("ack_nav_dt like", value, "ackNavDt");
            return (Criteria) this;
        }

        public Criteria andAckNavDtNotLike(String value) {
            addCriterion("ack_nav_dt not like", value, "ackNavDt");
            return (Criteria) this;
        }

        public Criteria andAckNavDtIn(List<String> values) {
            addCriterion("ack_nav_dt in", values, "ackNavDt");
            return (Criteria) this;
        }

        public Criteria andAckNavDtNotIn(List<String> values) {
            addCriterion("ack_nav_dt not in", values, "ackNavDt");
            return (Criteria) this;
        }

        public Criteria andAckNavDtBetween(String value1, String value2) {
            addCriterion("ack_nav_dt between", value1, value2, "ackNavDt");
            return (Criteria) this;
        }

        public Criteria andAckNavDtNotBetween(String value1, String value2) {
            addCriterion("ack_nav_dt not between", value1, value2, "ackNavDt");
            return (Criteria) this;
        }

        public Criteria andTransferInAckNavDtIsNull() {
            addCriterion("transfer_in_ack_nav_dt is null");
            return (Criteria) this;
        }

        public Criteria andTransferInAckNavDtIsNotNull() {
            addCriterion("transfer_in_ack_nav_dt is not null");
            return (Criteria) this;
        }

        public Criteria andTransferInAckNavDtEqualTo(String value) {
            addCriterion("transfer_in_ack_nav_dt =", value, "transferInAckNavDt");
            return (Criteria) this;
        }

        public Criteria andTransferInAckNavDtNotEqualTo(String value) {
            addCriterion("transfer_in_ack_nav_dt <>", value, "transferInAckNavDt");
            return (Criteria) this;
        }

        public Criteria andTransferInAckNavDtGreaterThan(String value) {
            addCriterion("transfer_in_ack_nav_dt >", value, "transferInAckNavDt");
            return (Criteria) this;
        }

        public Criteria andTransferInAckNavDtGreaterThanOrEqualTo(String value) {
            addCriterion("transfer_in_ack_nav_dt >=", value, "transferInAckNavDt");
            return (Criteria) this;
        }

        public Criteria andTransferInAckNavDtLessThan(String value) {
            addCriterion("transfer_in_ack_nav_dt <", value, "transferInAckNavDt");
            return (Criteria) this;
        }

        public Criteria andTransferInAckNavDtLessThanOrEqualTo(String value) {
            addCriterion("transfer_in_ack_nav_dt <=", value, "transferInAckNavDt");
            return (Criteria) this;
        }

        public Criteria andTransferInAckNavDtLike(String value) {
            addCriterion("transfer_in_ack_nav_dt like", value, "transferInAckNavDt");
            return (Criteria) this;
        }

        public Criteria andTransferInAckNavDtNotLike(String value) {
            addCriterion("transfer_in_ack_nav_dt not like", value, "transferInAckNavDt");
            return (Criteria) this;
        }

        public Criteria andTransferInAckNavDtIn(List<String> values) {
            addCriterion("transfer_in_ack_nav_dt in", values, "transferInAckNavDt");
            return (Criteria) this;
        }

        public Criteria andTransferInAckNavDtNotIn(List<String> values) {
            addCriterion("transfer_in_ack_nav_dt not in", values, "transferInAckNavDt");
            return (Criteria) this;
        }

        public Criteria andTransferInAckNavDtBetween(String value1, String value2) {
            addCriterion("transfer_in_ack_nav_dt between", value1, value2, "transferInAckNavDt");
            return (Criteria) this;
        }

        public Criteria andTransferInAckNavDtNotBetween(String value1, String value2) {
            addCriterion("transfer_in_ack_nav_dt not between", value1, value2, "transferInAckNavDt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
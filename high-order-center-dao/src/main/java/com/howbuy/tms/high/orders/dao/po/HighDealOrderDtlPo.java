package com.howbuy.tms.high.orders.dao.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class HighDealOrderDtlPo implements Serializable {
    private Long id;

    private String dealDtlNo;

    private String dealNo;

    private String fundCode;

    private String fundName;

    private String fundType;

    private String fundSubType;

    private String fundShareClass;

    private BigDecimal appAmt;

    private BigDecimal appVol;

    private String redeemDirection;

    private BigDecimal discountRate;

    private String txAppFlag;

    private String riskFlag;

    private String largeRedmFlag;

    private String allowDt;

    private String fundDivMode;

    private String mBusiCode;

    private BigDecimal fee;

    private BigDecimal ackAmt;

    private BigDecimal ackVol;

    private String ackDt;

    private String txAckFlag;

    private String taTradeDt;

    private String cancelOrderSrc;

    private String notifySubmitFlag;

    private String memo;

    private Date updateDtm;

    private Date createDtm;

    private String disCode;

    private String outletCode;

    private String taCode;

    private String txAcctNo;

    private BigDecimal nav;

    private String productChannel;

    private BigDecimal appointmentDiscount;

    private BigDecimal esitmateFee;

    private String firstBuyFlag;

    private String esignatureFlag;

    private String econtractFlag;

    private String unusualTransType;

    private String openEndTime;

    private String productClass;

    private BigDecimal advanceAmt;

    private String limitType;

    private String supportAdvanceFlag;

    private Integer calmTime;

    private BigDecimal interest;

    private BigDecimal achievementPay;

    private BigDecimal achievementCompen;

    private BigDecimal volByInterest;

    private BigDecimal recuperateFee;

    private String feeCalMode;

    private Date calmDtm;

    private String custRiskLevel;

    private String fundRiskLevel;

    private String orderFormType;

    private BigDecimal tAckVol;

    private BigDecimal ackGrams;

    private String submitTaDt;

    private String appointmentDealNoType;

    private String discountModel;

    private String cpAcctNo;

    private String protocolNo;

    private String protocolType;

    private String bankAcct;

    private String bankCode;

    private String dualentryStatus;

    private Date dualentryFinishDtm;

    private String dualentryInterposeFlag;

    private String callbackStatus;

    private Date callbackFinishDtm;

    private String callbackInterposeFlag;

    private String calmdtmInterposeFlag;

    private String assetcertificateStatus;

    private String assetInterposeFlag;

    private Date retrieveDtm;

    private String appointmentDealNo;

    private String qualificationType;

    private String refundDt;

    private String isRedeemExpire;

    private String preExpireDate;

    private String submitTaDtInterposeFlag;

    private String repurchaseProtocolNo;

    private String joinDt;

    private String forceRedeemFlag;

    private BigDecimal preAppVol;

    private String forceRedeemMemo;

    private String cxgDealNo;

    private BigDecimal netAppAmt;

    private String appointId;

    private BigDecimal subsAmt;

    private String mergeSubmitFlag;

    private String mainDealOrderNo;

    private String contractVersion;

    private String highFundInvPlanFlag;

    private BigDecimal agencyFee;

    private BigDecimal otherFee1;

    private BigDecimal transferFee;

    private BigDecimal achievPay;

    private BigDecimal totalTransFee;

    private String adjustFlag;

    private String transDirect;

    private String transferReason;

    private String divDt;

    private String originSerialNo;

    private String payOutStatus;

    private String payOutDt;

    private String continuanceFlag;

    private String stageFlag;

    private BigDecimal transferPrice;

    private String isNoTradeTransfer;

    private String taAckNo;

    private BigDecimal discountAmt;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDealDtlNo() {
        return dealDtlNo;
    }

    public void setDealDtlNo(String dealDtlNo) {
        this.dealDtlNo = dealDtlNo == null ? null : dealDtlNo.trim();
    }

    public String getDealNo() {
        return dealNo;
    }

    public void setDealNo(String dealNo) {
        this.dealNo = dealNo == null ? null : dealNo.trim();
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode == null ? null : fundCode.trim();
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName == null ? null : fundName.trim();
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType == null ? null : fundType.trim();
    }

    public String getFundSubType() {
        return fundSubType;
    }

    public void setFundSubType(String fundSubType) {
        this.fundSubType = fundSubType == null ? null : fundSubType.trim();
    }

    public String getFundShareClass() {
        return fundShareClass;
    }

    public void setFundShareClass(String fundShareClass) {
        this.fundShareClass = fundShareClass == null ? null : fundShareClass.trim();
    }

    public BigDecimal getAppAmt() {
        return appAmt;
    }

    public void setAppAmt(BigDecimal appAmt) {
        this.appAmt = appAmt;
    }

    public BigDecimal getAppVol() {
        return appVol;
    }

    public void setAppVol(BigDecimal appVol) {
        this.appVol = appVol;
    }

    public String getRedeemDirection() {
        return redeemDirection;
    }

    public void setRedeemDirection(String redeemDirection) {
        this.redeemDirection = redeemDirection == null ? null : redeemDirection.trim();
    }

    public BigDecimal getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(BigDecimal discountRate) {
        this.discountRate = discountRate;
    }

    public String getTxAppFlag() {
        return txAppFlag;
    }

    public void setTxAppFlag(String txAppFlag) {
        this.txAppFlag = txAppFlag == null ? null : txAppFlag.trim();
    }

    public String getRiskFlag() {
        return riskFlag;
    }

    public void setRiskFlag(String riskFlag) {
        this.riskFlag = riskFlag == null ? null : riskFlag.trim();
    }

    public String getLargeRedmFlag() {
        return largeRedmFlag;
    }

    public void setLargeRedmFlag(String largeRedmFlag) {
        this.largeRedmFlag = largeRedmFlag == null ? null : largeRedmFlag.trim();
    }

    public String getAllowDt() {
        return allowDt;
    }

    public void setAllowDt(String allowDt) {
        this.allowDt = allowDt == null ? null : allowDt.trim();
    }

    public String getFundDivMode() {
        return fundDivMode;
    }

    public void setFundDivMode(String fundDivMode) {
        this.fundDivMode = fundDivMode == null ? null : fundDivMode.trim();
    }

    public String getmBusiCode() {
        return mBusiCode;
    }

    public void setmBusiCode(String mBusiCode) {
        this.mBusiCode = mBusiCode == null ? null : mBusiCode.trim();
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public BigDecimal getAckAmt() {
        return ackAmt;
    }

    public void setAckAmt(BigDecimal ackAmt) {
        this.ackAmt = ackAmt;
    }

    public BigDecimal getAckVol() {
        return ackVol;
    }

    public void setAckVol(BigDecimal ackVol) {
        this.ackVol = ackVol;
    }

    public String getAckDt() {
        return ackDt;
    }

    public void setAckDt(String ackDt) {
        this.ackDt = ackDt == null ? null : ackDt.trim();
    }

    public String getTxAckFlag() {
        return txAckFlag;
    }

    public void setTxAckFlag(String txAckFlag) {
        this.txAckFlag = txAckFlag == null ? null : txAckFlag.trim();
    }

    public String getTaTradeDt() {
        return taTradeDt;
    }

    public void setTaTradeDt(String taTradeDt) {
        this.taTradeDt = taTradeDt == null ? null : taTradeDt.trim();
    }

    public String getCancelOrderSrc() {
        return cancelOrderSrc;
    }

    public void setCancelOrderSrc(String cancelOrderSrc) {
        this.cancelOrderSrc = cancelOrderSrc == null ? null : cancelOrderSrc.trim();
    }

    public String getNotifySubmitFlag() {
        return notifySubmitFlag;
    }

    public void setNotifySubmitFlag(String notifySubmitFlag) {
        this.notifySubmitFlag = notifySubmitFlag == null ? null : notifySubmitFlag.trim();
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo == null ? null : memo.trim();
    }

    public Date getUpdateDtm() {
        return updateDtm;
    }

    public void setUpdateDtm(Date updateDtm) {
        this.updateDtm = updateDtm;
    }

    public Date getCreateDtm() {
        return createDtm;
    }

    public void setCreateDtm(Date createDtm) {
        this.createDtm = createDtm;
    }

    public String getDisCode() {
        return disCode;
    }

    public void setDisCode(String disCode) {
        this.disCode = disCode == null ? null : disCode.trim();
    }

    public String getOutletCode() {
        return outletCode;
    }

    public void setOutletCode(String outletCode) {
        this.outletCode = outletCode == null ? null : outletCode.trim();
    }

    public String getTaCode() {
        return taCode;
    }

    public void setTaCode(String taCode) {
        this.taCode = taCode == null ? null : taCode.trim();
    }

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo == null ? null : txAcctNo.trim();
    }

    public BigDecimal getNav() {
        return nav;
    }

    public void setNav(BigDecimal nav) {
        this.nav = nav;
    }

    public String getProductChannel() {
        return productChannel;
    }

    public void setProductChannel(String productChannel) {
        this.productChannel = productChannel == null ? null : productChannel.trim();
    }

    public BigDecimal getAppointmentDiscount() {
        return appointmentDiscount;
    }

    public void setAppointmentDiscount(BigDecimal appointmentDiscount) {
        this.appointmentDiscount = appointmentDiscount;
    }

    public BigDecimal getEsitmateFee() {
        return esitmateFee;
    }

    public void setEsitmateFee(BigDecimal esitmateFee) {
        this.esitmateFee = esitmateFee;
    }

    public String getFirstBuyFlag() {
        return firstBuyFlag;
    }

    public void setFirstBuyFlag(String firstBuyFlag) {
        this.firstBuyFlag = firstBuyFlag == null ? null : firstBuyFlag.trim();
    }

    public String getEsignatureFlag() {
        return esignatureFlag;
    }

    public void setEsignatureFlag(String esignatureFlag) {
        this.esignatureFlag = esignatureFlag == null ? null : esignatureFlag.trim();
    }

    public String getEcontractFlag() {
        return econtractFlag;
    }

    public void setEcontractFlag(String econtractFlag) {
        this.econtractFlag = econtractFlag == null ? null : econtractFlag.trim();
    }

    public String getUnusualTransType() {
        return unusualTransType;
    }

    public void setUnusualTransType(String unusualTransType) {
        this.unusualTransType = unusualTransType == null ? null : unusualTransType.trim();
    }

    public String getOpenEndTime() {
        return openEndTime;
    }

    public void setOpenEndTime(String openEndTime) {
        this.openEndTime = openEndTime == null ? null : openEndTime.trim();
    }

    public String getProductClass() {
        return productClass;
    }

    public void setProductClass(String productClass) {
        this.productClass = productClass == null ? null : productClass.trim();
    }

    public BigDecimal getAdvanceAmt() {
        return advanceAmt;
    }

    public void setAdvanceAmt(BigDecimal advanceAmt) {
        this.advanceAmt = advanceAmt;
    }

    public String getLimitType() {
        return limitType;
    }

    public void setLimitType(String limitType) {
        this.limitType = limitType == null ? null : limitType.trim();
    }

    public String getSupportAdvanceFlag() {
        return supportAdvanceFlag;
    }

    public void setSupportAdvanceFlag(String supportAdvanceFlag) {
        this.supportAdvanceFlag = supportAdvanceFlag == null ? null : supportAdvanceFlag.trim();
    }

    public Integer getCalmTime() {
        return calmTime;
    }

    public void setCalmTime(Integer calmTime) {
        this.calmTime = calmTime;
    }

    public BigDecimal getInterest() {
        return interest;
    }

    public void setInterest(BigDecimal interest) {
        this.interest = interest;
    }

    public BigDecimal getAchievementPay() {
        return achievementPay;
    }

    public void setAchievementPay(BigDecimal achievementPay) {
        this.achievementPay = achievementPay;
    }

    public BigDecimal getAchievementCompen() {
        return achievementCompen;
    }

    public void setAchievementCompen(BigDecimal achievementCompen) {
        this.achievementCompen = achievementCompen;
    }

    public BigDecimal getVolByInterest() {
        return volByInterest;
    }

    public void setVolByInterest(BigDecimal volByInterest) {
        this.volByInterest = volByInterest;
    }

    public BigDecimal getRecuperateFee() {
        return recuperateFee;
    }

    public void setRecuperateFee(BigDecimal recuperateFee) {
        this.recuperateFee = recuperateFee;
    }

    public String getFeeCalMode() {
        return feeCalMode;
    }

    public void setFeeCalMode(String feeCalMode) {
        this.feeCalMode = feeCalMode == null ? null : feeCalMode.trim();
    }

    public Date getCalmDtm() {
        return calmDtm;
    }

    public void setCalmDtm(Date calmDtm) {
        this.calmDtm = calmDtm;
    }

    public String getCustRiskLevel() {
        return custRiskLevel;
    }

    public void setCustRiskLevel(String custRiskLevel) {
        this.custRiskLevel = custRiskLevel == null ? null : custRiskLevel.trim();
    }

    public String getFundRiskLevel() {
        return fundRiskLevel;
    }

    public void setFundRiskLevel(String fundRiskLevel) {
        this.fundRiskLevel = fundRiskLevel == null ? null : fundRiskLevel.trim();
    }

    public String getOrderFormType() {
        return orderFormType;
    }

    public void setOrderFormType(String orderFormType) {
        this.orderFormType = orderFormType == null ? null : orderFormType.trim();
    }

    public BigDecimal gettAckVol() {
        return tAckVol;
    }

    public void settAckVol(BigDecimal tAckVol) {
        this.tAckVol = tAckVol;
    }

    public BigDecimal getAckGrams() {
        return ackGrams;
    }

    public void setAckGrams(BigDecimal ackGrams) {
        this.ackGrams = ackGrams;
    }

    public String getSubmitTaDt() {
        return submitTaDt;
    }

    public void setSubmitTaDt(String submitTaDt) {
        this.submitTaDt = submitTaDt == null ? null : submitTaDt.trim();
    }

    public String getAppointmentDealNoType() {
        return appointmentDealNoType;
    }

    public void setAppointmentDealNoType(String appointmentDealNoType) {
        this.appointmentDealNoType = appointmentDealNoType == null ? null : appointmentDealNoType.trim();
    }

    public String getDiscountModel() {
        return discountModel;
    }

    public void setDiscountModel(String discountModel) {
        this.discountModel = discountModel == null ? null : discountModel.trim();
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo == null ? null : cpAcctNo.trim();
    }

    public String getProtocolNo() {
        return protocolNo;
    }

    public void setProtocolNo(String protocolNo) {
        this.protocolNo = protocolNo == null ? null : protocolNo.trim();
    }

    public String getProtocolType() {
        return protocolType;
    }

    public void setProtocolType(String protocolType) {
        this.protocolType = protocolType == null ? null : protocolType.trim();
    }

    public String getBankAcct() {
        return bankAcct;
    }

    public void setBankAcct(String bankAcct) {
        this.bankAcct = bankAcct == null ? null : bankAcct.trim();
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode == null ? null : bankCode.trim();
    }

    public String getDualentryStatus() {
        return dualentryStatus;
    }

    public void setDualentryStatus(String dualentryStatus) {
        this.dualentryStatus = dualentryStatus == null ? null : dualentryStatus.trim();
    }

    public Date getDualentryFinishDtm() {
        return dualentryFinishDtm;
    }

    public void setDualentryFinishDtm(Date dualentryFinishDtm) {
        this.dualentryFinishDtm = dualentryFinishDtm;
    }

    public String getDualentryInterposeFlag() {
        return dualentryInterposeFlag;
    }

    public void setDualentryInterposeFlag(String dualentryInterposeFlag) {
        this.dualentryInterposeFlag = dualentryInterposeFlag == null ? null : dualentryInterposeFlag.trim();
    }

    public String getCallbackStatus() {
        return callbackStatus;
    }

    public void setCallbackStatus(String callbackStatus) {
        this.callbackStatus = callbackStatus == null ? null : callbackStatus.trim();
    }

    public Date getCallbackFinishDtm() {
        return callbackFinishDtm;
    }

    public void setCallbackFinishDtm(Date callbackFinishDtm) {
        this.callbackFinishDtm = callbackFinishDtm;
    }

    public String getCallbackInterposeFlag() {
        return callbackInterposeFlag;
    }

    public void setCallbackInterposeFlag(String callbackInterposeFlag) {
        this.callbackInterposeFlag = callbackInterposeFlag == null ? null : callbackInterposeFlag.trim();
    }

    public String getCalmdtmInterposeFlag() {
        return calmdtmInterposeFlag;
    }

    public void setCalmdtmInterposeFlag(String calmdtmInterposeFlag) {
        this.calmdtmInterposeFlag = calmdtmInterposeFlag == null ? null : calmdtmInterposeFlag.trim();
    }

    public String getAssetcertificateStatus() {
        return assetcertificateStatus;
    }

    public void setAssetcertificateStatus(String assetcertificateStatus) {
        this.assetcertificateStatus = assetcertificateStatus == null ? null : assetcertificateStatus.trim();
    }

    public String getAssetInterposeFlag() {
        return assetInterposeFlag;
    }

    public void setAssetInterposeFlag(String assetInterposeFlag) {
        this.assetInterposeFlag = assetInterposeFlag == null ? null : assetInterposeFlag.trim();
    }

    public Date getRetrieveDtm() {
        return retrieveDtm;
    }

    public void setRetrieveDtm(Date retrieveDtm) {
        this.retrieveDtm = retrieveDtm;
    }

    public String getAppointmentDealNo() {
        return appointmentDealNo;
    }

    public void setAppointmentDealNo(String appointmentDealNo) {
        this.appointmentDealNo = appointmentDealNo == null ? null : appointmentDealNo.trim();
    }

    public String getQualificationType() {
        return qualificationType;
    }

    public void setQualificationType(String qualificationType) {
        this.qualificationType = qualificationType == null ? null : qualificationType.trim();
    }

    public String getRefundDt() {
        return refundDt;
    }

    public void setRefundDt(String refundDt) {
        this.refundDt = refundDt == null ? null : refundDt.trim();
    }

    public String getIsRedeemExpire() {
        return isRedeemExpire;
    }

    public void setIsRedeemExpire(String isRedeemExpire) {
        this.isRedeemExpire = isRedeemExpire == null ? null : isRedeemExpire.trim();
    }

    public String getPreExpireDate() {
        return preExpireDate;
    }

    public void setPreExpireDate(String preExpireDate) {
        this.preExpireDate = preExpireDate == null ? null : preExpireDate.trim();
    }

    public String getSubmitTaDtInterposeFlag() {
        return submitTaDtInterposeFlag;
    }

    public void setSubmitTaDtInterposeFlag(String submitTaDtInterposeFlag) {
        this.submitTaDtInterposeFlag = submitTaDtInterposeFlag == null ? null : submitTaDtInterposeFlag.trim();
    }

    public String getRepurchaseProtocolNo() {
        return repurchaseProtocolNo;
    }

    public void setRepurchaseProtocolNo(String repurchaseProtocolNo) {
        this.repurchaseProtocolNo = repurchaseProtocolNo == null ? null : repurchaseProtocolNo.trim();
    }

    public String getJoinDt() {
        return joinDt;
    }

    public void setJoinDt(String joinDt) {
        this.joinDt = joinDt == null ? null : joinDt.trim();
    }

    public String getForceRedeemFlag() {
        return forceRedeemFlag;
    }

    public void setForceRedeemFlag(String forceRedeemFlag) {
        this.forceRedeemFlag = forceRedeemFlag == null ? null : forceRedeemFlag.trim();
    }

    public BigDecimal getPreAppVol() {
        return preAppVol;
    }

    public void setPreAppVol(BigDecimal preAppVol) {
        this.preAppVol = preAppVol;
    }

    public String getForceRedeemMemo() {
        return forceRedeemMemo;
    }

    public void setForceRedeemMemo(String forceRedeemMemo) {
        this.forceRedeemMemo = forceRedeemMemo == null ? null : forceRedeemMemo.trim();
    }

    public String getCxgDealNo() {
        return cxgDealNo;
    }

    public void setCxgDealNo(String cxgDealNo) {
        this.cxgDealNo = cxgDealNo == null ? null : cxgDealNo.trim();
    }

    public BigDecimal getNetAppAmt() {
        return netAppAmt;
    }

    public void setNetAppAmt(BigDecimal netAppAmt) {
        this.netAppAmt = netAppAmt;
    }

    public String getAppointId() {
        return appointId;
    }

    public void setAppointId(String appointId) {
        this.appointId = appointId == null ? null : appointId.trim();
    }

    public BigDecimal getSubsAmt() {
        return subsAmt;
    }

    public void setSubsAmt(BigDecimal subsAmt) {
        this.subsAmt = subsAmt;
    }

    public String getMergeSubmitFlag() {
        return mergeSubmitFlag;
    }

    public void setMergeSubmitFlag(String mergeSubmitFlag) {
        this.mergeSubmitFlag = mergeSubmitFlag == null ? null : mergeSubmitFlag.trim();
    }

    public String getMainDealOrderNo() {
        return mainDealOrderNo;
    }

    public void setMainDealOrderNo(String mainDealOrderNo) {
        this.mainDealOrderNo = mainDealOrderNo == null ? null : mainDealOrderNo.trim();
    }

    public String getContractVersion() {
        return contractVersion;
    }

    public void setContractVersion(String contractVersion) {
        this.contractVersion = contractVersion == null ? null : contractVersion.trim();
    }

    public String getHighFundInvPlanFlag() {
        return highFundInvPlanFlag;
    }

    public void setHighFundInvPlanFlag(String highFundInvPlanFlag) {
        this.highFundInvPlanFlag = highFundInvPlanFlag == null ? null : highFundInvPlanFlag.trim();
    }

    public BigDecimal getAgencyFee() {
        return agencyFee;
    }

    public void setAgencyFee(BigDecimal agencyFee) {
        this.agencyFee = agencyFee;
    }

    public BigDecimal getOtherFee1() {
        return otherFee1;
    }

    public void setOtherFee1(BigDecimal otherFee1) {
        this.otherFee1 = otherFee1;
    }

    public BigDecimal getTransferFee() {
        return transferFee;
    }

    public void setTransferFee(BigDecimal transferFee) {
        this.transferFee = transferFee;
    }

    public BigDecimal getAchievPay() {
        return achievPay;
    }

    public void setAchievPay(BigDecimal achievPay) {
        this.achievPay = achievPay;
    }

    public BigDecimal getTotalTransFee() {
        return totalTransFee;
    }

    public void setTotalTransFee(BigDecimal totalTransFee) {
        this.totalTransFee = totalTransFee;
    }

    public String getAdjustFlag() {
        return adjustFlag;
    }

    public void setAdjustFlag(String adjustFlag) {
        this.adjustFlag = adjustFlag == null ? null : adjustFlag.trim();
    }

    public String getTransDirect() {
        return transDirect;
    }

    public void setTransDirect(String transDirect) {
        this.transDirect = transDirect == null ? null : transDirect.trim();
    }

    public String getTransferReason() {
        return transferReason;
    }

    public void setTransferReason(String transferReason) {
        this.transferReason = transferReason == null ? null : transferReason.trim();
    }

    public String getDivDt() {
        return divDt;
    }

    public void setDivDt(String divDt) {
        this.divDt = divDt == null ? null : divDt.trim();
    }

    public String getOriginSerialNo() {
        return originSerialNo;
    }

    public void setOriginSerialNo(String originSerialNo) {
        this.originSerialNo = originSerialNo == null ? null : originSerialNo.trim();
    }

    public String getPayOutStatus() {
        return payOutStatus;
    }

    public void setPayOutStatus(String payOutStatus) {
        this.payOutStatus = payOutStatus == null ? null : payOutStatus.trim();
    }

    public String getPayOutDt() {
        return payOutDt;
    }

    public void setPayOutDt(String payOutDt) {
        this.payOutDt = payOutDt == null ? null : payOutDt.trim();
    }

    public String getContinuanceFlag() {
        return continuanceFlag;
    }

    public void setContinuanceFlag(String continuanceFlag) {
        this.continuanceFlag = continuanceFlag == null ? null : continuanceFlag.trim();
    }

    public String getStageFlag() {
        return stageFlag;
    }

    public void setStageFlag(String stageFlag) {
        this.stageFlag = stageFlag == null ? null : stageFlag.trim();
    }

    public BigDecimal getTransferPrice() {
        return transferPrice;
    }

    public void setTransferPrice(BigDecimal transferPrice) {
        this.transferPrice = transferPrice;
    }

    public String getIsNoTradeTransfer() {
        return isNoTradeTransfer;
    }

    public void setIsNoTradeTransfer(String isNoTradeTransfer) {
        this.isNoTradeTransfer = isNoTradeTransfer == null ? null : isNoTradeTransfer.trim();
    }

    public String getTaAckNo() {
        return taAckNo;
    }

    public void setTaAckNo(String taAckNo) {
        this.taAckNo = taAckNo == null ? null : taAckNo.trim();
    }

    public BigDecimal getDiscountAmt() {
        return discountAmt;
    }

    public void setDiscountAmt(BigDecimal discountAmt) {
        this.discountAmt = discountAmt;
    }
}
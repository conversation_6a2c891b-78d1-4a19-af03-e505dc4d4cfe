package com.howbuy.tms.high.orders.dao.po;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class HighDealOrderDtlPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public HighDealOrderDtlPoExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andDealDtlNoIsNull() {
            addCriterion("deal_dtl_no is null");
            return (Criteria) this;
        }

        public Criteria andDealDtlNoIsNotNull() {
            addCriterion("deal_dtl_no is not null");
            return (Criteria) this;
        }

        public Criteria andDealDtlNoEqualTo(String value) {
            addCriterion("deal_dtl_no =", value, "dealDtlNo");
            return (Criteria) this;
        }

        public Criteria andDealDtlNoNotEqualTo(String value) {
            addCriterion("deal_dtl_no <>", value, "dealDtlNo");
            return (Criteria) this;
        }

        public Criteria andDealDtlNoGreaterThan(String value) {
            addCriterion("deal_dtl_no >", value, "dealDtlNo");
            return (Criteria) this;
        }

        public Criteria andDealDtlNoGreaterThanOrEqualTo(String value) {
            addCriterion("deal_dtl_no >=", value, "dealDtlNo");
            return (Criteria) this;
        }

        public Criteria andDealDtlNoLessThan(String value) {
            addCriterion("deal_dtl_no <", value, "dealDtlNo");
            return (Criteria) this;
        }

        public Criteria andDealDtlNoLessThanOrEqualTo(String value) {
            addCriterion("deal_dtl_no <=", value, "dealDtlNo");
            return (Criteria) this;
        }

        public Criteria andDealDtlNoLike(String value) {
            addCriterion("deal_dtl_no like", value, "dealDtlNo");
            return (Criteria) this;
        }

        public Criteria andDealDtlNoNotLike(String value) {
            addCriterion("deal_dtl_no not like", value, "dealDtlNo");
            return (Criteria) this;
        }

        public Criteria andDealDtlNoIn(List<String> values) {
            addCriterion("deal_dtl_no in", values, "dealDtlNo");
            return (Criteria) this;
        }

        public Criteria andDealDtlNoNotIn(List<String> values) {
            addCriterion("deal_dtl_no not in", values, "dealDtlNo");
            return (Criteria) this;
        }

        public Criteria andDealDtlNoBetween(String value1, String value2) {
            addCriterion("deal_dtl_no between", value1, value2, "dealDtlNo");
            return (Criteria) this;
        }

        public Criteria andDealDtlNoNotBetween(String value1, String value2) {
            addCriterion("deal_dtl_no not between", value1, value2, "dealDtlNo");
            return (Criteria) this;
        }

        public Criteria andDealNoIsNull() {
            addCriterion("deal_no is null");
            return (Criteria) this;
        }

        public Criteria andDealNoIsNotNull() {
            addCriterion("deal_no is not null");
            return (Criteria) this;
        }

        public Criteria andDealNoEqualTo(String value) {
            addCriterion("deal_no =", value, "dealNo");
            return (Criteria) this;
        }

        public Criteria andDealNoNotEqualTo(String value) {
            addCriterion("deal_no <>", value, "dealNo");
            return (Criteria) this;
        }

        public Criteria andDealNoGreaterThan(String value) {
            addCriterion("deal_no >", value, "dealNo");
            return (Criteria) this;
        }

        public Criteria andDealNoGreaterThanOrEqualTo(String value) {
            addCriterion("deal_no >=", value, "dealNo");
            return (Criteria) this;
        }

        public Criteria andDealNoLessThan(String value) {
            addCriterion("deal_no <", value, "dealNo");
            return (Criteria) this;
        }

        public Criteria andDealNoLessThanOrEqualTo(String value) {
            addCriterion("deal_no <=", value, "dealNo");
            return (Criteria) this;
        }

        public Criteria andDealNoLike(String value) {
            addCriterion("deal_no like", value, "dealNo");
            return (Criteria) this;
        }

        public Criteria andDealNoNotLike(String value) {
            addCriterion("deal_no not like", value, "dealNo");
            return (Criteria) this;
        }

        public Criteria andDealNoIn(List<String> values) {
            addCriterion("deal_no in", values, "dealNo");
            return (Criteria) this;
        }

        public Criteria andDealNoNotIn(List<String> values) {
            addCriterion("deal_no not in", values, "dealNo");
            return (Criteria) this;
        }

        public Criteria andDealNoBetween(String value1, String value2) {
            addCriterion("deal_no between", value1, value2, "dealNo");
            return (Criteria) this;
        }

        public Criteria andDealNoNotBetween(String value1, String value2) {
            addCriterion("deal_no not between", value1, value2, "dealNo");
            return (Criteria) this;
        }

        public Criteria andFundCodeIsNull() {
            addCriterion("fund_code is null");
            return (Criteria) this;
        }

        public Criteria andFundCodeIsNotNull() {
            addCriterion("fund_code is not null");
            return (Criteria) this;
        }

        public Criteria andFundCodeEqualTo(String value) {
            addCriterion("fund_code =", value, "fundCode");
            return (Criteria) this;
        }

        public Criteria andFundCodeNotEqualTo(String value) {
            addCriterion("fund_code <>", value, "fundCode");
            return (Criteria) this;
        }

        public Criteria andFundCodeGreaterThan(String value) {
            addCriterion("fund_code >", value, "fundCode");
            return (Criteria) this;
        }

        public Criteria andFundCodeGreaterThanOrEqualTo(String value) {
            addCriterion("fund_code >=", value, "fundCode");
            return (Criteria) this;
        }

        public Criteria andFundCodeLessThan(String value) {
            addCriterion("fund_code <", value, "fundCode");
            return (Criteria) this;
        }

        public Criteria andFundCodeLessThanOrEqualTo(String value) {
            addCriterion("fund_code <=", value, "fundCode");
            return (Criteria) this;
        }

        public Criteria andFundCodeLike(String value) {
            addCriterion("fund_code like", value, "fundCode");
            return (Criteria) this;
        }

        public Criteria andFundCodeNotLike(String value) {
            addCriterion("fund_code not like", value, "fundCode");
            return (Criteria) this;
        }

        public Criteria andFundCodeIn(List<String> values) {
            addCriterion("fund_code in", values, "fundCode");
            return (Criteria) this;
        }

        public Criteria andFundCodeNotIn(List<String> values) {
            addCriterion("fund_code not in", values, "fundCode");
            return (Criteria) this;
        }

        public Criteria andFundCodeBetween(String value1, String value2) {
            addCriterion("fund_code between", value1, value2, "fundCode");
            return (Criteria) this;
        }

        public Criteria andFundCodeNotBetween(String value1, String value2) {
            addCriterion("fund_code not between", value1, value2, "fundCode");
            return (Criteria) this;
        }

        public Criteria andFundNameIsNull() {
            addCriterion("fund_name is null");
            return (Criteria) this;
        }

        public Criteria andFundNameIsNotNull() {
            addCriterion("fund_name is not null");
            return (Criteria) this;
        }

        public Criteria andFundNameEqualTo(String value) {
            addCriterion("fund_name =", value, "fundName");
            return (Criteria) this;
        }

        public Criteria andFundNameNotEqualTo(String value) {
            addCriterion("fund_name <>", value, "fundName");
            return (Criteria) this;
        }

        public Criteria andFundNameGreaterThan(String value) {
            addCriterion("fund_name >", value, "fundName");
            return (Criteria) this;
        }

        public Criteria andFundNameGreaterThanOrEqualTo(String value) {
            addCriterion("fund_name >=", value, "fundName");
            return (Criteria) this;
        }

        public Criteria andFundNameLessThan(String value) {
            addCriterion("fund_name <", value, "fundName");
            return (Criteria) this;
        }

        public Criteria andFundNameLessThanOrEqualTo(String value) {
            addCriterion("fund_name <=", value, "fundName");
            return (Criteria) this;
        }

        public Criteria andFundNameLike(String value) {
            addCriterion("fund_name like", value, "fundName");
            return (Criteria) this;
        }

        public Criteria andFundNameNotLike(String value) {
            addCriterion("fund_name not like", value, "fundName");
            return (Criteria) this;
        }

        public Criteria andFundNameIn(List<String> values) {
            addCriterion("fund_name in", values, "fundName");
            return (Criteria) this;
        }

        public Criteria andFundNameNotIn(List<String> values) {
            addCriterion("fund_name not in", values, "fundName");
            return (Criteria) this;
        }

        public Criteria andFundNameBetween(String value1, String value2) {
            addCriterion("fund_name between", value1, value2, "fundName");
            return (Criteria) this;
        }

        public Criteria andFundNameNotBetween(String value1, String value2) {
            addCriterion("fund_name not between", value1, value2, "fundName");
            return (Criteria) this;
        }

        public Criteria andFundTypeIsNull() {
            addCriterion("fund_type is null");
            return (Criteria) this;
        }

        public Criteria andFundTypeIsNotNull() {
            addCriterion("fund_type is not null");
            return (Criteria) this;
        }

        public Criteria andFundTypeEqualTo(String value) {
            addCriterion("fund_type =", value, "fundType");
            return (Criteria) this;
        }

        public Criteria andFundTypeNotEqualTo(String value) {
            addCriterion("fund_type <>", value, "fundType");
            return (Criteria) this;
        }

        public Criteria andFundTypeGreaterThan(String value) {
            addCriterion("fund_type >", value, "fundType");
            return (Criteria) this;
        }

        public Criteria andFundTypeGreaterThanOrEqualTo(String value) {
            addCriterion("fund_type >=", value, "fundType");
            return (Criteria) this;
        }

        public Criteria andFundTypeLessThan(String value) {
            addCriterion("fund_type <", value, "fundType");
            return (Criteria) this;
        }

        public Criteria andFundTypeLessThanOrEqualTo(String value) {
            addCriterion("fund_type <=", value, "fundType");
            return (Criteria) this;
        }

        public Criteria andFundTypeLike(String value) {
            addCriterion("fund_type like", value, "fundType");
            return (Criteria) this;
        }

        public Criteria andFundTypeNotLike(String value) {
            addCriterion("fund_type not like", value, "fundType");
            return (Criteria) this;
        }

        public Criteria andFundTypeIn(List<String> values) {
            addCriterion("fund_type in", values, "fundType");
            return (Criteria) this;
        }

        public Criteria andFundTypeNotIn(List<String> values) {
            addCriterion("fund_type not in", values, "fundType");
            return (Criteria) this;
        }

        public Criteria andFundTypeBetween(String value1, String value2) {
            addCriterion("fund_type between", value1, value2, "fundType");
            return (Criteria) this;
        }

        public Criteria andFundTypeNotBetween(String value1, String value2) {
            addCriterion("fund_type not between", value1, value2, "fundType");
            return (Criteria) this;
        }

        public Criteria andFundSubTypeIsNull() {
            addCriterion("fund_sub_type is null");
            return (Criteria) this;
        }

        public Criteria andFundSubTypeIsNotNull() {
            addCriterion("fund_sub_type is not null");
            return (Criteria) this;
        }

        public Criteria andFundSubTypeEqualTo(String value) {
            addCriterion("fund_sub_type =", value, "fundSubType");
            return (Criteria) this;
        }

        public Criteria andFundSubTypeNotEqualTo(String value) {
            addCriterion("fund_sub_type <>", value, "fundSubType");
            return (Criteria) this;
        }

        public Criteria andFundSubTypeGreaterThan(String value) {
            addCriterion("fund_sub_type >", value, "fundSubType");
            return (Criteria) this;
        }

        public Criteria andFundSubTypeGreaterThanOrEqualTo(String value) {
            addCriterion("fund_sub_type >=", value, "fundSubType");
            return (Criteria) this;
        }

        public Criteria andFundSubTypeLessThan(String value) {
            addCriterion("fund_sub_type <", value, "fundSubType");
            return (Criteria) this;
        }

        public Criteria andFundSubTypeLessThanOrEqualTo(String value) {
            addCriterion("fund_sub_type <=", value, "fundSubType");
            return (Criteria) this;
        }

        public Criteria andFundSubTypeLike(String value) {
            addCriterion("fund_sub_type like", value, "fundSubType");
            return (Criteria) this;
        }

        public Criteria andFundSubTypeNotLike(String value) {
            addCriterion("fund_sub_type not like", value, "fundSubType");
            return (Criteria) this;
        }

        public Criteria andFundSubTypeIn(List<String> values) {
            addCriterion("fund_sub_type in", values, "fundSubType");
            return (Criteria) this;
        }

        public Criteria andFundSubTypeNotIn(List<String> values) {
            addCriterion("fund_sub_type not in", values, "fundSubType");
            return (Criteria) this;
        }

        public Criteria andFundSubTypeBetween(String value1, String value2) {
            addCriterion("fund_sub_type between", value1, value2, "fundSubType");
            return (Criteria) this;
        }

        public Criteria andFundSubTypeNotBetween(String value1, String value2) {
            addCriterion("fund_sub_type not between", value1, value2, "fundSubType");
            return (Criteria) this;
        }

        public Criteria andFundShareClassIsNull() {
            addCriterion("fund_share_class is null");
            return (Criteria) this;
        }

        public Criteria andFundShareClassIsNotNull() {
            addCriterion("fund_share_class is not null");
            return (Criteria) this;
        }

        public Criteria andFundShareClassEqualTo(String value) {
            addCriterion("fund_share_class =", value, "fundShareClass");
            return (Criteria) this;
        }

        public Criteria andFundShareClassNotEqualTo(String value) {
            addCriterion("fund_share_class <>", value, "fundShareClass");
            return (Criteria) this;
        }

        public Criteria andFundShareClassGreaterThan(String value) {
            addCriterion("fund_share_class >", value, "fundShareClass");
            return (Criteria) this;
        }

        public Criteria andFundShareClassGreaterThanOrEqualTo(String value) {
            addCriterion("fund_share_class >=", value, "fundShareClass");
            return (Criteria) this;
        }

        public Criteria andFundShareClassLessThan(String value) {
            addCriterion("fund_share_class <", value, "fundShareClass");
            return (Criteria) this;
        }

        public Criteria andFundShareClassLessThanOrEqualTo(String value) {
            addCriterion("fund_share_class <=", value, "fundShareClass");
            return (Criteria) this;
        }

        public Criteria andFundShareClassLike(String value) {
            addCriterion("fund_share_class like", value, "fundShareClass");
            return (Criteria) this;
        }

        public Criteria andFundShareClassNotLike(String value) {
            addCriterion("fund_share_class not like", value, "fundShareClass");
            return (Criteria) this;
        }

        public Criteria andFundShareClassIn(List<String> values) {
            addCriterion("fund_share_class in", values, "fundShareClass");
            return (Criteria) this;
        }

        public Criteria andFundShareClassNotIn(List<String> values) {
            addCriterion("fund_share_class not in", values, "fundShareClass");
            return (Criteria) this;
        }

        public Criteria andFundShareClassBetween(String value1, String value2) {
            addCriterion("fund_share_class between", value1, value2, "fundShareClass");
            return (Criteria) this;
        }

        public Criteria andFundShareClassNotBetween(String value1, String value2) {
            addCriterion("fund_share_class not between", value1, value2, "fundShareClass");
            return (Criteria) this;
        }

        public Criteria andAppAmtIsNull() {
            addCriterion("app_amt is null");
            return (Criteria) this;
        }

        public Criteria andAppAmtIsNotNull() {
            addCriterion("app_amt is not null");
            return (Criteria) this;
        }

        public Criteria andAppAmtEqualTo(BigDecimal value) {
            addCriterion("app_amt =", value, "appAmt");
            return (Criteria) this;
        }

        public Criteria andAppAmtNotEqualTo(BigDecimal value) {
            addCriterion("app_amt <>", value, "appAmt");
            return (Criteria) this;
        }

        public Criteria andAppAmtGreaterThan(BigDecimal value) {
            addCriterion("app_amt >", value, "appAmt");
            return (Criteria) this;
        }

        public Criteria andAppAmtGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("app_amt >=", value, "appAmt");
            return (Criteria) this;
        }

        public Criteria andAppAmtLessThan(BigDecimal value) {
            addCriterion("app_amt <", value, "appAmt");
            return (Criteria) this;
        }

        public Criteria andAppAmtLessThanOrEqualTo(BigDecimal value) {
            addCriterion("app_amt <=", value, "appAmt");
            return (Criteria) this;
        }

        public Criteria andAppAmtIn(List<BigDecimal> values) {
            addCriterion("app_amt in", values, "appAmt");
            return (Criteria) this;
        }

        public Criteria andAppAmtNotIn(List<BigDecimal> values) {
            addCriterion("app_amt not in", values, "appAmt");
            return (Criteria) this;
        }

        public Criteria andAppAmtBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("app_amt between", value1, value2, "appAmt");
            return (Criteria) this;
        }

        public Criteria andAppAmtNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("app_amt not between", value1, value2, "appAmt");
            return (Criteria) this;
        }

        public Criteria andAppVolIsNull() {
            addCriterion("app_vol is null");
            return (Criteria) this;
        }

        public Criteria andAppVolIsNotNull() {
            addCriterion("app_vol is not null");
            return (Criteria) this;
        }

        public Criteria andAppVolEqualTo(BigDecimal value) {
            addCriterion("app_vol =", value, "appVol");
            return (Criteria) this;
        }

        public Criteria andAppVolNotEqualTo(BigDecimal value) {
            addCriterion("app_vol <>", value, "appVol");
            return (Criteria) this;
        }

        public Criteria andAppVolGreaterThan(BigDecimal value) {
            addCriterion("app_vol >", value, "appVol");
            return (Criteria) this;
        }

        public Criteria andAppVolGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("app_vol >=", value, "appVol");
            return (Criteria) this;
        }

        public Criteria andAppVolLessThan(BigDecimal value) {
            addCriterion("app_vol <", value, "appVol");
            return (Criteria) this;
        }

        public Criteria andAppVolLessThanOrEqualTo(BigDecimal value) {
            addCriterion("app_vol <=", value, "appVol");
            return (Criteria) this;
        }

        public Criteria andAppVolIn(List<BigDecimal> values) {
            addCriterion("app_vol in", values, "appVol");
            return (Criteria) this;
        }

        public Criteria andAppVolNotIn(List<BigDecimal> values) {
            addCriterion("app_vol not in", values, "appVol");
            return (Criteria) this;
        }

        public Criteria andAppVolBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("app_vol between", value1, value2, "appVol");
            return (Criteria) this;
        }

        public Criteria andAppVolNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("app_vol not between", value1, value2, "appVol");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionIsNull() {
            addCriterion("redeem_direction is null");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionIsNotNull() {
            addCriterion("redeem_direction is not null");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionEqualTo(String value) {
            addCriterion("redeem_direction =", value, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionNotEqualTo(String value) {
            addCriterion("redeem_direction <>", value, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionGreaterThan(String value) {
            addCriterion("redeem_direction >", value, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionGreaterThanOrEqualTo(String value) {
            addCriterion("redeem_direction >=", value, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionLessThan(String value) {
            addCriterion("redeem_direction <", value, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionLessThanOrEqualTo(String value) {
            addCriterion("redeem_direction <=", value, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionLike(String value) {
            addCriterion("redeem_direction like", value, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionNotLike(String value) {
            addCriterion("redeem_direction not like", value, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionIn(List<String> values) {
            addCriterion("redeem_direction in", values, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionNotIn(List<String> values) {
            addCriterion("redeem_direction not in", values, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionBetween(String value1, String value2) {
            addCriterion("redeem_direction between", value1, value2, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andRedeemDirectionNotBetween(String value1, String value2) {
            addCriterion("redeem_direction not between", value1, value2, "redeemDirection");
            return (Criteria) this;
        }

        public Criteria andDiscountRateIsNull() {
            addCriterion("discount_rate is null");
            return (Criteria) this;
        }

        public Criteria andDiscountRateIsNotNull() {
            addCriterion("discount_rate is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountRateEqualTo(BigDecimal value) {
            addCriterion("discount_rate =", value, "discountRate");
            return (Criteria) this;
        }

        public Criteria andDiscountRateNotEqualTo(BigDecimal value) {
            addCriterion("discount_rate <>", value, "discountRate");
            return (Criteria) this;
        }

        public Criteria andDiscountRateGreaterThan(BigDecimal value) {
            addCriterion("discount_rate >", value, "discountRate");
            return (Criteria) this;
        }

        public Criteria andDiscountRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("discount_rate >=", value, "discountRate");
            return (Criteria) this;
        }

        public Criteria andDiscountRateLessThan(BigDecimal value) {
            addCriterion("discount_rate <", value, "discountRate");
            return (Criteria) this;
        }

        public Criteria andDiscountRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("discount_rate <=", value, "discountRate");
            return (Criteria) this;
        }

        public Criteria andDiscountRateIn(List<BigDecimal> values) {
            addCriterion("discount_rate in", values, "discountRate");
            return (Criteria) this;
        }

        public Criteria andDiscountRateNotIn(List<BigDecimal> values) {
            addCriterion("discount_rate not in", values, "discountRate");
            return (Criteria) this;
        }

        public Criteria andDiscountRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("discount_rate between", value1, value2, "discountRate");
            return (Criteria) this;
        }

        public Criteria andDiscountRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("discount_rate not between", value1, value2, "discountRate");
            return (Criteria) this;
        }

        public Criteria andTxAppFlagIsNull() {
            addCriterion("tx_app_flag is null");
            return (Criteria) this;
        }

        public Criteria andTxAppFlagIsNotNull() {
            addCriterion("tx_app_flag is not null");
            return (Criteria) this;
        }

        public Criteria andTxAppFlagEqualTo(String value) {
            addCriterion("tx_app_flag =", value, "txAppFlag");
            return (Criteria) this;
        }

        public Criteria andTxAppFlagNotEqualTo(String value) {
            addCriterion("tx_app_flag <>", value, "txAppFlag");
            return (Criteria) this;
        }

        public Criteria andTxAppFlagGreaterThan(String value) {
            addCriterion("tx_app_flag >", value, "txAppFlag");
            return (Criteria) this;
        }

        public Criteria andTxAppFlagGreaterThanOrEqualTo(String value) {
            addCriterion("tx_app_flag >=", value, "txAppFlag");
            return (Criteria) this;
        }

        public Criteria andTxAppFlagLessThan(String value) {
            addCriterion("tx_app_flag <", value, "txAppFlag");
            return (Criteria) this;
        }

        public Criteria andTxAppFlagLessThanOrEqualTo(String value) {
            addCriterion("tx_app_flag <=", value, "txAppFlag");
            return (Criteria) this;
        }

        public Criteria andTxAppFlagLike(String value) {
            addCriterion("tx_app_flag like", value, "txAppFlag");
            return (Criteria) this;
        }

        public Criteria andTxAppFlagNotLike(String value) {
            addCriterion("tx_app_flag not like", value, "txAppFlag");
            return (Criteria) this;
        }

        public Criteria andTxAppFlagIn(List<String> values) {
            addCriterion("tx_app_flag in", values, "txAppFlag");
            return (Criteria) this;
        }

        public Criteria andTxAppFlagNotIn(List<String> values) {
            addCriterion("tx_app_flag not in", values, "txAppFlag");
            return (Criteria) this;
        }

        public Criteria andTxAppFlagBetween(String value1, String value2) {
            addCriterion("tx_app_flag between", value1, value2, "txAppFlag");
            return (Criteria) this;
        }

        public Criteria andTxAppFlagNotBetween(String value1, String value2) {
            addCriterion("tx_app_flag not between", value1, value2, "txAppFlag");
            return (Criteria) this;
        }

        public Criteria andRiskFlagIsNull() {
            addCriterion("risk_flag is null");
            return (Criteria) this;
        }

        public Criteria andRiskFlagIsNotNull() {
            addCriterion("risk_flag is not null");
            return (Criteria) this;
        }

        public Criteria andRiskFlagEqualTo(String value) {
            addCriterion("risk_flag =", value, "riskFlag");
            return (Criteria) this;
        }

        public Criteria andRiskFlagNotEqualTo(String value) {
            addCriterion("risk_flag <>", value, "riskFlag");
            return (Criteria) this;
        }

        public Criteria andRiskFlagGreaterThan(String value) {
            addCriterion("risk_flag >", value, "riskFlag");
            return (Criteria) this;
        }

        public Criteria andRiskFlagGreaterThanOrEqualTo(String value) {
            addCriterion("risk_flag >=", value, "riskFlag");
            return (Criteria) this;
        }

        public Criteria andRiskFlagLessThan(String value) {
            addCriterion("risk_flag <", value, "riskFlag");
            return (Criteria) this;
        }

        public Criteria andRiskFlagLessThanOrEqualTo(String value) {
            addCriterion("risk_flag <=", value, "riskFlag");
            return (Criteria) this;
        }

        public Criteria andRiskFlagLike(String value) {
            addCriterion("risk_flag like", value, "riskFlag");
            return (Criteria) this;
        }

        public Criteria andRiskFlagNotLike(String value) {
            addCriterion("risk_flag not like", value, "riskFlag");
            return (Criteria) this;
        }

        public Criteria andRiskFlagIn(List<String> values) {
            addCriterion("risk_flag in", values, "riskFlag");
            return (Criteria) this;
        }

        public Criteria andRiskFlagNotIn(List<String> values) {
            addCriterion("risk_flag not in", values, "riskFlag");
            return (Criteria) this;
        }

        public Criteria andRiskFlagBetween(String value1, String value2) {
            addCriterion("risk_flag between", value1, value2, "riskFlag");
            return (Criteria) this;
        }

        public Criteria andRiskFlagNotBetween(String value1, String value2) {
            addCriterion("risk_flag not between", value1, value2, "riskFlag");
            return (Criteria) this;
        }

        public Criteria andLargeRedmFlagIsNull() {
            addCriterion("large_redm_flag is null");
            return (Criteria) this;
        }

        public Criteria andLargeRedmFlagIsNotNull() {
            addCriterion("large_redm_flag is not null");
            return (Criteria) this;
        }

        public Criteria andLargeRedmFlagEqualTo(String value) {
            addCriterion("large_redm_flag =", value, "largeRedmFlag");
            return (Criteria) this;
        }

        public Criteria andLargeRedmFlagNotEqualTo(String value) {
            addCriterion("large_redm_flag <>", value, "largeRedmFlag");
            return (Criteria) this;
        }

        public Criteria andLargeRedmFlagGreaterThan(String value) {
            addCriterion("large_redm_flag >", value, "largeRedmFlag");
            return (Criteria) this;
        }

        public Criteria andLargeRedmFlagGreaterThanOrEqualTo(String value) {
            addCriterion("large_redm_flag >=", value, "largeRedmFlag");
            return (Criteria) this;
        }

        public Criteria andLargeRedmFlagLessThan(String value) {
            addCriterion("large_redm_flag <", value, "largeRedmFlag");
            return (Criteria) this;
        }

        public Criteria andLargeRedmFlagLessThanOrEqualTo(String value) {
            addCriterion("large_redm_flag <=", value, "largeRedmFlag");
            return (Criteria) this;
        }

        public Criteria andLargeRedmFlagLike(String value) {
            addCriterion("large_redm_flag like", value, "largeRedmFlag");
            return (Criteria) this;
        }

        public Criteria andLargeRedmFlagNotLike(String value) {
            addCriterion("large_redm_flag not like", value, "largeRedmFlag");
            return (Criteria) this;
        }

        public Criteria andLargeRedmFlagIn(List<String> values) {
            addCriterion("large_redm_flag in", values, "largeRedmFlag");
            return (Criteria) this;
        }

        public Criteria andLargeRedmFlagNotIn(List<String> values) {
            addCriterion("large_redm_flag not in", values, "largeRedmFlag");
            return (Criteria) this;
        }

        public Criteria andLargeRedmFlagBetween(String value1, String value2) {
            addCriterion("large_redm_flag between", value1, value2, "largeRedmFlag");
            return (Criteria) this;
        }

        public Criteria andLargeRedmFlagNotBetween(String value1, String value2) {
            addCriterion("large_redm_flag not between", value1, value2, "largeRedmFlag");
            return (Criteria) this;
        }

        public Criteria andAllowDtIsNull() {
            addCriterion("allow_dt is null");
            return (Criteria) this;
        }

        public Criteria andAllowDtIsNotNull() {
            addCriterion("allow_dt is not null");
            return (Criteria) this;
        }

        public Criteria andAllowDtEqualTo(String value) {
            addCriterion("allow_dt =", value, "allowDt");
            return (Criteria) this;
        }

        public Criteria andAllowDtNotEqualTo(String value) {
            addCriterion("allow_dt <>", value, "allowDt");
            return (Criteria) this;
        }

        public Criteria andAllowDtGreaterThan(String value) {
            addCriterion("allow_dt >", value, "allowDt");
            return (Criteria) this;
        }

        public Criteria andAllowDtGreaterThanOrEqualTo(String value) {
            addCriterion("allow_dt >=", value, "allowDt");
            return (Criteria) this;
        }

        public Criteria andAllowDtLessThan(String value) {
            addCriterion("allow_dt <", value, "allowDt");
            return (Criteria) this;
        }

        public Criteria andAllowDtLessThanOrEqualTo(String value) {
            addCriterion("allow_dt <=", value, "allowDt");
            return (Criteria) this;
        }

        public Criteria andAllowDtLike(String value) {
            addCriterion("allow_dt like", value, "allowDt");
            return (Criteria) this;
        }

        public Criteria andAllowDtNotLike(String value) {
            addCriterion("allow_dt not like", value, "allowDt");
            return (Criteria) this;
        }

        public Criteria andAllowDtIn(List<String> values) {
            addCriterion("allow_dt in", values, "allowDt");
            return (Criteria) this;
        }

        public Criteria andAllowDtNotIn(List<String> values) {
            addCriterion("allow_dt not in", values, "allowDt");
            return (Criteria) this;
        }

        public Criteria andAllowDtBetween(String value1, String value2) {
            addCriterion("allow_dt between", value1, value2, "allowDt");
            return (Criteria) this;
        }

        public Criteria andAllowDtNotBetween(String value1, String value2) {
            addCriterion("allow_dt not between", value1, value2, "allowDt");
            return (Criteria) this;
        }

        public Criteria andFundDivModeIsNull() {
            addCriterion("fund_div_mode is null");
            return (Criteria) this;
        }

        public Criteria andFundDivModeIsNotNull() {
            addCriterion("fund_div_mode is not null");
            return (Criteria) this;
        }

        public Criteria andFundDivModeEqualTo(String value) {
            addCriterion("fund_div_mode =", value, "fundDivMode");
            return (Criteria) this;
        }

        public Criteria andFundDivModeNotEqualTo(String value) {
            addCriterion("fund_div_mode <>", value, "fundDivMode");
            return (Criteria) this;
        }

        public Criteria andFundDivModeGreaterThan(String value) {
            addCriterion("fund_div_mode >", value, "fundDivMode");
            return (Criteria) this;
        }

        public Criteria andFundDivModeGreaterThanOrEqualTo(String value) {
            addCriterion("fund_div_mode >=", value, "fundDivMode");
            return (Criteria) this;
        }

        public Criteria andFundDivModeLessThan(String value) {
            addCriterion("fund_div_mode <", value, "fundDivMode");
            return (Criteria) this;
        }

        public Criteria andFundDivModeLessThanOrEqualTo(String value) {
            addCriterion("fund_div_mode <=", value, "fundDivMode");
            return (Criteria) this;
        }

        public Criteria andFundDivModeLike(String value) {
            addCriterion("fund_div_mode like", value, "fundDivMode");
            return (Criteria) this;
        }

        public Criteria andFundDivModeNotLike(String value) {
            addCriterion("fund_div_mode not like", value, "fundDivMode");
            return (Criteria) this;
        }

        public Criteria andFundDivModeIn(List<String> values) {
            addCriterion("fund_div_mode in", values, "fundDivMode");
            return (Criteria) this;
        }

        public Criteria andFundDivModeNotIn(List<String> values) {
            addCriterion("fund_div_mode not in", values, "fundDivMode");
            return (Criteria) this;
        }

        public Criteria andFundDivModeBetween(String value1, String value2) {
            addCriterion("fund_div_mode between", value1, value2, "fundDivMode");
            return (Criteria) this;
        }

        public Criteria andFundDivModeNotBetween(String value1, String value2) {
            addCriterion("fund_div_mode not between", value1, value2, "fundDivMode");
            return (Criteria) this;
        }

        public Criteria andMBusiCodeIsNull() {
            addCriterion("m_busi_code is null");
            return (Criteria) this;
        }

        public Criteria andMBusiCodeIsNotNull() {
            addCriterion("m_busi_code is not null");
            return (Criteria) this;
        }

        public Criteria andMBusiCodeEqualTo(String value) {
            addCriterion("m_busi_code =", value, "mBusiCode");
            return (Criteria) this;
        }

        public Criteria andMBusiCodeNotEqualTo(String value) {
            addCriterion("m_busi_code <>", value, "mBusiCode");
            return (Criteria) this;
        }

        public Criteria andMBusiCodeGreaterThan(String value) {
            addCriterion("m_busi_code >", value, "mBusiCode");
            return (Criteria) this;
        }

        public Criteria andMBusiCodeGreaterThanOrEqualTo(String value) {
            addCriterion("m_busi_code >=", value, "mBusiCode");
            return (Criteria) this;
        }

        public Criteria andMBusiCodeLessThan(String value) {
            addCriterion("m_busi_code <", value, "mBusiCode");
            return (Criteria) this;
        }

        public Criteria andMBusiCodeLessThanOrEqualTo(String value) {
            addCriterion("m_busi_code <=", value, "mBusiCode");
            return (Criteria) this;
        }

        public Criteria andMBusiCodeLike(String value) {
            addCriterion("m_busi_code like", value, "mBusiCode");
            return (Criteria) this;
        }

        public Criteria andMBusiCodeNotLike(String value) {
            addCriterion("m_busi_code not like", value, "mBusiCode");
            return (Criteria) this;
        }

        public Criteria andMBusiCodeIn(List<String> values) {
            addCriterion("m_busi_code in", values, "mBusiCode");
            return (Criteria) this;
        }

        public Criteria andMBusiCodeNotIn(List<String> values) {
            addCriterion("m_busi_code not in", values, "mBusiCode");
            return (Criteria) this;
        }

        public Criteria andMBusiCodeBetween(String value1, String value2) {
            addCriterion("m_busi_code between", value1, value2, "mBusiCode");
            return (Criteria) this;
        }

        public Criteria andMBusiCodeNotBetween(String value1, String value2) {
            addCriterion("m_busi_code not between", value1, value2, "mBusiCode");
            return (Criteria) this;
        }

        public Criteria andFeeIsNull() {
            addCriterion("fee is null");
            return (Criteria) this;
        }

        public Criteria andFeeIsNotNull() {
            addCriterion("fee is not null");
            return (Criteria) this;
        }

        public Criteria andFeeEqualTo(BigDecimal value) {
            addCriterion("fee =", value, "fee");
            return (Criteria) this;
        }

        public Criteria andFeeNotEqualTo(BigDecimal value) {
            addCriterion("fee <>", value, "fee");
            return (Criteria) this;
        }

        public Criteria andFeeGreaterThan(BigDecimal value) {
            addCriterion("fee >", value, "fee");
            return (Criteria) this;
        }

        public Criteria andFeeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("fee >=", value, "fee");
            return (Criteria) this;
        }

        public Criteria andFeeLessThan(BigDecimal value) {
            addCriterion("fee <", value, "fee");
            return (Criteria) this;
        }

        public Criteria andFeeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("fee <=", value, "fee");
            return (Criteria) this;
        }

        public Criteria andFeeIn(List<BigDecimal> values) {
            addCriterion("fee in", values, "fee");
            return (Criteria) this;
        }

        public Criteria andFeeNotIn(List<BigDecimal> values) {
            addCriterion("fee not in", values, "fee");
            return (Criteria) this;
        }

        public Criteria andFeeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("fee between", value1, value2, "fee");
            return (Criteria) this;
        }

        public Criteria andFeeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("fee not between", value1, value2, "fee");
            return (Criteria) this;
        }

        public Criteria andAckAmtIsNull() {
            addCriterion("ack_amt is null");
            return (Criteria) this;
        }

        public Criteria andAckAmtIsNotNull() {
            addCriterion("ack_amt is not null");
            return (Criteria) this;
        }

        public Criteria andAckAmtEqualTo(BigDecimal value) {
            addCriterion("ack_amt =", value, "ackAmt");
            return (Criteria) this;
        }

        public Criteria andAckAmtNotEqualTo(BigDecimal value) {
            addCriterion("ack_amt <>", value, "ackAmt");
            return (Criteria) this;
        }

        public Criteria andAckAmtGreaterThan(BigDecimal value) {
            addCriterion("ack_amt >", value, "ackAmt");
            return (Criteria) this;
        }

        public Criteria andAckAmtGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("ack_amt >=", value, "ackAmt");
            return (Criteria) this;
        }

        public Criteria andAckAmtLessThan(BigDecimal value) {
            addCriterion("ack_amt <", value, "ackAmt");
            return (Criteria) this;
        }

        public Criteria andAckAmtLessThanOrEqualTo(BigDecimal value) {
            addCriterion("ack_amt <=", value, "ackAmt");
            return (Criteria) this;
        }

        public Criteria andAckAmtIn(List<BigDecimal> values) {
            addCriterion("ack_amt in", values, "ackAmt");
            return (Criteria) this;
        }

        public Criteria andAckAmtNotIn(List<BigDecimal> values) {
            addCriterion("ack_amt not in", values, "ackAmt");
            return (Criteria) this;
        }

        public Criteria andAckAmtBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ack_amt between", value1, value2, "ackAmt");
            return (Criteria) this;
        }

        public Criteria andAckAmtNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ack_amt not between", value1, value2, "ackAmt");
            return (Criteria) this;
        }

        public Criteria andAckVolIsNull() {
            addCriterion("ack_vol is null");
            return (Criteria) this;
        }

        public Criteria andAckVolIsNotNull() {
            addCriterion("ack_vol is not null");
            return (Criteria) this;
        }

        public Criteria andAckVolEqualTo(BigDecimal value) {
            addCriterion("ack_vol =", value, "ackVol");
            return (Criteria) this;
        }

        public Criteria andAckVolNotEqualTo(BigDecimal value) {
            addCriterion("ack_vol <>", value, "ackVol");
            return (Criteria) this;
        }

        public Criteria andAckVolGreaterThan(BigDecimal value) {
            addCriterion("ack_vol >", value, "ackVol");
            return (Criteria) this;
        }

        public Criteria andAckVolGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("ack_vol >=", value, "ackVol");
            return (Criteria) this;
        }

        public Criteria andAckVolLessThan(BigDecimal value) {
            addCriterion("ack_vol <", value, "ackVol");
            return (Criteria) this;
        }

        public Criteria andAckVolLessThanOrEqualTo(BigDecimal value) {
            addCriterion("ack_vol <=", value, "ackVol");
            return (Criteria) this;
        }

        public Criteria andAckVolIn(List<BigDecimal> values) {
            addCriterion("ack_vol in", values, "ackVol");
            return (Criteria) this;
        }

        public Criteria andAckVolNotIn(List<BigDecimal> values) {
            addCriterion("ack_vol not in", values, "ackVol");
            return (Criteria) this;
        }

        public Criteria andAckVolBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ack_vol between", value1, value2, "ackVol");
            return (Criteria) this;
        }

        public Criteria andAckVolNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ack_vol not between", value1, value2, "ackVol");
            return (Criteria) this;
        }

        public Criteria andAckDtIsNull() {
            addCriterion("ack_dt is null");
            return (Criteria) this;
        }

        public Criteria andAckDtIsNotNull() {
            addCriterion("ack_dt is not null");
            return (Criteria) this;
        }

        public Criteria andAckDtEqualTo(String value) {
            addCriterion("ack_dt =", value, "ackDt");
            return (Criteria) this;
        }

        public Criteria andAckDtNotEqualTo(String value) {
            addCriterion("ack_dt <>", value, "ackDt");
            return (Criteria) this;
        }

        public Criteria andAckDtGreaterThan(String value) {
            addCriterion("ack_dt >", value, "ackDt");
            return (Criteria) this;
        }

        public Criteria andAckDtGreaterThanOrEqualTo(String value) {
            addCriterion("ack_dt >=", value, "ackDt");
            return (Criteria) this;
        }

        public Criteria andAckDtLessThan(String value) {
            addCriterion("ack_dt <", value, "ackDt");
            return (Criteria) this;
        }

        public Criteria andAckDtLessThanOrEqualTo(String value) {
            addCriterion("ack_dt <=", value, "ackDt");
            return (Criteria) this;
        }

        public Criteria andAckDtLike(String value) {
            addCriterion("ack_dt like", value, "ackDt");
            return (Criteria) this;
        }

        public Criteria andAckDtNotLike(String value) {
            addCriterion("ack_dt not like", value, "ackDt");
            return (Criteria) this;
        }

        public Criteria andAckDtIn(List<String> values) {
            addCriterion("ack_dt in", values, "ackDt");
            return (Criteria) this;
        }

        public Criteria andAckDtNotIn(List<String> values) {
            addCriterion("ack_dt not in", values, "ackDt");
            return (Criteria) this;
        }

        public Criteria andAckDtBetween(String value1, String value2) {
            addCriterion("ack_dt between", value1, value2, "ackDt");
            return (Criteria) this;
        }

        public Criteria andAckDtNotBetween(String value1, String value2) {
            addCriterion("ack_dt not between", value1, value2, "ackDt");
            return (Criteria) this;
        }

        public Criteria andTxAckFlagIsNull() {
            addCriterion("tx_ack_flag is null");
            return (Criteria) this;
        }

        public Criteria andTxAckFlagIsNotNull() {
            addCriterion("tx_ack_flag is not null");
            return (Criteria) this;
        }

        public Criteria andTxAckFlagEqualTo(String value) {
            addCriterion("tx_ack_flag =", value, "txAckFlag");
            return (Criteria) this;
        }

        public Criteria andTxAckFlagNotEqualTo(String value) {
            addCriterion("tx_ack_flag <>", value, "txAckFlag");
            return (Criteria) this;
        }

        public Criteria andTxAckFlagGreaterThan(String value) {
            addCriterion("tx_ack_flag >", value, "txAckFlag");
            return (Criteria) this;
        }

        public Criteria andTxAckFlagGreaterThanOrEqualTo(String value) {
            addCriterion("tx_ack_flag >=", value, "txAckFlag");
            return (Criteria) this;
        }

        public Criteria andTxAckFlagLessThan(String value) {
            addCriterion("tx_ack_flag <", value, "txAckFlag");
            return (Criteria) this;
        }

        public Criteria andTxAckFlagLessThanOrEqualTo(String value) {
            addCriterion("tx_ack_flag <=", value, "txAckFlag");
            return (Criteria) this;
        }

        public Criteria andTxAckFlagLike(String value) {
            addCriterion("tx_ack_flag like", value, "txAckFlag");
            return (Criteria) this;
        }

        public Criteria andTxAckFlagNotLike(String value) {
            addCriterion("tx_ack_flag not like", value, "txAckFlag");
            return (Criteria) this;
        }

        public Criteria andTxAckFlagIn(List<String> values) {
            addCriterion("tx_ack_flag in", values, "txAckFlag");
            return (Criteria) this;
        }

        public Criteria andTxAckFlagNotIn(List<String> values) {
            addCriterion("tx_ack_flag not in", values, "txAckFlag");
            return (Criteria) this;
        }

        public Criteria andTxAckFlagBetween(String value1, String value2) {
            addCriterion("tx_ack_flag between", value1, value2, "txAckFlag");
            return (Criteria) this;
        }

        public Criteria andTxAckFlagNotBetween(String value1, String value2) {
            addCriterion("tx_ack_flag not between", value1, value2, "txAckFlag");
            return (Criteria) this;
        }

        public Criteria andTaTradeDtIsNull() {
            addCriterion("ta_trade_dt is null");
            return (Criteria) this;
        }

        public Criteria andTaTradeDtIsNotNull() {
            addCriterion("ta_trade_dt is not null");
            return (Criteria) this;
        }

        public Criteria andTaTradeDtEqualTo(String value) {
            addCriterion("ta_trade_dt =", value, "taTradeDt");
            return (Criteria) this;
        }

        public Criteria andTaTradeDtNotEqualTo(String value) {
            addCriterion("ta_trade_dt <>", value, "taTradeDt");
            return (Criteria) this;
        }

        public Criteria andTaTradeDtGreaterThan(String value) {
            addCriterion("ta_trade_dt >", value, "taTradeDt");
            return (Criteria) this;
        }

        public Criteria andTaTradeDtGreaterThanOrEqualTo(String value) {
            addCriterion("ta_trade_dt >=", value, "taTradeDt");
            return (Criteria) this;
        }

        public Criteria andTaTradeDtLessThan(String value) {
            addCriterion("ta_trade_dt <", value, "taTradeDt");
            return (Criteria) this;
        }

        public Criteria andTaTradeDtLessThanOrEqualTo(String value) {
            addCriterion("ta_trade_dt <=", value, "taTradeDt");
            return (Criteria) this;
        }

        public Criteria andTaTradeDtLike(String value) {
            addCriterion("ta_trade_dt like", value, "taTradeDt");
            return (Criteria) this;
        }

        public Criteria andTaTradeDtNotLike(String value) {
            addCriterion("ta_trade_dt not like", value, "taTradeDt");
            return (Criteria) this;
        }

        public Criteria andTaTradeDtIn(List<String> values) {
            addCriterion("ta_trade_dt in", values, "taTradeDt");
            return (Criteria) this;
        }

        public Criteria andTaTradeDtNotIn(List<String> values) {
            addCriterion("ta_trade_dt not in", values, "taTradeDt");
            return (Criteria) this;
        }

        public Criteria andTaTradeDtBetween(String value1, String value2) {
            addCriterion("ta_trade_dt between", value1, value2, "taTradeDt");
            return (Criteria) this;
        }

        public Criteria andTaTradeDtNotBetween(String value1, String value2) {
            addCriterion("ta_trade_dt not between", value1, value2, "taTradeDt");
            return (Criteria) this;
        }

        public Criteria andCancelOrderSrcIsNull() {
            addCriterion("cancel_order_src is null");
            return (Criteria) this;
        }

        public Criteria andCancelOrderSrcIsNotNull() {
            addCriterion("cancel_order_src is not null");
            return (Criteria) this;
        }

        public Criteria andCancelOrderSrcEqualTo(String value) {
            addCriterion("cancel_order_src =", value, "cancelOrderSrc");
            return (Criteria) this;
        }

        public Criteria andCancelOrderSrcNotEqualTo(String value) {
            addCriterion("cancel_order_src <>", value, "cancelOrderSrc");
            return (Criteria) this;
        }

        public Criteria andCancelOrderSrcGreaterThan(String value) {
            addCriterion("cancel_order_src >", value, "cancelOrderSrc");
            return (Criteria) this;
        }

        public Criteria andCancelOrderSrcGreaterThanOrEqualTo(String value) {
            addCriterion("cancel_order_src >=", value, "cancelOrderSrc");
            return (Criteria) this;
        }

        public Criteria andCancelOrderSrcLessThan(String value) {
            addCriterion("cancel_order_src <", value, "cancelOrderSrc");
            return (Criteria) this;
        }

        public Criteria andCancelOrderSrcLessThanOrEqualTo(String value) {
            addCriterion("cancel_order_src <=", value, "cancelOrderSrc");
            return (Criteria) this;
        }

        public Criteria andCancelOrderSrcLike(String value) {
            addCriterion("cancel_order_src like", value, "cancelOrderSrc");
            return (Criteria) this;
        }

        public Criteria andCancelOrderSrcNotLike(String value) {
            addCriterion("cancel_order_src not like", value, "cancelOrderSrc");
            return (Criteria) this;
        }

        public Criteria andCancelOrderSrcIn(List<String> values) {
            addCriterion("cancel_order_src in", values, "cancelOrderSrc");
            return (Criteria) this;
        }

        public Criteria andCancelOrderSrcNotIn(List<String> values) {
            addCriterion("cancel_order_src not in", values, "cancelOrderSrc");
            return (Criteria) this;
        }

        public Criteria andCancelOrderSrcBetween(String value1, String value2) {
            addCriterion("cancel_order_src between", value1, value2, "cancelOrderSrc");
            return (Criteria) this;
        }

        public Criteria andCancelOrderSrcNotBetween(String value1, String value2) {
            addCriterion("cancel_order_src not between", value1, value2, "cancelOrderSrc");
            return (Criteria) this;
        }

        public Criteria andNotifySubmitFlagIsNull() {
            addCriterion("notify_submit_flag is null");
            return (Criteria) this;
        }

        public Criteria andNotifySubmitFlagIsNotNull() {
            addCriterion("notify_submit_flag is not null");
            return (Criteria) this;
        }

        public Criteria andNotifySubmitFlagEqualTo(String value) {
            addCriterion("notify_submit_flag =", value, "notifySubmitFlag");
            return (Criteria) this;
        }

        public Criteria andNotifySubmitFlagNotEqualTo(String value) {
            addCriterion("notify_submit_flag <>", value, "notifySubmitFlag");
            return (Criteria) this;
        }

        public Criteria andNotifySubmitFlagGreaterThan(String value) {
            addCriterion("notify_submit_flag >", value, "notifySubmitFlag");
            return (Criteria) this;
        }

        public Criteria andNotifySubmitFlagGreaterThanOrEqualTo(String value) {
            addCriterion("notify_submit_flag >=", value, "notifySubmitFlag");
            return (Criteria) this;
        }

        public Criteria andNotifySubmitFlagLessThan(String value) {
            addCriterion("notify_submit_flag <", value, "notifySubmitFlag");
            return (Criteria) this;
        }

        public Criteria andNotifySubmitFlagLessThanOrEqualTo(String value) {
            addCriterion("notify_submit_flag <=", value, "notifySubmitFlag");
            return (Criteria) this;
        }

        public Criteria andNotifySubmitFlagLike(String value) {
            addCriterion("notify_submit_flag like", value, "notifySubmitFlag");
            return (Criteria) this;
        }

        public Criteria andNotifySubmitFlagNotLike(String value) {
            addCriterion("notify_submit_flag not like", value, "notifySubmitFlag");
            return (Criteria) this;
        }

        public Criteria andNotifySubmitFlagIn(List<String> values) {
            addCriterion("notify_submit_flag in", values, "notifySubmitFlag");
            return (Criteria) this;
        }

        public Criteria andNotifySubmitFlagNotIn(List<String> values) {
            addCriterion("notify_submit_flag not in", values, "notifySubmitFlag");
            return (Criteria) this;
        }

        public Criteria andNotifySubmitFlagBetween(String value1, String value2) {
            addCriterion("notify_submit_flag between", value1, value2, "notifySubmitFlag");
            return (Criteria) this;
        }

        public Criteria andNotifySubmitFlagNotBetween(String value1, String value2) {
            addCriterion("notify_submit_flag not between", value1, value2, "notifySubmitFlag");
            return (Criteria) this;
        }

        public Criteria andMemoIsNull() {
            addCriterion("memo is null");
            return (Criteria) this;
        }

        public Criteria andMemoIsNotNull() {
            addCriterion("memo is not null");
            return (Criteria) this;
        }

        public Criteria andMemoEqualTo(String value) {
            addCriterion("memo =", value, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoNotEqualTo(String value) {
            addCriterion("memo <>", value, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoGreaterThan(String value) {
            addCriterion("memo >", value, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoGreaterThanOrEqualTo(String value) {
            addCriterion("memo >=", value, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoLessThan(String value) {
            addCriterion("memo <", value, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoLessThanOrEqualTo(String value) {
            addCriterion("memo <=", value, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoLike(String value) {
            addCriterion("memo like", value, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoNotLike(String value) {
            addCriterion("memo not like", value, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoIn(List<String> values) {
            addCriterion("memo in", values, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoNotIn(List<String> values) {
            addCriterion("memo not in", values, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoBetween(String value1, String value2) {
            addCriterion("memo between", value1, value2, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoNotBetween(String value1, String value2) {
            addCriterion("memo not between", value1, value2, "memo");
            return (Criteria) this;
        }

        public Criteria andUpdateDtmIsNull() {
            addCriterion("update_dtm is null");
            return (Criteria) this;
        }

        public Criteria andUpdateDtmIsNotNull() {
            addCriterion("update_dtm is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateDtmEqualTo(Date value) {
            addCriterion("update_dtm =", value, "updateDtm");
            return (Criteria) this;
        }

        public Criteria andUpdateDtmNotEqualTo(Date value) {
            addCriterion("update_dtm <>", value, "updateDtm");
            return (Criteria) this;
        }

        public Criteria andUpdateDtmGreaterThan(Date value) {
            addCriterion("update_dtm >", value, "updateDtm");
            return (Criteria) this;
        }

        public Criteria andUpdateDtmGreaterThanOrEqualTo(Date value) {
            addCriterion("update_dtm >=", value, "updateDtm");
            return (Criteria) this;
        }

        public Criteria andUpdateDtmLessThan(Date value) {
            addCriterion("update_dtm <", value, "updateDtm");
            return (Criteria) this;
        }

        public Criteria andUpdateDtmLessThanOrEqualTo(Date value) {
            addCriterion("update_dtm <=", value, "updateDtm");
            return (Criteria) this;
        }

        public Criteria andUpdateDtmIn(List<Date> values) {
            addCriterion("update_dtm in", values, "updateDtm");
            return (Criteria) this;
        }

        public Criteria andUpdateDtmNotIn(List<Date> values) {
            addCriterion("update_dtm not in", values, "updateDtm");
            return (Criteria) this;
        }

        public Criteria andUpdateDtmBetween(Date value1, Date value2) {
            addCriterion("update_dtm between", value1, value2, "updateDtm");
            return (Criteria) this;
        }

        public Criteria andUpdateDtmNotBetween(Date value1, Date value2) {
            addCriterion("update_dtm not between", value1, value2, "updateDtm");
            return (Criteria) this;
        }

        public Criteria andCreateDtmIsNull() {
            addCriterion("create_dtm is null");
            return (Criteria) this;
        }

        public Criteria andCreateDtmIsNotNull() {
            addCriterion("create_dtm is not null");
            return (Criteria) this;
        }

        public Criteria andCreateDtmEqualTo(Date value) {
            addCriterion("create_dtm =", value, "createDtm");
            return (Criteria) this;
        }

        public Criteria andCreateDtmNotEqualTo(Date value) {
            addCriterion("create_dtm <>", value, "createDtm");
            return (Criteria) this;
        }

        public Criteria andCreateDtmGreaterThan(Date value) {
            addCriterion("create_dtm >", value, "createDtm");
            return (Criteria) this;
        }

        public Criteria andCreateDtmGreaterThanOrEqualTo(Date value) {
            addCriterion("create_dtm >=", value, "createDtm");
            return (Criteria) this;
        }

        public Criteria andCreateDtmLessThan(Date value) {
            addCriterion("create_dtm <", value, "createDtm");
            return (Criteria) this;
        }

        public Criteria andCreateDtmLessThanOrEqualTo(Date value) {
            addCriterion("create_dtm <=", value, "createDtm");
            return (Criteria) this;
        }

        public Criteria andCreateDtmIn(List<Date> values) {
            addCriterion("create_dtm in", values, "createDtm");
            return (Criteria) this;
        }

        public Criteria andCreateDtmNotIn(List<Date> values) {
            addCriterion("create_dtm not in", values, "createDtm");
            return (Criteria) this;
        }

        public Criteria andCreateDtmBetween(Date value1, Date value2) {
            addCriterion("create_dtm between", value1, value2, "createDtm");
            return (Criteria) this;
        }

        public Criteria andCreateDtmNotBetween(Date value1, Date value2) {
            addCriterion("create_dtm not between", value1, value2, "createDtm");
            return (Criteria) this;
        }

        public Criteria andDisCodeIsNull() {
            addCriterion("dis_code is null");
            return (Criteria) this;
        }

        public Criteria andDisCodeIsNotNull() {
            addCriterion("dis_code is not null");
            return (Criteria) this;
        }

        public Criteria andDisCodeEqualTo(String value) {
            addCriterion("dis_code =", value, "disCode");
            return (Criteria) this;
        }

        public Criteria andDisCodeNotEqualTo(String value) {
            addCriterion("dis_code <>", value, "disCode");
            return (Criteria) this;
        }

        public Criteria andDisCodeGreaterThan(String value) {
            addCriterion("dis_code >", value, "disCode");
            return (Criteria) this;
        }

        public Criteria andDisCodeGreaterThanOrEqualTo(String value) {
            addCriterion("dis_code >=", value, "disCode");
            return (Criteria) this;
        }

        public Criteria andDisCodeLessThan(String value) {
            addCriterion("dis_code <", value, "disCode");
            return (Criteria) this;
        }

        public Criteria andDisCodeLessThanOrEqualTo(String value) {
            addCriterion("dis_code <=", value, "disCode");
            return (Criteria) this;
        }

        public Criteria andDisCodeLike(String value) {
            addCriterion("dis_code like", value, "disCode");
            return (Criteria) this;
        }

        public Criteria andDisCodeNotLike(String value) {
            addCriterion("dis_code not like", value, "disCode");
            return (Criteria) this;
        }

        public Criteria andDisCodeIn(List<String> values) {
            addCriterion("dis_code in", values, "disCode");
            return (Criteria) this;
        }

        public Criteria andDisCodeNotIn(List<String> values) {
            addCriterion("dis_code not in", values, "disCode");
            return (Criteria) this;
        }

        public Criteria andDisCodeBetween(String value1, String value2) {
            addCriterion("dis_code between", value1, value2, "disCode");
            return (Criteria) this;
        }

        public Criteria andDisCodeNotBetween(String value1, String value2) {
            addCriterion("dis_code not between", value1, value2, "disCode");
            return (Criteria) this;
        }

        public Criteria andOutletCodeIsNull() {
            addCriterion("outlet_code is null");
            return (Criteria) this;
        }

        public Criteria andOutletCodeIsNotNull() {
            addCriterion("outlet_code is not null");
            return (Criteria) this;
        }

        public Criteria andOutletCodeEqualTo(String value) {
            addCriterion("outlet_code =", value, "outletCode");
            return (Criteria) this;
        }

        public Criteria andOutletCodeNotEqualTo(String value) {
            addCriterion("outlet_code <>", value, "outletCode");
            return (Criteria) this;
        }

        public Criteria andOutletCodeGreaterThan(String value) {
            addCriterion("outlet_code >", value, "outletCode");
            return (Criteria) this;
        }

        public Criteria andOutletCodeGreaterThanOrEqualTo(String value) {
            addCriterion("outlet_code >=", value, "outletCode");
            return (Criteria) this;
        }

        public Criteria andOutletCodeLessThan(String value) {
            addCriterion("outlet_code <", value, "outletCode");
            return (Criteria) this;
        }

        public Criteria andOutletCodeLessThanOrEqualTo(String value) {
            addCriterion("outlet_code <=", value, "outletCode");
            return (Criteria) this;
        }

        public Criteria andOutletCodeLike(String value) {
            addCriterion("outlet_code like", value, "outletCode");
            return (Criteria) this;
        }

        public Criteria andOutletCodeNotLike(String value) {
            addCriterion("outlet_code not like", value, "outletCode");
            return (Criteria) this;
        }

        public Criteria andOutletCodeIn(List<String> values) {
            addCriterion("outlet_code in", values, "outletCode");
            return (Criteria) this;
        }

        public Criteria andOutletCodeNotIn(List<String> values) {
            addCriterion("outlet_code not in", values, "outletCode");
            return (Criteria) this;
        }

        public Criteria andOutletCodeBetween(String value1, String value2) {
            addCriterion("outlet_code between", value1, value2, "outletCode");
            return (Criteria) this;
        }

        public Criteria andOutletCodeNotBetween(String value1, String value2) {
            addCriterion("outlet_code not between", value1, value2, "outletCode");
            return (Criteria) this;
        }

        public Criteria andTaCodeIsNull() {
            addCriterion("ta_code is null");
            return (Criteria) this;
        }

        public Criteria andTaCodeIsNotNull() {
            addCriterion("ta_code is not null");
            return (Criteria) this;
        }

        public Criteria andTaCodeEqualTo(String value) {
            addCriterion("ta_code =", value, "taCode");
            return (Criteria) this;
        }

        public Criteria andTaCodeNotEqualTo(String value) {
            addCriterion("ta_code <>", value, "taCode");
            return (Criteria) this;
        }

        public Criteria andTaCodeGreaterThan(String value) {
            addCriterion("ta_code >", value, "taCode");
            return (Criteria) this;
        }

        public Criteria andTaCodeGreaterThanOrEqualTo(String value) {
            addCriterion("ta_code >=", value, "taCode");
            return (Criteria) this;
        }

        public Criteria andTaCodeLessThan(String value) {
            addCriterion("ta_code <", value, "taCode");
            return (Criteria) this;
        }

        public Criteria andTaCodeLessThanOrEqualTo(String value) {
            addCriterion("ta_code <=", value, "taCode");
            return (Criteria) this;
        }

        public Criteria andTaCodeLike(String value) {
            addCriterion("ta_code like", value, "taCode");
            return (Criteria) this;
        }

        public Criteria andTaCodeNotLike(String value) {
            addCriterion("ta_code not like", value, "taCode");
            return (Criteria) this;
        }

        public Criteria andTaCodeIn(List<String> values) {
            addCriterion("ta_code in", values, "taCode");
            return (Criteria) this;
        }

        public Criteria andTaCodeNotIn(List<String> values) {
            addCriterion("ta_code not in", values, "taCode");
            return (Criteria) this;
        }

        public Criteria andTaCodeBetween(String value1, String value2) {
            addCriterion("ta_code between", value1, value2, "taCode");
            return (Criteria) this;
        }

        public Criteria andTaCodeNotBetween(String value1, String value2) {
            addCriterion("ta_code not between", value1, value2, "taCode");
            return (Criteria) this;
        }

        public Criteria andTxAcctNoIsNull() {
            addCriterion("tx_acct_no is null");
            return (Criteria) this;
        }

        public Criteria andTxAcctNoIsNotNull() {
            addCriterion("tx_acct_no is not null");
            return (Criteria) this;
        }

        public Criteria andTxAcctNoEqualTo(String value) {
            addCriterion("tx_acct_no =", value, "txAcctNo");
            return (Criteria) this;
        }

        public Criteria andTxAcctNoNotEqualTo(String value) {
            addCriterion("tx_acct_no <>", value, "txAcctNo");
            return (Criteria) this;
        }

        public Criteria andTxAcctNoGreaterThan(String value) {
            addCriterion("tx_acct_no >", value, "txAcctNo");
            return (Criteria) this;
        }

        public Criteria andTxAcctNoGreaterThanOrEqualTo(String value) {
            addCriterion("tx_acct_no >=", value, "txAcctNo");
            return (Criteria) this;
        }

        public Criteria andTxAcctNoLessThan(String value) {
            addCriterion("tx_acct_no <", value, "txAcctNo");
            return (Criteria) this;
        }

        public Criteria andTxAcctNoLessThanOrEqualTo(String value) {
            addCriterion("tx_acct_no <=", value, "txAcctNo");
            return (Criteria) this;
        }

        public Criteria andTxAcctNoLike(String value) {
            addCriterion("tx_acct_no like", value, "txAcctNo");
            return (Criteria) this;
        }

        public Criteria andTxAcctNoNotLike(String value) {
            addCriterion("tx_acct_no not like", value, "txAcctNo");
            return (Criteria) this;
        }

        public Criteria andTxAcctNoIn(List<String> values) {
            addCriterion("tx_acct_no in", values, "txAcctNo");
            return (Criteria) this;
        }

        public Criteria andTxAcctNoNotIn(List<String> values) {
            addCriterion("tx_acct_no not in", values, "txAcctNo");
            return (Criteria) this;
        }

        public Criteria andTxAcctNoBetween(String value1, String value2) {
            addCriterion("tx_acct_no between", value1, value2, "txAcctNo");
            return (Criteria) this;
        }

        public Criteria andTxAcctNoNotBetween(String value1, String value2) {
            addCriterion("tx_acct_no not between", value1, value2, "txAcctNo");
            return (Criteria) this;
        }

        public Criteria andNavIsNull() {
            addCriterion("nav is null");
            return (Criteria) this;
        }

        public Criteria andNavIsNotNull() {
            addCriterion("nav is not null");
            return (Criteria) this;
        }

        public Criteria andNavEqualTo(BigDecimal value) {
            addCriterion("nav =", value, "nav");
            return (Criteria) this;
        }

        public Criteria andNavNotEqualTo(BigDecimal value) {
            addCriterion("nav <>", value, "nav");
            return (Criteria) this;
        }

        public Criteria andNavGreaterThan(BigDecimal value) {
            addCriterion("nav >", value, "nav");
            return (Criteria) this;
        }

        public Criteria andNavGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("nav >=", value, "nav");
            return (Criteria) this;
        }

        public Criteria andNavLessThan(BigDecimal value) {
            addCriterion("nav <", value, "nav");
            return (Criteria) this;
        }

        public Criteria andNavLessThanOrEqualTo(BigDecimal value) {
            addCriterion("nav <=", value, "nav");
            return (Criteria) this;
        }

        public Criteria andNavIn(List<BigDecimal> values) {
            addCriterion("nav in", values, "nav");
            return (Criteria) this;
        }

        public Criteria andNavNotIn(List<BigDecimal> values) {
            addCriterion("nav not in", values, "nav");
            return (Criteria) this;
        }

        public Criteria andNavBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("nav between", value1, value2, "nav");
            return (Criteria) this;
        }

        public Criteria andNavNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("nav not between", value1, value2, "nav");
            return (Criteria) this;
        }

        public Criteria andProductChannelIsNull() {
            addCriterion("product_channel is null");
            return (Criteria) this;
        }

        public Criteria andProductChannelIsNotNull() {
            addCriterion("product_channel is not null");
            return (Criteria) this;
        }

        public Criteria andProductChannelEqualTo(String value) {
            addCriterion("product_channel =", value, "productChannel");
            return (Criteria) this;
        }

        public Criteria andProductChannelNotEqualTo(String value) {
            addCriterion("product_channel <>", value, "productChannel");
            return (Criteria) this;
        }

        public Criteria andProductChannelGreaterThan(String value) {
            addCriterion("product_channel >", value, "productChannel");
            return (Criteria) this;
        }

        public Criteria andProductChannelGreaterThanOrEqualTo(String value) {
            addCriterion("product_channel >=", value, "productChannel");
            return (Criteria) this;
        }

        public Criteria andProductChannelLessThan(String value) {
            addCriterion("product_channel <", value, "productChannel");
            return (Criteria) this;
        }

        public Criteria andProductChannelLessThanOrEqualTo(String value) {
            addCriterion("product_channel <=", value, "productChannel");
            return (Criteria) this;
        }

        public Criteria andProductChannelLike(String value) {
            addCriterion("product_channel like", value, "productChannel");
            return (Criteria) this;
        }

        public Criteria andProductChannelNotLike(String value) {
            addCriterion("product_channel not like", value, "productChannel");
            return (Criteria) this;
        }

        public Criteria andProductChannelIn(List<String> values) {
            addCriterion("product_channel in", values, "productChannel");
            return (Criteria) this;
        }

        public Criteria andProductChannelNotIn(List<String> values) {
            addCriterion("product_channel not in", values, "productChannel");
            return (Criteria) this;
        }

        public Criteria andProductChannelBetween(String value1, String value2) {
            addCriterion("product_channel between", value1, value2, "productChannel");
            return (Criteria) this;
        }

        public Criteria andProductChannelNotBetween(String value1, String value2) {
            addCriterion("product_channel not between", value1, value2, "productChannel");
            return (Criteria) this;
        }

        public Criteria andAppointmentDiscountIsNull() {
            addCriterion("appointment_discount is null");
            return (Criteria) this;
        }

        public Criteria andAppointmentDiscountIsNotNull() {
            addCriterion("appointment_discount is not null");
            return (Criteria) this;
        }

        public Criteria andAppointmentDiscountEqualTo(BigDecimal value) {
            addCriterion("appointment_discount =", value, "appointmentDiscount");
            return (Criteria) this;
        }

        public Criteria andAppointmentDiscountNotEqualTo(BigDecimal value) {
            addCriterion("appointment_discount <>", value, "appointmentDiscount");
            return (Criteria) this;
        }

        public Criteria andAppointmentDiscountGreaterThan(BigDecimal value) {
            addCriterion("appointment_discount >", value, "appointmentDiscount");
            return (Criteria) this;
        }

        public Criteria andAppointmentDiscountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("appointment_discount >=", value, "appointmentDiscount");
            return (Criteria) this;
        }

        public Criteria andAppointmentDiscountLessThan(BigDecimal value) {
            addCriterion("appointment_discount <", value, "appointmentDiscount");
            return (Criteria) this;
        }

        public Criteria andAppointmentDiscountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("appointment_discount <=", value, "appointmentDiscount");
            return (Criteria) this;
        }

        public Criteria andAppointmentDiscountIn(List<BigDecimal> values) {
            addCriterion("appointment_discount in", values, "appointmentDiscount");
            return (Criteria) this;
        }

        public Criteria andAppointmentDiscountNotIn(List<BigDecimal> values) {
            addCriterion("appointment_discount not in", values, "appointmentDiscount");
            return (Criteria) this;
        }

        public Criteria andAppointmentDiscountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("appointment_discount between", value1, value2, "appointmentDiscount");
            return (Criteria) this;
        }

        public Criteria andAppointmentDiscountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("appointment_discount not between", value1, value2, "appointmentDiscount");
            return (Criteria) this;
        }

        public Criteria andEsitmateFeeIsNull() {
            addCriterion("esitmate_fee is null");
            return (Criteria) this;
        }

        public Criteria andEsitmateFeeIsNotNull() {
            addCriterion("esitmate_fee is not null");
            return (Criteria) this;
        }

        public Criteria andEsitmateFeeEqualTo(BigDecimal value) {
            addCriterion("esitmate_fee =", value, "esitmateFee");
            return (Criteria) this;
        }

        public Criteria andEsitmateFeeNotEqualTo(BigDecimal value) {
            addCriterion("esitmate_fee <>", value, "esitmateFee");
            return (Criteria) this;
        }

        public Criteria andEsitmateFeeGreaterThan(BigDecimal value) {
            addCriterion("esitmate_fee >", value, "esitmateFee");
            return (Criteria) this;
        }

        public Criteria andEsitmateFeeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("esitmate_fee >=", value, "esitmateFee");
            return (Criteria) this;
        }

        public Criteria andEsitmateFeeLessThan(BigDecimal value) {
            addCriterion("esitmate_fee <", value, "esitmateFee");
            return (Criteria) this;
        }

        public Criteria andEsitmateFeeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("esitmate_fee <=", value, "esitmateFee");
            return (Criteria) this;
        }

        public Criteria andEsitmateFeeIn(List<BigDecimal> values) {
            addCriterion("esitmate_fee in", values, "esitmateFee");
            return (Criteria) this;
        }

        public Criteria andEsitmateFeeNotIn(List<BigDecimal> values) {
            addCriterion("esitmate_fee not in", values, "esitmateFee");
            return (Criteria) this;
        }

        public Criteria andEsitmateFeeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("esitmate_fee between", value1, value2, "esitmateFee");
            return (Criteria) this;
        }

        public Criteria andEsitmateFeeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("esitmate_fee not between", value1, value2, "esitmateFee");
            return (Criteria) this;
        }

        public Criteria andFirstBuyFlagIsNull() {
            addCriterion("first_buy_flag is null");
            return (Criteria) this;
        }

        public Criteria andFirstBuyFlagIsNotNull() {
            addCriterion("first_buy_flag is not null");
            return (Criteria) this;
        }

        public Criteria andFirstBuyFlagEqualTo(String value) {
            addCriterion("first_buy_flag =", value, "firstBuyFlag");
            return (Criteria) this;
        }

        public Criteria andFirstBuyFlagNotEqualTo(String value) {
            addCriterion("first_buy_flag <>", value, "firstBuyFlag");
            return (Criteria) this;
        }

        public Criteria andFirstBuyFlagGreaterThan(String value) {
            addCriterion("first_buy_flag >", value, "firstBuyFlag");
            return (Criteria) this;
        }

        public Criteria andFirstBuyFlagGreaterThanOrEqualTo(String value) {
            addCriterion("first_buy_flag >=", value, "firstBuyFlag");
            return (Criteria) this;
        }

        public Criteria andFirstBuyFlagLessThan(String value) {
            addCriterion("first_buy_flag <", value, "firstBuyFlag");
            return (Criteria) this;
        }

        public Criteria andFirstBuyFlagLessThanOrEqualTo(String value) {
            addCriterion("first_buy_flag <=", value, "firstBuyFlag");
            return (Criteria) this;
        }

        public Criteria andFirstBuyFlagLike(String value) {
            addCriterion("first_buy_flag like", value, "firstBuyFlag");
            return (Criteria) this;
        }

        public Criteria andFirstBuyFlagNotLike(String value) {
            addCriterion("first_buy_flag not like", value, "firstBuyFlag");
            return (Criteria) this;
        }

        public Criteria andFirstBuyFlagIn(List<String> values) {
            addCriterion("first_buy_flag in", values, "firstBuyFlag");
            return (Criteria) this;
        }

        public Criteria andFirstBuyFlagNotIn(List<String> values) {
            addCriterion("first_buy_flag not in", values, "firstBuyFlag");
            return (Criteria) this;
        }

        public Criteria andFirstBuyFlagBetween(String value1, String value2) {
            addCriterion("first_buy_flag between", value1, value2, "firstBuyFlag");
            return (Criteria) this;
        }

        public Criteria andFirstBuyFlagNotBetween(String value1, String value2) {
            addCriterion("first_buy_flag not between", value1, value2, "firstBuyFlag");
            return (Criteria) this;
        }

        public Criteria andEsignatureFlagIsNull() {
            addCriterion("esignature_flag is null");
            return (Criteria) this;
        }

        public Criteria andEsignatureFlagIsNotNull() {
            addCriterion("esignature_flag is not null");
            return (Criteria) this;
        }

        public Criteria andEsignatureFlagEqualTo(String value) {
            addCriterion("esignature_flag =", value, "esignatureFlag");
            return (Criteria) this;
        }

        public Criteria andEsignatureFlagNotEqualTo(String value) {
            addCriterion("esignature_flag <>", value, "esignatureFlag");
            return (Criteria) this;
        }

        public Criteria andEsignatureFlagGreaterThan(String value) {
            addCriterion("esignature_flag >", value, "esignatureFlag");
            return (Criteria) this;
        }

        public Criteria andEsignatureFlagGreaterThanOrEqualTo(String value) {
            addCriterion("esignature_flag >=", value, "esignatureFlag");
            return (Criteria) this;
        }

        public Criteria andEsignatureFlagLessThan(String value) {
            addCriterion("esignature_flag <", value, "esignatureFlag");
            return (Criteria) this;
        }

        public Criteria andEsignatureFlagLessThanOrEqualTo(String value) {
            addCriterion("esignature_flag <=", value, "esignatureFlag");
            return (Criteria) this;
        }

        public Criteria andEsignatureFlagLike(String value) {
            addCriterion("esignature_flag like", value, "esignatureFlag");
            return (Criteria) this;
        }

        public Criteria andEsignatureFlagNotLike(String value) {
            addCriterion("esignature_flag not like", value, "esignatureFlag");
            return (Criteria) this;
        }

        public Criteria andEsignatureFlagIn(List<String> values) {
            addCriterion("esignature_flag in", values, "esignatureFlag");
            return (Criteria) this;
        }

        public Criteria andEsignatureFlagNotIn(List<String> values) {
            addCriterion("esignature_flag not in", values, "esignatureFlag");
            return (Criteria) this;
        }

        public Criteria andEsignatureFlagBetween(String value1, String value2) {
            addCriterion("esignature_flag between", value1, value2, "esignatureFlag");
            return (Criteria) this;
        }

        public Criteria andEsignatureFlagNotBetween(String value1, String value2) {
            addCriterion("esignature_flag not between", value1, value2, "esignatureFlag");
            return (Criteria) this;
        }

        public Criteria andEcontractFlagIsNull() {
            addCriterion("econtract_flag is null");
            return (Criteria) this;
        }

        public Criteria andEcontractFlagIsNotNull() {
            addCriterion("econtract_flag is not null");
            return (Criteria) this;
        }

        public Criteria andEcontractFlagEqualTo(String value) {
            addCriterion("econtract_flag =", value, "econtractFlag");
            return (Criteria) this;
        }

        public Criteria andEcontractFlagNotEqualTo(String value) {
            addCriterion("econtract_flag <>", value, "econtractFlag");
            return (Criteria) this;
        }

        public Criteria andEcontractFlagGreaterThan(String value) {
            addCriterion("econtract_flag >", value, "econtractFlag");
            return (Criteria) this;
        }

        public Criteria andEcontractFlagGreaterThanOrEqualTo(String value) {
            addCriterion("econtract_flag >=", value, "econtractFlag");
            return (Criteria) this;
        }

        public Criteria andEcontractFlagLessThan(String value) {
            addCriterion("econtract_flag <", value, "econtractFlag");
            return (Criteria) this;
        }

        public Criteria andEcontractFlagLessThanOrEqualTo(String value) {
            addCriterion("econtract_flag <=", value, "econtractFlag");
            return (Criteria) this;
        }

        public Criteria andEcontractFlagLike(String value) {
            addCriterion("econtract_flag like", value, "econtractFlag");
            return (Criteria) this;
        }

        public Criteria andEcontractFlagNotLike(String value) {
            addCriterion("econtract_flag not like", value, "econtractFlag");
            return (Criteria) this;
        }

        public Criteria andEcontractFlagIn(List<String> values) {
            addCriterion("econtract_flag in", values, "econtractFlag");
            return (Criteria) this;
        }

        public Criteria andEcontractFlagNotIn(List<String> values) {
            addCriterion("econtract_flag not in", values, "econtractFlag");
            return (Criteria) this;
        }

        public Criteria andEcontractFlagBetween(String value1, String value2) {
            addCriterion("econtract_flag between", value1, value2, "econtractFlag");
            return (Criteria) this;
        }

        public Criteria andEcontractFlagNotBetween(String value1, String value2) {
            addCriterion("econtract_flag not between", value1, value2, "econtractFlag");
            return (Criteria) this;
        }

        public Criteria andUnusualTransTypeIsNull() {
            addCriterion("unusual_trans_type is null");
            return (Criteria) this;
        }

        public Criteria andUnusualTransTypeIsNotNull() {
            addCriterion("unusual_trans_type is not null");
            return (Criteria) this;
        }

        public Criteria andUnusualTransTypeEqualTo(String value) {
            addCriterion("unusual_trans_type =", value, "unusualTransType");
            return (Criteria) this;
        }

        public Criteria andUnusualTransTypeNotEqualTo(String value) {
            addCriterion("unusual_trans_type <>", value, "unusualTransType");
            return (Criteria) this;
        }

        public Criteria andUnusualTransTypeGreaterThan(String value) {
            addCriterion("unusual_trans_type >", value, "unusualTransType");
            return (Criteria) this;
        }

        public Criteria andUnusualTransTypeGreaterThanOrEqualTo(String value) {
            addCriterion("unusual_trans_type >=", value, "unusualTransType");
            return (Criteria) this;
        }

        public Criteria andUnusualTransTypeLessThan(String value) {
            addCriterion("unusual_trans_type <", value, "unusualTransType");
            return (Criteria) this;
        }

        public Criteria andUnusualTransTypeLessThanOrEqualTo(String value) {
            addCriterion("unusual_trans_type <=", value, "unusualTransType");
            return (Criteria) this;
        }

        public Criteria andUnusualTransTypeLike(String value) {
            addCriterion("unusual_trans_type like", value, "unusualTransType");
            return (Criteria) this;
        }

        public Criteria andUnusualTransTypeNotLike(String value) {
            addCriterion("unusual_trans_type not like", value, "unusualTransType");
            return (Criteria) this;
        }

        public Criteria andUnusualTransTypeIn(List<String> values) {
            addCriterion("unusual_trans_type in", values, "unusualTransType");
            return (Criteria) this;
        }

        public Criteria andUnusualTransTypeNotIn(List<String> values) {
            addCriterion("unusual_trans_type not in", values, "unusualTransType");
            return (Criteria) this;
        }

        public Criteria andUnusualTransTypeBetween(String value1, String value2) {
            addCriterion("unusual_trans_type between", value1, value2, "unusualTransType");
            return (Criteria) this;
        }

        public Criteria andUnusualTransTypeNotBetween(String value1, String value2) {
            addCriterion("unusual_trans_type not between", value1, value2, "unusualTransType");
            return (Criteria) this;
        }

        public Criteria andOpenEndTimeIsNull() {
            addCriterion("open_end_time is null");
            return (Criteria) this;
        }

        public Criteria andOpenEndTimeIsNotNull() {
            addCriterion("open_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andOpenEndTimeEqualTo(String value) {
            addCriterion("open_end_time =", value, "openEndTime");
            return (Criteria) this;
        }

        public Criteria andOpenEndTimeNotEqualTo(String value) {
            addCriterion("open_end_time <>", value, "openEndTime");
            return (Criteria) this;
        }

        public Criteria andOpenEndTimeGreaterThan(String value) {
            addCriterion("open_end_time >", value, "openEndTime");
            return (Criteria) this;
        }

        public Criteria andOpenEndTimeGreaterThanOrEqualTo(String value) {
            addCriterion("open_end_time >=", value, "openEndTime");
            return (Criteria) this;
        }

        public Criteria andOpenEndTimeLessThan(String value) {
            addCriterion("open_end_time <", value, "openEndTime");
            return (Criteria) this;
        }

        public Criteria andOpenEndTimeLessThanOrEqualTo(String value) {
            addCriterion("open_end_time <=", value, "openEndTime");
            return (Criteria) this;
        }

        public Criteria andOpenEndTimeLike(String value) {
            addCriterion("open_end_time like", value, "openEndTime");
            return (Criteria) this;
        }

        public Criteria andOpenEndTimeNotLike(String value) {
            addCriterion("open_end_time not like", value, "openEndTime");
            return (Criteria) this;
        }

        public Criteria andOpenEndTimeIn(List<String> values) {
            addCriterion("open_end_time in", values, "openEndTime");
            return (Criteria) this;
        }

        public Criteria andOpenEndTimeNotIn(List<String> values) {
            addCriterion("open_end_time not in", values, "openEndTime");
            return (Criteria) this;
        }

        public Criteria andOpenEndTimeBetween(String value1, String value2) {
            addCriterion("open_end_time between", value1, value2, "openEndTime");
            return (Criteria) this;
        }

        public Criteria andOpenEndTimeNotBetween(String value1, String value2) {
            addCriterion("open_end_time not between", value1, value2, "openEndTime");
            return (Criteria) this;
        }

        public Criteria andProductClassIsNull() {
            addCriterion("product_class is null");
            return (Criteria) this;
        }

        public Criteria andProductClassIsNotNull() {
            addCriterion("product_class is not null");
            return (Criteria) this;
        }

        public Criteria andProductClassEqualTo(String value) {
            addCriterion("product_class =", value, "productClass");
            return (Criteria) this;
        }

        public Criteria andProductClassNotEqualTo(String value) {
            addCriterion("product_class <>", value, "productClass");
            return (Criteria) this;
        }

        public Criteria andProductClassGreaterThan(String value) {
            addCriterion("product_class >", value, "productClass");
            return (Criteria) this;
        }

        public Criteria andProductClassGreaterThanOrEqualTo(String value) {
            addCriterion("product_class >=", value, "productClass");
            return (Criteria) this;
        }

        public Criteria andProductClassLessThan(String value) {
            addCriterion("product_class <", value, "productClass");
            return (Criteria) this;
        }

        public Criteria andProductClassLessThanOrEqualTo(String value) {
            addCriterion("product_class <=", value, "productClass");
            return (Criteria) this;
        }

        public Criteria andProductClassLike(String value) {
            addCriterion("product_class like", value, "productClass");
            return (Criteria) this;
        }

        public Criteria andProductClassNotLike(String value) {
            addCriterion("product_class not like", value, "productClass");
            return (Criteria) this;
        }

        public Criteria andProductClassIn(List<String> values) {
            addCriterion("product_class in", values, "productClass");
            return (Criteria) this;
        }

        public Criteria andProductClassNotIn(List<String> values) {
            addCriterion("product_class not in", values, "productClass");
            return (Criteria) this;
        }

        public Criteria andProductClassBetween(String value1, String value2) {
            addCriterion("product_class between", value1, value2, "productClass");
            return (Criteria) this;
        }

        public Criteria andProductClassNotBetween(String value1, String value2) {
            addCriterion("product_class not between", value1, value2, "productClass");
            return (Criteria) this;
        }

        public Criteria andAdvanceAmtIsNull() {
            addCriterion("advance_amt is null");
            return (Criteria) this;
        }

        public Criteria andAdvanceAmtIsNotNull() {
            addCriterion("advance_amt is not null");
            return (Criteria) this;
        }

        public Criteria andAdvanceAmtEqualTo(BigDecimal value) {
            addCriterion("advance_amt =", value, "advanceAmt");
            return (Criteria) this;
        }

        public Criteria andAdvanceAmtNotEqualTo(BigDecimal value) {
            addCriterion("advance_amt <>", value, "advanceAmt");
            return (Criteria) this;
        }

        public Criteria andAdvanceAmtGreaterThan(BigDecimal value) {
            addCriterion("advance_amt >", value, "advanceAmt");
            return (Criteria) this;
        }

        public Criteria andAdvanceAmtGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("advance_amt >=", value, "advanceAmt");
            return (Criteria) this;
        }

        public Criteria andAdvanceAmtLessThan(BigDecimal value) {
            addCriterion("advance_amt <", value, "advanceAmt");
            return (Criteria) this;
        }

        public Criteria andAdvanceAmtLessThanOrEqualTo(BigDecimal value) {
            addCriterion("advance_amt <=", value, "advanceAmt");
            return (Criteria) this;
        }

        public Criteria andAdvanceAmtIn(List<BigDecimal> values) {
            addCriterion("advance_amt in", values, "advanceAmt");
            return (Criteria) this;
        }

        public Criteria andAdvanceAmtNotIn(List<BigDecimal> values) {
            addCriterion("advance_amt not in", values, "advanceAmt");
            return (Criteria) this;
        }

        public Criteria andAdvanceAmtBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("advance_amt between", value1, value2, "advanceAmt");
            return (Criteria) this;
        }

        public Criteria andAdvanceAmtNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("advance_amt not between", value1, value2, "advanceAmt");
            return (Criteria) this;
        }

        public Criteria andLimitTypeIsNull() {
            addCriterion("limit_type is null");
            return (Criteria) this;
        }

        public Criteria andLimitTypeIsNotNull() {
            addCriterion("limit_type is not null");
            return (Criteria) this;
        }

        public Criteria andLimitTypeEqualTo(String value) {
            addCriterion("limit_type =", value, "limitType");
            return (Criteria) this;
        }

        public Criteria andLimitTypeNotEqualTo(String value) {
            addCriterion("limit_type <>", value, "limitType");
            return (Criteria) this;
        }

        public Criteria andLimitTypeGreaterThan(String value) {
            addCriterion("limit_type >", value, "limitType");
            return (Criteria) this;
        }

        public Criteria andLimitTypeGreaterThanOrEqualTo(String value) {
            addCriterion("limit_type >=", value, "limitType");
            return (Criteria) this;
        }

        public Criteria andLimitTypeLessThan(String value) {
            addCriterion("limit_type <", value, "limitType");
            return (Criteria) this;
        }

        public Criteria andLimitTypeLessThanOrEqualTo(String value) {
            addCriterion("limit_type <=", value, "limitType");
            return (Criteria) this;
        }

        public Criteria andLimitTypeLike(String value) {
            addCriterion("limit_type like", value, "limitType");
            return (Criteria) this;
        }

        public Criteria andLimitTypeNotLike(String value) {
            addCriterion("limit_type not like", value, "limitType");
            return (Criteria) this;
        }

        public Criteria andLimitTypeIn(List<String> values) {
            addCriterion("limit_type in", values, "limitType");
            return (Criteria) this;
        }

        public Criteria andLimitTypeNotIn(List<String> values) {
            addCriterion("limit_type not in", values, "limitType");
            return (Criteria) this;
        }

        public Criteria andLimitTypeBetween(String value1, String value2) {
            addCriterion("limit_type between", value1, value2, "limitType");
            return (Criteria) this;
        }

        public Criteria andLimitTypeNotBetween(String value1, String value2) {
            addCriterion("limit_type not between", value1, value2, "limitType");
            return (Criteria) this;
        }

        public Criteria andSupportAdvanceFlagIsNull() {
            addCriterion("support_advance_flag is null");
            return (Criteria) this;
        }

        public Criteria andSupportAdvanceFlagIsNotNull() {
            addCriterion("support_advance_flag is not null");
            return (Criteria) this;
        }

        public Criteria andSupportAdvanceFlagEqualTo(String value) {
            addCriterion("support_advance_flag =", value, "supportAdvanceFlag");
            return (Criteria) this;
        }

        public Criteria andSupportAdvanceFlagNotEqualTo(String value) {
            addCriterion("support_advance_flag <>", value, "supportAdvanceFlag");
            return (Criteria) this;
        }

        public Criteria andSupportAdvanceFlagGreaterThan(String value) {
            addCriterion("support_advance_flag >", value, "supportAdvanceFlag");
            return (Criteria) this;
        }

        public Criteria andSupportAdvanceFlagGreaterThanOrEqualTo(String value) {
            addCriterion("support_advance_flag >=", value, "supportAdvanceFlag");
            return (Criteria) this;
        }

        public Criteria andSupportAdvanceFlagLessThan(String value) {
            addCriterion("support_advance_flag <", value, "supportAdvanceFlag");
            return (Criteria) this;
        }

        public Criteria andSupportAdvanceFlagLessThanOrEqualTo(String value) {
            addCriterion("support_advance_flag <=", value, "supportAdvanceFlag");
            return (Criteria) this;
        }

        public Criteria andSupportAdvanceFlagLike(String value) {
            addCriterion("support_advance_flag like", value, "supportAdvanceFlag");
            return (Criteria) this;
        }

        public Criteria andSupportAdvanceFlagNotLike(String value) {
            addCriterion("support_advance_flag not like", value, "supportAdvanceFlag");
            return (Criteria) this;
        }

        public Criteria andSupportAdvanceFlagIn(List<String> values) {
            addCriterion("support_advance_flag in", values, "supportAdvanceFlag");
            return (Criteria) this;
        }

        public Criteria andSupportAdvanceFlagNotIn(List<String> values) {
            addCriterion("support_advance_flag not in", values, "supportAdvanceFlag");
            return (Criteria) this;
        }

        public Criteria andSupportAdvanceFlagBetween(String value1, String value2) {
            addCriterion("support_advance_flag between", value1, value2, "supportAdvanceFlag");
            return (Criteria) this;
        }

        public Criteria andSupportAdvanceFlagNotBetween(String value1, String value2) {
            addCriterion("support_advance_flag not between", value1, value2, "supportAdvanceFlag");
            return (Criteria) this;
        }

        public Criteria andCalmTimeIsNull() {
            addCriterion("calm_time is null");
            return (Criteria) this;
        }

        public Criteria andCalmTimeIsNotNull() {
            addCriterion("calm_time is not null");
            return (Criteria) this;
        }

        public Criteria andCalmTimeEqualTo(Integer value) {
            addCriterion("calm_time =", value, "calmTime");
            return (Criteria) this;
        }

        public Criteria andCalmTimeNotEqualTo(Integer value) {
            addCriterion("calm_time <>", value, "calmTime");
            return (Criteria) this;
        }

        public Criteria andCalmTimeGreaterThan(Integer value) {
            addCriterion("calm_time >", value, "calmTime");
            return (Criteria) this;
        }

        public Criteria andCalmTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("calm_time >=", value, "calmTime");
            return (Criteria) this;
        }

        public Criteria andCalmTimeLessThan(Integer value) {
            addCriterion("calm_time <", value, "calmTime");
            return (Criteria) this;
        }

        public Criteria andCalmTimeLessThanOrEqualTo(Integer value) {
            addCriterion("calm_time <=", value, "calmTime");
            return (Criteria) this;
        }

        public Criteria andCalmTimeIn(List<Integer> values) {
            addCriterion("calm_time in", values, "calmTime");
            return (Criteria) this;
        }

        public Criteria andCalmTimeNotIn(List<Integer> values) {
            addCriterion("calm_time not in", values, "calmTime");
            return (Criteria) this;
        }

        public Criteria andCalmTimeBetween(Integer value1, Integer value2) {
            addCriterion("calm_time between", value1, value2, "calmTime");
            return (Criteria) this;
        }

        public Criteria andCalmTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("calm_time not between", value1, value2, "calmTime");
            return (Criteria) this;
        }

        public Criteria andInterestIsNull() {
            addCriterion("interest is null");
            return (Criteria) this;
        }

        public Criteria andInterestIsNotNull() {
            addCriterion("interest is not null");
            return (Criteria) this;
        }

        public Criteria andInterestEqualTo(BigDecimal value) {
            addCriterion("interest =", value, "interest");
            return (Criteria) this;
        }

        public Criteria andInterestNotEqualTo(BigDecimal value) {
            addCriterion("interest <>", value, "interest");
            return (Criteria) this;
        }

        public Criteria andInterestGreaterThan(BigDecimal value) {
            addCriterion("interest >", value, "interest");
            return (Criteria) this;
        }

        public Criteria andInterestGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("interest >=", value, "interest");
            return (Criteria) this;
        }

        public Criteria andInterestLessThan(BigDecimal value) {
            addCriterion("interest <", value, "interest");
            return (Criteria) this;
        }

        public Criteria andInterestLessThanOrEqualTo(BigDecimal value) {
            addCriterion("interest <=", value, "interest");
            return (Criteria) this;
        }

        public Criteria andInterestIn(List<BigDecimal> values) {
            addCriterion("interest in", values, "interest");
            return (Criteria) this;
        }

        public Criteria andInterestNotIn(List<BigDecimal> values) {
            addCriterion("interest not in", values, "interest");
            return (Criteria) this;
        }

        public Criteria andInterestBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("interest between", value1, value2, "interest");
            return (Criteria) this;
        }

        public Criteria andInterestNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("interest not between", value1, value2, "interest");
            return (Criteria) this;
        }

        public Criteria andAchievementPayIsNull() {
            addCriterion("achievement_pay is null");
            return (Criteria) this;
        }

        public Criteria andAchievementPayIsNotNull() {
            addCriterion("achievement_pay is not null");
            return (Criteria) this;
        }

        public Criteria andAchievementPayEqualTo(BigDecimal value) {
            addCriterion("achievement_pay =", value, "achievementPay");
            return (Criteria) this;
        }

        public Criteria andAchievementPayNotEqualTo(BigDecimal value) {
            addCriterion("achievement_pay <>", value, "achievementPay");
            return (Criteria) this;
        }

        public Criteria andAchievementPayGreaterThan(BigDecimal value) {
            addCriterion("achievement_pay >", value, "achievementPay");
            return (Criteria) this;
        }

        public Criteria andAchievementPayGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("achievement_pay >=", value, "achievementPay");
            return (Criteria) this;
        }

        public Criteria andAchievementPayLessThan(BigDecimal value) {
            addCriterion("achievement_pay <", value, "achievementPay");
            return (Criteria) this;
        }

        public Criteria andAchievementPayLessThanOrEqualTo(BigDecimal value) {
            addCriterion("achievement_pay <=", value, "achievementPay");
            return (Criteria) this;
        }

        public Criteria andAchievementPayIn(List<BigDecimal> values) {
            addCriterion("achievement_pay in", values, "achievementPay");
            return (Criteria) this;
        }

        public Criteria andAchievementPayNotIn(List<BigDecimal> values) {
            addCriterion("achievement_pay not in", values, "achievementPay");
            return (Criteria) this;
        }

        public Criteria andAchievementPayBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("achievement_pay between", value1, value2, "achievementPay");
            return (Criteria) this;
        }

        public Criteria andAchievementPayNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("achievement_pay not between", value1, value2, "achievementPay");
            return (Criteria) this;
        }

        public Criteria andAchievementCompenIsNull() {
            addCriterion("achievement_compen is null");
            return (Criteria) this;
        }

        public Criteria andAchievementCompenIsNotNull() {
            addCriterion("achievement_compen is not null");
            return (Criteria) this;
        }

        public Criteria andAchievementCompenEqualTo(BigDecimal value) {
            addCriterion("achievement_compen =", value, "achievementCompen");
            return (Criteria) this;
        }

        public Criteria andAchievementCompenNotEqualTo(BigDecimal value) {
            addCriterion("achievement_compen <>", value, "achievementCompen");
            return (Criteria) this;
        }

        public Criteria andAchievementCompenGreaterThan(BigDecimal value) {
            addCriterion("achievement_compen >", value, "achievementCompen");
            return (Criteria) this;
        }

        public Criteria andAchievementCompenGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("achievement_compen >=", value, "achievementCompen");
            return (Criteria) this;
        }

        public Criteria andAchievementCompenLessThan(BigDecimal value) {
            addCriterion("achievement_compen <", value, "achievementCompen");
            return (Criteria) this;
        }

        public Criteria andAchievementCompenLessThanOrEqualTo(BigDecimal value) {
            addCriterion("achievement_compen <=", value, "achievementCompen");
            return (Criteria) this;
        }

        public Criteria andAchievementCompenIn(List<BigDecimal> values) {
            addCriterion("achievement_compen in", values, "achievementCompen");
            return (Criteria) this;
        }

        public Criteria andAchievementCompenNotIn(List<BigDecimal> values) {
            addCriterion("achievement_compen not in", values, "achievementCompen");
            return (Criteria) this;
        }

        public Criteria andAchievementCompenBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("achievement_compen between", value1, value2, "achievementCompen");
            return (Criteria) this;
        }

        public Criteria andAchievementCompenNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("achievement_compen not between", value1, value2, "achievementCompen");
            return (Criteria) this;
        }

        public Criteria andVolByInterestIsNull() {
            addCriterion("vol_by_interest is null");
            return (Criteria) this;
        }

        public Criteria andVolByInterestIsNotNull() {
            addCriterion("vol_by_interest is not null");
            return (Criteria) this;
        }

        public Criteria andVolByInterestEqualTo(BigDecimal value) {
            addCriterion("vol_by_interest =", value, "volByInterest");
            return (Criteria) this;
        }

        public Criteria andVolByInterestNotEqualTo(BigDecimal value) {
            addCriterion("vol_by_interest <>", value, "volByInterest");
            return (Criteria) this;
        }

        public Criteria andVolByInterestGreaterThan(BigDecimal value) {
            addCriterion("vol_by_interest >", value, "volByInterest");
            return (Criteria) this;
        }

        public Criteria andVolByInterestGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("vol_by_interest >=", value, "volByInterest");
            return (Criteria) this;
        }

        public Criteria andVolByInterestLessThan(BigDecimal value) {
            addCriterion("vol_by_interest <", value, "volByInterest");
            return (Criteria) this;
        }

        public Criteria andVolByInterestLessThanOrEqualTo(BigDecimal value) {
            addCriterion("vol_by_interest <=", value, "volByInterest");
            return (Criteria) this;
        }

        public Criteria andVolByInterestIn(List<BigDecimal> values) {
            addCriterion("vol_by_interest in", values, "volByInterest");
            return (Criteria) this;
        }

        public Criteria andVolByInterestNotIn(List<BigDecimal> values) {
            addCriterion("vol_by_interest not in", values, "volByInterest");
            return (Criteria) this;
        }

        public Criteria andVolByInterestBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("vol_by_interest between", value1, value2, "volByInterest");
            return (Criteria) this;
        }

        public Criteria andVolByInterestNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("vol_by_interest not between", value1, value2, "volByInterest");
            return (Criteria) this;
        }

        public Criteria andRecuperateFeeIsNull() {
            addCriterion("recuperate_fee is null");
            return (Criteria) this;
        }

        public Criteria andRecuperateFeeIsNotNull() {
            addCriterion("recuperate_fee is not null");
            return (Criteria) this;
        }

        public Criteria andRecuperateFeeEqualTo(BigDecimal value) {
            addCriterion("recuperate_fee =", value, "recuperateFee");
            return (Criteria) this;
        }

        public Criteria andRecuperateFeeNotEqualTo(BigDecimal value) {
            addCriterion("recuperate_fee <>", value, "recuperateFee");
            return (Criteria) this;
        }

        public Criteria andRecuperateFeeGreaterThan(BigDecimal value) {
            addCriterion("recuperate_fee >", value, "recuperateFee");
            return (Criteria) this;
        }

        public Criteria andRecuperateFeeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("recuperate_fee >=", value, "recuperateFee");
            return (Criteria) this;
        }

        public Criteria andRecuperateFeeLessThan(BigDecimal value) {
            addCriterion("recuperate_fee <", value, "recuperateFee");
            return (Criteria) this;
        }

        public Criteria andRecuperateFeeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("recuperate_fee <=", value, "recuperateFee");
            return (Criteria) this;
        }

        public Criteria andRecuperateFeeIn(List<BigDecimal> values) {
            addCriterion("recuperate_fee in", values, "recuperateFee");
            return (Criteria) this;
        }

        public Criteria andRecuperateFeeNotIn(List<BigDecimal> values) {
            addCriterion("recuperate_fee not in", values, "recuperateFee");
            return (Criteria) this;
        }

        public Criteria andRecuperateFeeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("recuperate_fee between", value1, value2, "recuperateFee");
            return (Criteria) this;
        }

        public Criteria andRecuperateFeeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("recuperate_fee not between", value1, value2, "recuperateFee");
            return (Criteria) this;
        }

        public Criteria andFeeCalModeIsNull() {
            addCriterion("fee_cal_mode is null");
            return (Criteria) this;
        }

        public Criteria andFeeCalModeIsNotNull() {
            addCriterion("fee_cal_mode is not null");
            return (Criteria) this;
        }

        public Criteria andFeeCalModeEqualTo(String value) {
            addCriterion("fee_cal_mode =", value, "feeCalMode");
            return (Criteria) this;
        }

        public Criteria andFeeCalModeNotEqualTo(String value) {
            addCriterion("fee_cal_mode <>", value, "feeCalMode");
            return (Criteria) this;
        }

        public Criteria andFeeCalModeGreaterThan(String value) {
            addCriterion("fee_cal_mode >", value, "feeCalMode");
            return (Criteria) this;
        }

        public Criteria andFeeCalModeGreaterThanOrEqualTo(String value) {
            addCriterion("fee_cal_mode >=", value, "feeCalMode");
            return (Criteria) this;
        }

        public Criteria andFeeCalModeLessThan(String value) {
            addCriterion("fee_cal_mode <", value, "feeCalMode");
            return (Criteria) this;
        }

        public Criteria andFeeCalModeLessThanOrEqualTo(String value) {
            addCriterion("fee_cal_mode <=", value, "feeCalMode");
            return (Criteria) this;
        }

        public Criteria andFeeCalModeLike(String value) {
            addCriterion("fee_cal_mode like", value, "feeCalMode");
            return (Criteria) this;
        }

        public Criteria andFeeCalModeNotLike(String value) {
            addCriterion("fee_cal_mode not like", value, "feeCalMode");
            return (Criteria) this;
        }

        public Criteria andFeeCalModeIn(List<String> values) {
            addCriterion("fee_cal_mode in", values, "feeCalMode");
            return (Criteria) this;
        }

        public Criteria andFeeCalModeNotIn(List<String> values) {
            addCriterion("fee_cal_mode not in", values, "feeCalMode");
            return (Criteria) this;
        }

        public Criteria andFeeCalModeBetween(String value1, String value2) {
            addCriterion("fee_cal_mode between", value1, value2, "feeCalMode");
            return (Criteria) this;
        }

        public Criteria andFeeCalModeNotBetween(String value1, String value2) {
            addCriterion("fee_cal_mode not between", value1, value2, "feeCalMode");
            return (Criteria) this;
        }

        public Criteria andCalmDtmIsNull() {
            addCriterion("calm_dtm is null");
            return (Criteria) this;
        }

        public Criteria andCalmDtmIsNotNull() {
            addCriterion("calm_dtm is not null");
            return (Criteria) this;
        }

        public Criteria andCalmDtmEqualTo(Date value) {
            addCriterion("calm_dtm =", value, "calmDtm");
            return (Criteria) this;
        }

        public Criteria andCalmDtmNotEqualTo(Date value) {
            addCriterion("calm_dtm <>", value, "calmDtm");
            return (Criteria) this;
        }

        public Criteria andCalmDtmGreaterThan(Date value) {
            addCriterion("calm_dtm >", value, "calmDtm");
            return (Criteria) this;
        }

        public Criteria andCalmDtmGreaterThanOrEqualTo(Date value) {
            addCriterion("calm_dtm >=", value, "calmDtm");
            return (Criteria) this;
        }

        public Criteria andCalmDtmLessThan(Date value) {
            addCriterion("calm_dtm <", value, "calmDtm");
            return (Criteria) this;
        }

        public Criteria andCalmDtmLessThanOrEqualTo(Date value) {
            addCriterion("calm_dtm <=", value, "calmDtm");
            return (Criteria) this;
        }

        public Criteria andCalmDtmIn(List<Date> values) {
            addCriterion("calm_dtm in", values, "calmDtm");
            return (Criteria) this;
        }

        public Criteria andCalmDtmNotIn(List<Date> values) {
            addCriterion("calm_dtm not in", values, "calmDtm");
            return (Criteria) this;
        }

        public Criteria andCalmDtmBetween(Date value1, Date value2) {
            addCriterion("calm_dtm between", value1, value2, "calmDtm");
            return (Criteria) this;
        }

        public Criteria andCalmDtmNotBetween(Date value1, Date value2) {
            addCriterion("calm_dtm not between", value1, value2, "calmDtm");
            return (Criteria) this;
        }

        public Criteria andCustRiskLevelIsNull() {
            addCriterion("cust_risk_level is null");
            return (Criteria) this;
        }

        public Criteria andCustRiskLevelIsNotNull() {
            addCriterion("cust_risk_level is not null");
            return (Criteria) this;
        }

        public Criteria andCustRiskLevelEqualTo(String value) {
            addCriterion("cust_risk_level =", value, "custRiskLevel");
            return (Criteria) this;
        }

        public Criteria andCustRiskLevelNotEqualTo(String value) {
            addCriterion("cust_risk_level <>", value, "custRiskLevel");
            return (Criteria) this;
        }

        public Criteria andCustRiskLevelGreaterThan(String value) {
            addCriterion("cust_risk_level >", value, "custRiskLevel");
            return (Criteria) this;
        }

        public Criteria andCustRiskLevelGreaterThanOrEqualTo(String value) {
            addCriterion("cust_risk_level >=", value, "custRiskLevel");
            return (Criteria) this;
        }

        public Criteria andCustRiskLevelLessThan(String value) {
            addCriterion("cust_risk_level <", value, "custRiskLevel");
            return (Criteria) this;
        }

        public Criteria andCustRiskLevelLessThanOrEqualTo(String value) {
            addCriterion("cust_risk_level <=", value, "custRiskLevel");
            return (Criteria) this;
        }

        public Criteria andCustRiskLevelLike(String value) {
            addCriterion("cust_risk_level like", value, "custRiskLevel");
            return (Criteria) this;
        }

        public Criteria andCustRiskLevelNotLike(String value) {
            addCriterion("cust_risk_level not like", value, "custRiskLevel");
            return (Criteria) this;
        }

        public Criteria andCustRiskLevelIn(List<String> values) {
            addCriterion("cust_risk_level in", values, "custRiskLevel");
            return (Criteria) this;
        }

        public Criteria andCustRiskLevelNotIn(List<String> values) {
            addCriterion("cust_risk_level not in", values, "custRiskLevel");
            return (Criteria) this;
        }

        public Criteria andCustRiskLevelBetween(String value1, String value2) {
            addCriterion("cust_risk_level between", value1, value2, "custRiskLevel");
            return (Criteria) this;
        }

        public Criteria andCustRiskLevelNotBetween(String value1, String value2) {
            addCriterion("cust_risk_level not between", value1, value2, "custRiskLevel");
            return (Criteria) this;
        }

        public Criteria andFundRiskLevelIsNull() {
            addCriterion("fund_risk_level is null");
            return (Criteria) this;
        }

        public Criteria andFundRiskLevelIsNotNull() {
            addCriterion("fund_risk_level is not null");
            return (Criteria) this;
        }

        public Criteria andFundRiskLevelEqualTo(String value) {
            addCriterion("fund_risk_level =", value, "fundRiskLevel");
            return (Criteria) this;
        }

        public Criteria andFundRiskLevelNotEqualTo(String value) {
            addCriterion("fund_risk_level <>", value, "fundRiskLevel");
            return (Criteria) this;
        }

        public Criteria andFundRiskLevelGreaterThan(String value) {
            addCriterion("fund_risk_level >", value, "fundRiskLevel");
            return (Criteria) this;
        }

        public Criteria andFundRiskLevelGreaterThanOrEqualTo(String value) {
            addCriterion("fund_risk_level >=", value, "fundRiskLevel");
            return (Criteria) this;
        }

        public Criteria andFundRiskLevelLessThan(String value) {
            addCriterion("fund_risk_level <", value, "fundRiskLevel");
            return (Criteria) this;
        }

        public Criteria andFundRiskLevelLessThanOrEqualTo(String value) {
            addCriterion("fund_risk_level <=", value, "fundRiskLevel");
            return (Criteria) this;
        }

        public Criteria andFundRiskLevelLike(String value) {
            addCriterion("fund_risk_level like", value, "fundRiskLevel");
            return (Criteria) this;
        }

        public Criteria andFundRiskLevelNotLike(String value) {
            addCriterion("fund_risk_level not like", value, "fundRiskLevel");
            return (Criteria) this;
        }

        public Criteria andFundRiskLevelIn(List<String> values) {
            addCriterion("fund_risk_level in", values, "fundRiskLevel");
            return (Criteria) this;
        }

        public Criteria andFundRiskLevelNotIn(List<String> values) {
            addCriterion("fund_risk_level not in", values, "fundRiskLevel");
            return (Criteria) this;
        }

        public Criteria andFundRiskLevelBetween(String value1, String value2) {
            addCriterion("fund_risk_level between", value1, value2, "fundRiskLevel");
            return (Criteria) this;
        }

        public Criteria andFundRiskLevelNotBetween(String value1, String value2) {
            addCriterion("fund_risk_level not between", value1, value2, "fundRiskLevel");
            return (Criteria) this;
        }

        public Criteria andOrderFormTypeIsNull() {
            addCriterion("order_form_type is null");
            return (Criteria) this;
        }

        public Criteria andOrderFormTypeIsNotNull() {
            addCriterion("order_form_type is not null");
            return (Criteria) this;
        }

        public Criteria andOrderFormTypeEqualTo(String value) {
            addCriterion("order_form_type =", value, "orderFormType");
            return (Criteria) this;
        }

        public Criteria andOrderFormTypeNotEqualTo(String value) {
            addCriterion("order_form_type <>", value, "orderFormType");
            return (Criteria) this;
        }

        public Criteria andOrderFormTypeGreaterThan(String value) {
            addCriterion("order_form_type >", value, "orderFormType");
            return (Criteria) this;
        }

        public Criteria andOrderFormTypeGreaterThanOrEqualTo(String value) {
            addCriterion("order_form_type >=", value, "orderFormType");
            return (Criteria) this;
        }

        public Criteria andOrderFormTypeLessThan(String value) {
            addCriterion("order_form_type <", value, "orderFormType");
            return (Criteria) this;
        }

        public Criteria andOrderFormTypeLessThanOrEqualTo(String value) {
            addCriterion("order_form_type <=", value, "orderFormType");
            return (Criteria) this;
        }

        public Criteria andOrderFormTypeLike(String value) {
            addCriterion("order_form_type like", value, "orderFormType");
            return (Criteria) this;
        }

        public Criteria andOrderFormTypeNotLike(String value) {
            addCriterion("order_form_type not like", value, "orderFormType");
            return (Criteria) this;
        }

        public Criteria andOrderFormTypeIn(List<String> values) {
            addCriterion("order_form_type in", values, "orderFormType");
            return (Criteria) this;
        }

        public Criteria andOrderFormTypeNotIn(List<String> values) {
            addCriterion("order_form_type not in", values, "orderFormType");
            return (Criteria) this;
        }

        public Criteria andOrderFormTypeBetween(String value1, String value2) {
            addCriterion("order_form_type between", value1, value2, "orderFormType");
            return (Criteria) this;
        }

        public Criteria andOrderFormTypeNotBetween(String value1, String value2) {
            addCriterion("order_form_type not between", value1, value2, "orderFormType");
            return (Criteria) this;
        }

        public Criteria andTAckVolIsNull() {
            addCriterion("t_ack_vol is null");
            return (Criteria) this;
        }

        public Criteria andTAckVolIsNotNull() {
            addCriterion("t_ack_vol is not null");
            return (Criteria) this;
        }

        public Criteria andTAckVolEqualTo(BigDecimal value) {
            addCriterion("t_ack_vol =", value, "tAckVol");
            return (Criteria) this;
        }

        public Criteria andTAckVolNotEqualTo(BigDecimal value) {
            addCriterion("t_ack_vol <>", value, "tAckVol");
            return (Criteria) this;
        }

        public Criteria andTAckVolGreaterThan(BigDecimal value) {
            addCriterion("t_ack_vol >", value, "tAckVol");
            return (Criteria) this;
        }

        public Criteria andTAckVolGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("t_ack_vol >=", value, "tAckVol");
            return (Criteria) this;
        }

        public Criteria andTAckVolLessThan(BigDecimal value) {
            addCriterion("t_ack_vol <", value, "tAckVol");
            return (Criteria) this;
        }

        public Criteria andTAckVolLessThanOrEqualTo(BigDecimal value) {
            addCriterion("t_ack_vol <=", value, "tAckVol");
            return (Criteria) this;
        }

        public Criteria andTAckVolIn(List<BigDecimal> values) {
            addCriterion("t_ack_vol in", values, "tAckVol");
            return (Criteria) this;
        }

        public Criteria andTAckVolNotIn(List<BigDecimal> values) {
            addCriterion("t_ack_vol not in", values, "tAckVol");
            return (Criteria) this;
        }

        public Criteria andTAckVolBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("t_ack_vol between", value1, value2, "tAckVol");
            return (Criteria) this;
        }

        public Criteria andTAckVolNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("t_ack_vol not between", value1, value2, "tAckVol");
            return (Criteria) this;
        }

        public Criteria andAckGramsIsNull() {
            addCriterion("ack_grams is null");
            return (Criteria) this;
        }

        public Criteria andAckGramsIsNotNull() {
            addCriterion("ack_grams is not null");
            return (Criteria) this;
        }

        public Criteria andAckGramsEqualTo(BigDecimal value) {
            addCriterion("ack_grams =", value, "ackGrams");
            return (Criteria) this;
        }

        public Criteria andAckGramsNotEqualTo(BigDecimal value) {
            addCriterion("ack_grams <>", value, "ackGrams");
            return (Criteria) this;
        }

        public Criteria andAckGramsGreaterThan(BigDecimal value) {
            addCriterion("ack_grams >", value, "ackGrams");
            return (Criteria) this;
        }

        public Criteria andAckGramsGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("ack_grams >=", value, "ackGrams");
            return (Criteria) this;
        }

        public Criteria andAckGramsLessThan(BigDecimal value) {
            addCriterion("ack_grams <", value, "ackGrams");
            return (Criteria) this;
        }

        public Criteria andAckGramsLessThanOrEqualTo(BigDecimal value) {
            addCriterion("ack_grams <=", value, "ackGrams");
            return (Criteria) this;
        }

        public Criteria andAckGramsIn(List<BigDecimal> values) {
            addCriterion("ack_grams in", values, "ackGrams");
            return (Criteria) this;
        }

        public Criteria andAckGramsNotIn(List<BigDecimal> values) {
            addCriterion("ack_grams not in", values, "ackGrams");
            return (Criteria) this;
        }

        public Criteria andAckGramsBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ack_grams between", value1, value2, "ackGrams");
            return (Criteria) this;
        }

        public Criteria andAckGramsNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ack_grams not between", value1, value2, "ackGrams");
            return (Criteria) this;
        }

        public Criteria andSubmitTaDtIsNull() {
            addCriterion("submit_ta_dt is null");
            return (Criteria) this;
        }

        public Criteria andSubmitTaDtIsNotNull() {
            addCriterion("submit_ta_dt is not null");
            return (Criteria) this;
        }

        public Criteria andSubmitTaDtEqualTo(String value) {
            addCriterion("submit_ta_dt =", value, "submitTaDt");
            return (Criteria) this;
        }

        public Criteria andSubmitTaDtNotEqualTo(String value) {
            addCriterion("submit_ta_dt <>", value, "submitTaDt");
            return (Criteria) this;
        }

        public Criteria andSubmitTaDtGreaterThan(String value) {
            addCriterion("submit_ta_dt >", value, "submitTaDt");
            return (Criteria) this;
        }

        public Criteria andSubmitTaDtGreaterThanOrEqualTo(String value) {
            addCriterion("submit_ta_dt >=", value, "submitTaDt");
            return (Criteria) this;
        }

        public Criteria andSubmitTaDtLessThan(String value) {
            addCriterion("submit_ta_dt <", value, "submitTaDt");
            return (Criteria) this;
        }

        public Criteria andSubmitTaDtLessThanOrEqualTo(String value) {
            addCriterion("submit_ta_dt <=", value, "submitTaDt");
            return (Criteria) this;
        }

        public Criteria andSubmitTaDtLike(String value) {
            addCriterion("submit_ta_dt like", value, "submitTaDt");
            return (Criteria) this;
        }

        public Criteria andSubmitTaDtNotLike(String value) {
            addCriterion("submit_ta_dt not like", value, "submitTaDt");
            return (Criteria) this;
        }

        public Criteria andSubmitTaDtIn(List<String> values) {
            addCriterion("submit_ta_dt in", values, "submitTaDt");
            return (Criteria) this;
        }

        public Criteria andSubmitTaDtNotIn(List<String> values) {
            addCriterion("submit_ta_dt not in", values, "submitTaDt");
            return (Criteria) this;
        }

        public Criteria andSubmitTaDtBetween(String value1, String value2) {
            addCriterion("submit_ta_dt between", value1, value2, "submitTaDt");
            return (Criteria) this;
        }

        public Criteria andSubmitTaDtNotBetween(String value1, String value2) {
            addCriterion("submit_ta_dt not between", value1, value2, "submitTaDt");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoTypeIsNull() {
            addCriterion("appointment_dealno_type is null");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoTypeIsNotNull() {
            addCriterion("appointment_dealno_type is not null");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoTypeEqualTo(String value) {
            addCriterion("appointment_dealno_type =", value, "appointmentDealNoType");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoTypeNotEqualTo(String value) {
            addCriterion("appointment_dealno_type <>", value, "appointmentDealNoType");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoTypeGreaterThan(String value) {
            addCriterion("appointment_dealno_type >", value, "appointmentDealNoType");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoTypeGreaterThanOrEqualTo(String value) {
            addCriterion("appointment_dealno_type >=", value, "appointmentDealNoType");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoTypeLessThan(String value) {
            addCriterion("appointment_dealno_type <", value, "appointmentDealNoType");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoTypeLessThanOrEqualTo(String value) {
            addCriterion("appointment_dealno_type <=", value, "appointmentDealNoType");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoTypeLike(String value) {
            addCriterion("appointment_dealno_type like", value, "appointmentDealNoType");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoTypeNotLike(String value) {
            addCriterion("appointment_dealno_type not like", value, "appointmentDealNoType");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoTypeIn(List<String> values) {
            addCriterion("appointment_dealno_type in", values, "appointmentDealNoType");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoTypeNotIn(List<String> values) {
            addCriterion("appointment_dealno_type not in", values, "appointmentDealNoType");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoTypeBetween(String value1, String value2) {
            addCriterion("appointment_dealno_type between", value1, value2, "appointmentDealNoType");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoTypeNotBetween(String value1, String value2) {
            addCriterion("appointment_dealno_type not between", value1, value2, "appointmentDealNoType");
            return (Criteria) this;
        }

        public Criteria andDiscountModelIsNull() {
            addCriterion("discount_model is null");
            return (Criteria) this;
        }

        public Criteria andDiscountModelIsNotNull() {
            addCriterion("discount_model is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountModelEqualTo(String value) {
            addCriterion("discount_model =", value, "discountModel");
            return (Criteria) this;
        }

        public Criteria andDiscountModelNotEqualTo(String value) {
            addCriterion("discount_model <>", value, "discountModel");
            return (Criteria) this;
        }

        public Criteria andDiscountModelGreaterThan(String value) {
            addCriterion("discount_model >", value, "discountModel");
            return (Criteria) this;
        }

        public Criteria andDiscountModelGreaterThanOrEqualTo(String value) {
            addCriterion("discount_model >=", value, "discountModel");
            return (Criteria) this;
        }

        public Criteria andDiscountModelLessThan(String value) {
            addCriterion("discount_model <", value, "discountModel");
            return (Criteria) this;
        }

        public Criteria andDiscountModelLessThanOrEqualTo(String value) {
            addCriterion("discount_model <=", value, "discountModel");
            return (Criteria) this;
        }

        public Criteria andDiscountModelLike(String value) {
            addCriterion("discount_model like", value, "discountModel");
            return (Criteria) this;
        }

        public Criteria andDiscountModelNotLike(String value) {
            addCriterion("discount_model not like", value, "discountModel");
            return (Criteria) this;
        }

        public Criteria andDiscountModelIn(List<String> values) {
            addCriterion("discount_model in", values, "discountModel");
            return (Criteria) this;
        }

        public Criteria andDiscountModelNotIn(List<String> values) {
            addCriterion("discount_model not in", values, "discountModel");
            return (Criteria) this;
        }

        public Criteria andDiscountModelBetween(String value1, String value2) {
            addCriterion("discount_model between", value1, value2, "discountModel");
            return (Criteria) this;
        }

        public Criteria andDiscountModelNotBetween(String value1, String value2) {
            addCriterion("discount_model not between", value1, value2, "discountModel");
            return (Criteria) this;
        }

        public Criteria andCpAcctNoIsNull() {
            addCriterion("cp_acct_no is null");
            return (Criteria) this;
        }

        public Criteria andCpAcctNoIsNotNull() {
            addCriterion("cp_acct_no is not null");
            return (Criteria) this;
        }

        public Criteria andCpAcctNoEqualTo(String value) {
            addCriterion("cp_acct_no =", value, "cpAcctNo");
            return (Criteria) this;
        }

        public Criteria andCpAcctNoNotEqualTo(String value) {
            addCriterion("cp_acct_no <>", value, "cpAcctNo");
            return (Criteria) this;
        }

        public Criteria andCpAcctNoGreaterThan(String value) {
            addCriterion("cp_acct_no >", value, "cpAcctNo");
            return (Criteria) this;
        }

        public Criteria andCpAcctNoGreaterThanOrEqualTo(String value) {
            addCriterion("cp_acct_no >=", value, "cpAcctNo");
            return (Criteria) this;
        }

        public Criteria andCpAcctNoLessThan(String value) {
            addCriterion("cp_acct_no <", value, "cpAcctNo");
            return (Criteria) this;
        }

        public Criteria andCpAcctNoLessThanOrEqualTo(String value) {
            addCriterion("cp_acct_no <=", value, "cpAcctNo");
            return (Criteria) this;
        }

        public Criteria andCpAcctNoLike(String value) {
            addCriterion("cp_acct_no like", value, "cpAcctNo");
            return (Criteria) this;
        }

        public Criteria andCpAcctNoNotLike(String value) {
            addCriterion("cp_acct_no not like", value, "cpAcctNo");
            return (Criteria) this;
        }

        public Criteria andCpAcctNoIn(List<String> values) {
            addCriterion("cp_acct_no in", values, "cpAcctNo");
            return (Criteria) this;
        }

        public Criteria andCpAcctNoNotIn(List<String> values) {
            addCriterion("cp_acct_no not in", values, "cpAcctNo");
            return (Criteria) this;
        }

        public Criteria andCpAcctNoBetween(String value1, String value2) {
            addCriterion("cp_acct_no between", value1, value2, "cpAcctNo");
            return (Criteria) this;
        }

        public Criteria andCpAcctNoNotBetween(String value1, String value2) {
            addCriterion("cp_acct_no not between", value1, value2, "cpAcctNo");
            return (Criteria) this;
        }

        public Criteria andProtocolNoIsNull() {
            addCriterion("protocol_no is null");
            return (Criteria) this;
        }

        public Criteria andProtocolNoIsNotNull() {
            addCriterion("protocol_no is not null");
            return (Criteria) this;
        }

        public Criteria andProtocolNoEqualTo(String value) {
            addCriterion("protocol_no =", value, "protocolNo");
            return (Criteria) this;
        }

        public Criteria andProtocolNoNotEqualTo(String value) {
            addCriterion("protocol_no <>", value, "protocolNo");
            return (Criteria) this;
        }

        public Criteria andProtocolNoGreaterThan(String value) {
            addCriterion("protocol_no >", value, "protocolNo");
            return (Criteria) this;
        }

        public Criteria andProtocolNoGreaterThanOrEqualTo(String value) {
            addCriterion("protocol_no >=", value, "protocolNo");
            return (Criteria) this;
        }

        public Criteria andProtocolNoLessThan(String value) {
            addCriterion("protocol_no <", value, "protocolNo");
            return (Criteria) this;
        }

        public Criteria andProtocolNoLessThanOrEqualTo(String value) {
            addCriterion("protocol_no <=", value, "protocolNo");
            return (Criteria) this;
        }

        public Criteria andProtocolNoLike(String value) {
            addCriterion("protocol_no like", value, "protocolNo");
            return (Criteria) this;
        }

        public Criteria andProtocolNoNotLike(String value) {
            addCriterion("protocol_no not like", value, "protocolNo");
            return (Criteria) this;
        }

        public Criteria andProtocolNoIn(List<String> values) {
            addCriterion("protocol_no in", values, "protocolNo");
            return (Criteria) this;
        }

        public Criteria andProtocolNoNotIn(List<String> values) {
            addCriterion("protocol_no not in", values, "protocolNo");
            return (Criteria) this;
        }

        public Criteria andProtocolNoBetween(String value1, String value2) {
            addCriterion("protocol_no between", value1, value2, "protocolNo");
            return (Criteria) this;
        }

        public Criteria andProtocolNoNotBetween(String value1, String value2) {
            addCriterion("protocol_no not between", value1, value2, "protocolNo");
            return (Criteria) this;
        }

        public Criteria andProtocolTypeIsNull() {
            addCriterion("protocol_type is null");
            return (Criteria) this;
        }

        public Criteria andProtocolTypeIsNotNull() {
            addCriterion("protocol_type is not null");
            return (Criteria) this;
        }

        public Criteria andProtocolTypeEqualTo(String value) {
            addCriterion("protocol_type =", value, "protocolType");
            return (Criteria) this;
        }

        public Criteria andProtocolTypeNotEqualTo(String value) {
            addCriterion("protocol_type <>", value, "protocolType");
            return (Criteria) this;
        }

        public Criteria andProtocolTypeGreaterThan(String value) {
            addCriterion("protocol_type >", value, "protocolType");
            return (Criteria) this;
        }

        public Criteria andProtocolTypeGreaterThanOrEqualTo(String value) {
            addCriterion("protocol_type >=", value, "protocolType");
            return (Criteria) this;
        }

        public Criteria andProtocolTypeLessThan(String value) {
            addCriterion("protocol_type <", value, "protocolType");
            return (Criteria) this;
        }

        public Criteria andProtocolTypeLessThanOrEqualTo(String value) {
            addCriterion("protocol_type <=", value, "protocolType");
            return (Criteria) this;
        }

        public Criteria andProtocolTypeLike(String value) {
            addCriterion("protocol_type like", value, "protocolType");
            return (Criteria) this;
        }

        public Criteria andProtocolTypeNotLike(String value) {
            addCriterion("protocol_type not like", value, "protocolType");
            return (Criteria) this;
        }

        public Criteria andProtocolTypeIn(List<String> values) {
            addCriterion("protocol_type in", values, "protocolType");
            return (Criteria) this;
        }

        public Criteria andProtocolTypeNotIn(List<String> values) {
            addCriterion("protocol_type not in", values, "protocolType");
            return (Criteria) this;
        }

        public Criteria andProtocolTypeBetween(String value1, String value2) {
            addCriterion("protocol_type between", value1, value2, "protocolType");
            return (Criteria) this;
        }

        public Criteria andProtocolTypeNotBetween(String value1, String value2) {
            addCriterion("protocol_type not between", value1, value2, "protocolType");
            return (Criteria) this;
        }

        public Criteria andBankAcctIsNull() {
            addCriterion("bank_acct is null");
            return (Criteria) this;
        }

        public Criteria andBankAcctIsNotNull() {
            addCriterion("bank_acct is not null");
            return (Criteria) this;
        }

        public Criteria andBankAcctEqualTo(String value) {
            addCriterion("bank_acct =", value, "bankAcct");
            return (Criteria) this;
        }

        public Criteria andBankAcctNotEqualTo(String value) {
            addCriterion("bank_acct <>", value, "bankAcct");
            return (Criteria) this;
        }

        public Criteria andBankAcctGreaterThan(String value) {
            addCriterion("bank_acct >", value, "bankAcct");
            return (Criteria) this;
        }

        public Criteria andBankAcctGreaterThanOrEqualTo(String value) {
            addCriterion("bank_acct >=", value, "bankAcct");
            return (Criteria) this;
        }

        public Criteria andBankAcctLessThan(String value) {
            addCriterion("bank_acct <", value, "bankAcct");
            return (Criteria) this;
        }

        public Criteria andBankAcctLessThanOrEqualTo(String value) {
            addCriterion("bank_acct <=", value, "bankAcct");
            return (Criteria) this;
        }

        public Criteria andBankAcctLike(String value) {
            addCriterion("bank_acct like", value, "bankAcct");
            return (Criteria) this;
        }

        public Criteria andBankAcctNotLike(String value) {
            addCriterion("bank_acct not like", value, "bankAcct");
            return (Criteria) this;
        }

        public Criteria andBankAcctIn(List<String> values) {
            addCriterion("bank_acct in", values, "bankAcct");
            return (Criteria) this;
        }

        public Criteria andBankAcctNotIn(List<String> values) {
            addCriterion("bank_acct not in", values, "bankAcct");
            return (Criteria) this;
        }

        public Criteria andBankAcctBetween(String value1, String value2) {
            addCriterion("bank_acct between", value1, value2, "bankAcct");
            return (Criteria) this;
        }

        public Criteria andBankAcctNotBetween(String value1, String value2) {
            addCriterion("bank_acct not between", value1, value2, "bankAcct");
            return (Criteria) this;
        }

        public Criteria andBankCodeIsNull() {
            addCriterion("bank_code is null");
            return (Criteria) this;
        }

        public Criteria andBankCodeIsNotNull() {
            addCriterion("bank_code is not null");
            return (Criteria) this;
        }

        public Criteria andBankCodeEqualTo(String value) {
            addCriterion("bank_code =", value, "bankCode");
            return (Criteria) this;
        }

        public Criteria andBankCodeNotEqualTo(String value) {
            addCriterion("bank_code <>", value, "bankCode");
            return (Criteria) this;
        }

        public Criteria andBankCodeGreaterThan(String value) {
            addCriterion("bank_code >", value, "bankCode");
            return (Criteria) this;
        }

        public Criteria andBankCodeGreaterThanOrEqualTo(String value) {
            addCriterion("bank_code >=", value, "bankCode");
            return (Criteria) this;
        }

        public Criteria andBankCodeLessThan(String value) {
            addCriterion("bank_code <", value, "bankCode");
            return (Criteria) this;
        }

        public Criteria andBankCodeLessThanOrEqualTo(String value) {
            addCriterion("bank_code <=", value, "bankCode");
            return (Criteria) this;
        }

        public Criteria andBankCodeLike(String value) {
            addCriterion("bank_code like", value, "bankCode");
            return (Criteria) this;
        }

        public Criteria andBankCodeNotLike(String value) {
            addCriterion("bank_code not like", value, "bankCode");
            return (Criteria) this;
        }

        public Criteria andBankCodeIn(List<String> values) {
            addCriterion("bank_code in", values, "bankCode");
            return (Criteria) this;
        }

        public Criteria andBankCodeNotIn(List<String> values) {
            addCriterion("bank_code not in", values, "bankCode");
            return (Criteria) this;
        }

        public Criteria andBankCodeBetween(String value1, String value2) {
            addCriterion("bank_code between", value1, value2, "bankCode");
            return (Criteria) this;
        }

        public Criteria andBankCodeNotBetween(String value1, String value2) {
            addCriterion("bank_code not between", value1, value2, "bankCode");
            return (Criteria) this;
        }

        public Criteria andDualentryStatusIsNull() {
            addCriterion("dualentry_status is null");
            return (Criteria) this;
        }

        public Criteria andDualentryStatusIsNotNull() {
            addCriterion("dualentry_status is not null");
            return (Criteria) this;
        }

        public Criteria andDualentryStatusEqualTo(String value) {
            addCriterion("dualentry_status =", value, "dualentryStatus");
            return (Criteria) this;
        }

        public Criteria andDualentryStatusNotEqualTo(String value) {
            addCriterion("dualentry_status <>", value, "dualentryStatus");
            return (Criteria) this;
        }

        public Criteria andDualentryStatusGreaterThan(String value) {
            addCriterion("dualentry_status >", value, "dualentryStatus");
            return (Criteria) this;
        }

        public Criteria andDualentryStatusGreaterThanOrEqualTo(String value) {
            addCriterion("dualentry_status >=", value, "dualentryStatus");
            return (Criteria) this;
        }

        public Criteria andDualentryStatusLessThan(String value) {
            addCriterion("dualentry_status <", value, "dualentryStatus");
            return (Criteria) this;
        }

        public Criteria andDualentryStatusLessThanOrEqualTo(String value) {
            addCriterion("dualentry_status <=", value, "dualentryStatus");
            return (Criteria) this;
        }

        public Criteria andDualentryStatusLike(String value) {
            addCriterion("dualentry_status like", value, "dualentryStatus");
            return (Criteria) this;
        }

        public Criteria andDualentryStatusNotLike(String value) {
            addCriterion("dualentry_status not like", value, "dualentryStatus");
            return (Criteria) this;
        }

        public Criteria andDualentryStatusIn(List<String> values) {
            addCriterion("dualentry_status in", values, "dualentryStatus");
            return (Criteria) this;
        }

        public Criteria andDualentryStatusNotIn(List<String> values) {
            addCriterion("dualentry_status not in", values, "dualentryStatus");
            return (Criteria) this;
        }

        public Criteria andDualentryStatusBetween(String value1, String value2) {
            addCriterion("dualentry_status between", value1, value2, "dualentryStatus");
            return (Criteria) this;
        }

        public Criteria andDualentryStatusNotBetween(String value1, String value2) {
            addCriterion("dualentry_status not between", value1, value2, "dualentryStatus");
            return (Criteria) this;
        }

        public Criteria andDualentryFinishDtmIsNull() {
            addCriterion("dualentry_finish_dtm is null");
            return (Criteria) this;
        }

        public Criteria andDualentryFinishDtmIsNotNull() {
            addCriterion("dualentry_finish_dtm is not null");
            return (Criteria) this;
        }

        public Criteria andDualentryFinishDtmEqualTo(Date value) {
            addCriterion("dualentry_finish_dtm =", value, "dualentryFinishDtm");
            return (Criteria) this;
        }

        public Criteria andDualentryFinishDtmNotEqualTo(Date value) {
            addCriterion("dualentry_finish_dtm <>", value, "dualentryFinishDtm");
            return (Criteria) this;
        }

        public Criteria andDualentryFinishDtmGreaterThan(Date value) {
            addCriterion("dualentry_finish_dtm >", value, "dualentryFinishDtm");
            return (Criteria) this;
        }

        public Criteria andDualentryFinishDtmGreaterThanOrEqualTo(Date value) {
            addCriterion("dualentry_finish_dtm >=", value, "dualentryFinishDtm");
            return (Criteria) this;
        }

        public Criteria andDualentryFinishDtmLessThan(Date value) {
            addCriterion("dualentry_finish_dtm <", value, "dualentryFinishDtm");
            return (Criteria) this;
        }

        public Criteria andDualentryFinishDtmLessThanOrEqualTo(Date value) {
            addCriterion("dualentry_finish_dtm <=", value, "dualentryFinishDtm");
            return (Criteria) this;
        }

        public Criteria andDualentryFinishDtmIn(List<Date> values) {
            addCriterion("dualentry_finish_dtm in", values, "dualentryFinishDtm");
            return (Criteria) this;
        }

        public Criteria andDualentryFinishDtmNotIn(List<Date> values) {
            addCriterion("dualentry_finish_dtm not in", values, "dualentryFinishDtm");
            return (Criteria) this;
        }

        public Criteria andDualentryFinishDtmBetween(Date value1, Date value2) {
            addCriterion("dualentry_finish_dtm between", value1, value2, "dualentryFinishDtm");
            return (Criteria) this;
        }

        public Criteria andDualentryFinishDtmNotBetween(Date value1, Date value2) {
            addCriterion("dualentry_finish_dtm not between", value1, value2, "dualentryFinishDtm");
            return (Criteria) this;
        }

        public Criteria andDualentryInterposeFlagIsNull() {
            addCriterion("dualentry_interpose_flag is null");
            return (Criteria) this;
        }

        public Criteria andDualentryInterposeFlagIsNotNull() {
            addCriterion("dualentry_interpose_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDualentryInterposeFlagEqualTo(String value) {
            addCriterion("dualentry_interpose_flag =", value, "dualentryInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andDualentryInterposeFlagNotEqualTo(String value) {
            addCriterion("dualentry_interpose_flag <>", value, "dualentryInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andDualentryInterposeFlagGreaterThan(String value) {
            addCriterion("dualentry_interpose_flag >", value, "dualentryInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andDualentryInterposeFlagGreaterThanOrEqualTo(String value) {
            addCriterion("dualentry_interpose_flag >=", value, "dualentryInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andDualentryInterposeFlagLessThan(String value) {
            addCriterion("dualentry_interpose_flag <", value, "dualentryInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andDualentryInterposeFlagLessThanOrEqualTo(String value) {
            addCriterion("dualentry_interpose_flag <=", value, "dualentryInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andDualentryInterposeFlagLike(String value) {
            addCriterion("dualentry_interpose_flag like", value, "dualentryInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andDualentryInterposeFlagNotLike(String value) {
            addCriterion("dualentry_interpose_flag not like", value, "dualentryInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andDualentryInterposeFlagIn(List<String> values) {
            addCriterion("dualentry_interpose_flag in", values, "dualentryInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andDualentryInterposeFlagNotIn(List<String> values) {
            addCriterion("dualentry_interpose_flag not in", values, "dualentryInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andDualentryInterposeFlagBetween(String value1, String value2) {
            addCriterion("dualentry_interpose_flag between", value1, value2, "dualentryInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andDualentryInterposeFlagNotBetween(String value1, String value2) {
            addCriterion("dualentry_interpose_flag not between", value1, value2, "dualentryInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andCallbackStatusIsNull() {
            addCriterion("callback_status is null");
            return (Criteria) this;
        }

        public Criteria andCallbackStatusIsNotNull() {
            addCriterion("callback_status is not null");
            return (Criteria) this;
        }

        public Criteria andCallbackStatusEqualTo(String value) {
            addCriterion("callback_status =", value, "callbackStatus");
            return (Criteria) this;
        }

        public Criteria andCallbackStatusNotEqualTo(String value) {
            addCriterion("callback_status <>", value, "callbackStatus");
            return (Criteria) this;
        }

        public Criteria andCallbackStatusGreaterThan(String value) {
            addCriterion("callback_status >", value, "callbackStatus");
            return (Criteria) this;
        }

        public Criteria andCallbackStatusGreaterThanOrEqualTo(String value) {
            addCriterion("callback_status >=", value, "callbackStatus");
            return (Criteria) this;
        }

        public Criteria andCallbackStatusLessThan(String value) {
            addCriterion("callback_status <", value, "callbackStatus");
            return (Criteria) this;
        }

        public Criteria andCallbackStatusLessThanOrEqualTo(String value) {
            addCriterion("callback_status <=", value, "callbackStatus");
            return (Criteria) this;
        }

        public Criteria andCallbackStatusLike(String value) {
            addCriterion("callback_status like", value, "callbackStatus");
            return (Criteria) this;
        }

        public Criteria andCallbackStatusNotLike(String value) {
            addCriterion("callback_status not like", value, "callbackStatus");
            return (Criteria) this;
        }

        public Criteria andCallbackStatusIn(List<String> values) {
            addCriterion("callback_status in", values, "callbackStatus");
            return (Criteria) this;
        }

        public Criteria andCallbackStatusNotIn(List<String> values) {
            addCriterion("callback_status not in", values, "callbackStatus");
            return (Criteria) this;
        }

        public Criteria andCallbackStatusBetween(String value1, String value2) {
            addCriterion("callback_status between", value1, value2, "callbackStatus");
            return (Criteria) this;
        }

        public Criteria andCallbackStatusNotBetween(String value1, String value2) {
            addCriterion("callback_status not between", value1, value2, "callbackStatus");
            return (Criteria) this;
        }

        public Criteria andCallbackFinishDtmIsNull() {
            addCriterion("callback_finish_dtm is null");
            return (Criteria) this;
        }

        public Criteria andCallbackFinishDtmIsNotNull() {
            addCriterion("callback_finish_dtm is not null");
            return (Criteria) this;
        }

        public Criteria andCallbackFinishDtmEqualTo(Date value) {
            addCriterion("callback_finish_dtm =", value, "callbackFinishDtm");
            return (Criteria) this;
        }

        public Criteria andCallbackFinishDtmNotEqualTo(Date value) {
            addCriterion("callback_finish_dtm <>", value, "callbackFinishDtm");
            return (Criteria) this;
        }

        public Criteria andCallbackFinishDtmGreaterThan(Date value) {
            addCriterion("callback_finish_dtm >", value, "callbackFinishDtm");
            return (Criteria) this;
        }

        public Criteria andCallbackFinishDtmGreaterThanOrEqualTo(Date value) {
            addCriterion("callback_finish_dtm >=", value, "callbackFinishDtm");
            return (Criteria) this;
        }

        public Criteria andCallbackFinishDtmLessThan(Date value) {
            addCriterion("callback_finish_dtm <", value, "callbackFinishDtm");
            return (Criteria) this;
        }

        public Criteria andCallbackFinishDtmLessThanOrEqualTo(Date value) {
            addCriterion("callback_finish_dtm <=", value, "callbackFinishDtm");
            return (Criteria) this;
        }

        public Criteria andCallbackFinishDtmIn(List<Date> values) {
            addCriterion("callback_finish_dtm in", values, "callbackFinishDtm");
            return (Criteria) this;
        }

        public Criteria andCallbackFinishDtmNotIn(List<Date> values) {
            addCriterion("callback_finish_dtm not in", values, "callbackFinishDtm");
            return (Criteria) this;
        }

        public Criteria andCallbackFinishDtmBetween(Date value1, Date value2) {
            addCriterion("callback_finish_dtm between", value1, value2, "callbackFinishDtm");
            return (Criteria) this;
        }

        public Criteria andCallbackFinishDtmNotBetween(Date value1, Date value2) {
            addCriterion("callback_finish_dtm not between", value1, value2, "callbackFinishDtm");
            return (Criteria) this;
        }

        public Criteria andCallbackInterposeFlagIsNull() {
            addCriterion("callback_interpose_flag is null");
            return (Criteria) this;
        }

        public Criteria andCallbackInterposeFlagIsNotNull() {
            addCriterion("callback_interpose_flag is not null");
            return (Criteria) this;
        }

        public Criteria andCallbackInterposeFlagEqualTo(String value) {
            addCriterion("callback_interpose_flag =", value, "callbackInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andCallbackInterposeFlagNotEqualTo(String value) {
            addCriterion("callback_interpose_flag <>", value, "callbackInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andCallbackInterposeFlagGreaterThan(String value) {
            addCriterion("callback_interpose_flag >", value, "callbackInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andCallbackInterposeFlagGreaterThanOrEqualTo(String value) {
            addCriterion("callback_interpose_flag >=", value, "callbackInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andCallbackInterposeFlagLessThan(String value) {
            addCriterion("callback_interpose_flag <", value, "callbackInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andCallbackInterposeFlagLessThanOrEqualTo(String value) {
            addCriterion("callback_interpose_flag <=", value, "callbackInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andCallbackInterposeFlagLike(String value) {
            addCriterion("callback_interpose_flag like", value, "callbackInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andCallbackInterposeFlagNotLike(String value) {
            addCriterion("callback_interpose_flag not like", value, "callbackInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andCallbackInterposeFlagIn(List<String> values) {
            addCriterion("callback_interpose_flag in", values, "callbackInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andCallbackInterposeFlagNotIn(List<String> values) {
            addCriterion("callback_interpose_flag not in", values, "callbackInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andCallbackInterposeFlagBetween(String value1, String value2) {
            addCriterion("callback_interpose_flag between", value1, value2, "callbackInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andCallbackInterposeFlagNotBetween(String value1, String value2) {
            addCriterion("callback_interpose_flag not between", value1, value2, "callbackInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andCalmdtmInterposeFlagIsNull() {
            addCriterion("calmdtm_interpose_flag is null");
            return (Criteria) this;
        }

        public Criteria andCalmdtmInterposeFlagIsNotNull() {
            addCriterion("calmdtm_interpose_flag is not null");
            return (Criteria) this;
        }

        public Criteria andCalmdtmInterposeFlagEqualTo(String value) {
            addCriterion("calmdtm_interpose_flag =", value, "calmdtmInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andCalmdtmInterposeFlagNotEqualTo(String value) {
            addCriterion("calmdtm_interpose_flag <>", value, "calmdtmInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andCalmdtmInterposeFlagGreaterThan(String value) {
            addCriterion("calmdtm_interpose_flag >", value, "calmdtmInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andCalmdtmInterposeFlagGreaterThanOrEqualTo(String value) {
            addCriterion("calmdtm_interpose_flag >=", value, "calmdtmInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andCalmdtmInterposeFlagLessThan(String value) {
            addCriterion("calmdtm_interpose_flag <", value, "calmdtmInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andCalmdtmInterposeFlagLessThanOrEqualTo(String value) {
            addCriterion("calmdtm_interpose_flag <=", value, "calmdtmInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andCalmdtmInterposeFlagLike(String value) {
            addCriterion("calmdtm_interpose_flag like", value, "calmdtmInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andCalmdtmInterposeFlagNotLike(String value) {
            addCriterion("calmdtm_interpose_flag not like", value, "calmdtmInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andCalmdtmInterposeFlagIn(List<String> values) {
            addCriterion("calmdtm_interpose_flag in", values, "calmdtmInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andCalmdtmInterposeFlagNotIn(List<String> values) {
            addCriterion("calmdtm_interpose_flag not in", values, "calmdtmInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andCalmdtmInterposeFlagBetween(String value1, String value2) {
            addCriterion("calmdtm_interpose_flag between", value1, value2, "calmdtmInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andCalmdtmInterposeFlagNotBetween(String value1, String value2) {
            addCriterion("calmdtm_interpose_flag not between", value1, value2, "calmdtmInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andAssetcertificateStatusIsNull() {
            addCriterion("assetcertificate_status is null");
            return (Criteria) this;
        }

        public Criteria andAssetcertificateStatusIsNotNull() {
            addCriterion("assetcertificate_status is not null");
            return (Criteria) this;
        }

        public Criteria andAssetcertificateStatusEqualTo(String value) {
            addCriterion("assetcertificate_status =", value, "assetcertificateStatus");
            return (Criteria) this;
        }

        public Criteria andAssetcertificateStatusNotEqualTo(String value) {
            addCriterion("assetcertificate_status <>", value, "assetcertificateStatus");
            return (Criteria) this;
        }

        public Criteria andAssetcertificateStatusGreaterThan(String value) {
            addCriterion("assetcertificate_status >", value, "assetcertificateStatus");
            return (Criteria) this;
        }

        public Criteria andAssetcertificateStatusGreaterThanOrEqualTo(String value) {
            addCriterion("assetcertificate_status >=", value, "assetcertificateStatus");
            return (Criteria) this;
        }

        public Criteria andAssetcertificateStatusLessThan(String value) {
            addCriterion("assetcertificate_status <", value, "assetcertificateStatus");
            return (Criteria) this;
        }

        public Criteria andAssetcertificateStatusLessThanOrEqualTo(String value) {
            addCriterion("assetcertificate_status <=", value, "assetcertificateStatus");
            return (Criteria) this;
        }

        public Criteria andAssetcertificateStatusLike(String value) {
            addCriterion("assetcertificate_status like", value, "assetcertificateStatus");
            return (Criteria) this;
        }

        public Criteria andAssetcertificateStatusNotLike(String value) {
            addCriterion("assetcertificate_status not like", value, "assetcertificateStatus");
            return (Criteria) this;
        }

        public Criteria andAssetcertificateStatusIn(List<String> values) {
            addCriterion("assetcertificate_status in", values, "assetcertificateStatus");
            return (Criteria) this;
        }

        public Criteria andAssetcertificateStatusNotIn(List<String> values) {
            addCriterion("assetcertificate_status not in", values, "assetcertificateStatus");
            return (Criteria) this;
        }

        public Criteria andAssetcertificateStatusBetween(String value1, String value2) {
            addCriterion("assetcertificate_status between", value1, value2, "assetcertificateStatus");
            return (Criteria) this;
        }

        public Criteria andAssetcertificateStatusNotBetween(String value1, String value2) {
            addCriterion("assetcertificate_status not between", value1, value2, "assetcertificateStatus");
            return (Criteria) this;
        }

        public Criteria andAssetInterposeFlagIsNull() {
            addCriterion("asset_interpose_flag is null");
            return (Criteria) this;
        }

        public Criteria andAssetInterposeFlagIsNotNull() {
            addCriterion("asset_interpose_flag is not null");
            return (Criteria) this;
        }

        public Criteria andAssetInterposeFlagEqualTo(String value) {
            addCriterion("asset_interpose_flag =", value, "assetInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andAssetInterposeFlagNotEqualTo(String value) {
            addCriterion("asset_interpose_flag <>", value, "assetInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andAssetInterposeFlagGreaterThan(String value) {
            addCriterion("asset_interpose_flag >", value, "assetInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andAssetInterposeFlagGreaterThanOrEqualTo(String value) {
            addCriterion("asset_interpose_flag >=", value, "assetInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andAssetInterposeFlagLessThan(String value) {
            addCriterion("asset_interpose_flag <", value, "assetInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andAssetInterposeFlagLessThanOrEqualTo(String value) {
            addCriterion("asset_interpose_flag <=", value, "assetInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andAssetInterposeFlagLike(String value) {
            addCriterion("asset_interpose_flag like", value, "assetInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andAssetInterposeFlagNotLike(String value) {
            addCriterion("asset_interpose_flag not like", value, "assetInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andAssetInterposeFlagIn(List<String> values) {
            addCriterion("asset_interpose_flag in", values, "assetInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andAssetInterposeFlagNotIn(List<String> values) {
            addCriterion("asset_interpose_flag not in", values, "assetInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andAssetInterposeFlagBetween(String value1, String value2) {
            addCriterion("asset_interpose_flag between", value1, value2, "assetInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andAssetInterposeFlagNotBetween(String value1, String value2) {
            addCriterion("asset_interpose_flag not between", value1, value2, "assetInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andRetrieveDtmIsNull() {
            addCriterion("retrieve_dtm is null");
            return (Criteria) this;
        }

        public Criteria andRetrieveDtmIsNotNull() {
            addCriterion("retrieve_dtm is not null");
            return (Criteria) this;
        }

        public Criteria andRetrieveDtmEqualTo(Date value) {
            addCriterion("retrieve_dtm =", value, "retrieveDtm");
            return (Criteria) this;
        }

        public Criteria andRetrieveDtmNotEqualTo(Date value) {
            addCriterion("retrieve_dtm <>", value, "retrieveDtm");
            return (Criteria) this;
        }

        public Criteria andRetrieveDtmGreaterThan(Date value) {
            addCriterion("retrieve_dtm >", value, "retrieveDtm");
            return (Criteria) this;
        }

        public Criteria andRetrieveDtmGreaterThanOrEqualTo(Date value) {
            addCriterion("retrieve_dtm >=", value, "retrieveDtm");
            return (Criteria) this;
        }

        public Criteria andRetrieveDtmLessThan(Date value) {
            addCriterion("retrieve_dtm <", value, "retrieveDtm");
            return (Criteria) this;
        }

        public Criteria andRetrieveDtmLessThanOrEqualTo(Date value) {
            addCriterion("retrieve_dtm <=", value, "retrieveDtm");
            return (Criteria) this;
        }

        public Criteria andRetrieveDtmIn(List<Date> values) {
            addCriterion("retrieve_dtm in", values, "retrieveDtm");
            return (Criteria) this;
        }

        public Criteria andRetrieveDtmNotIn(List<Date> values) {
            addCriterion("retrieve_dtm not in", values, "retrieveDtm");
            return (Criteria) this;
        }

        public Criteria andRetrieveDtmBetween(Date value1, Date value2) {
            addCriterion("retrieve_dtm between", value1, value2, "retrieveDtm");
            return (Criteria) this;
        }

        public Criteria andRetrieveDtmNotBetween(Date value1, Date value2) {
            addCriterion("retrieve_dtm not between", value1, value2, "retrieveDtm");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoIsNull() {
            addCriterion("appointment_deal_no is null");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoIsNotNull() {
            addCriterion("appointment_deal_no is not null");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoEqualTo(String value) {
            addCriterion("appointment_deal_no =", value, "appointmentDealNo");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoNotEqualTo(String value) {
            addCriterion("appointment_deal_no <>", value, "appointmentDealNo");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoGreaterThan(String value) {
            addCriterion("appointment_deal_no >", value, "appointmentDealNo");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoGreaterThanOrEqualTo(String value) {
            addCriterion("appointment_deal_no >=", value, "appointmentDealNo");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoLessThan(String value) {
            addCriterion("appointment_deal_no <", value, "appointmentDealNo");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoLessThanOrEqualTo(String value) {
            addCriterion("appointment_deal_no <=", value, "appointmentDealNo");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoLike(String value) {
            addCriterion("appointment_deal_no like", value, "appointmentDealNo");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoNotLike(String value) {
            addCriterion("appointment_deal_no not like", value, "appointmentDealNo");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoIn(List<String> values) {
            addCriterion("appointment_deal_no in", values, "appointmentDealNo");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoNotIn(List<String> values) {
            addCriterion("appointment_deal_no not in", values, "appointmentDealNo");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoBetween(String value1, String value2) {
            addCriterion("appointment_deal_no between", value1, value2, "appointmentDealNo");
            return (Criteria) this;
        }

        public Criteria andAppointmentDealNoNotBetween(String value1, String value2) {
            addCriterion("appointment_deal_no not between", value1, value2, "appointmentDealNo");
            return (Criteria) this;
        }

        public Criteria andQualificationTypeIsNull() {
            addCriterion("qualification_type is null");
            return (Criteria) this;
        }

        public Criteria andQualificationTypeIsNotNull() {
            addCriterion("qualification_type is not null");
            return (Criteria) this;
        }

        public Criteria andQualificationTypeEqualTo(String value) {
            addCriterion("qualification_type =", value, "qualificationType");
            return (Criteria) this;
        }

        public Criteria andQualificationTypeNotEqualTo(String value) {
            addCriterion("qualification_type <>", value, "qualificationType");
            return (Criteria) this;
        }

        public Criteria andQualificationTypeGreaterThan(String value) {
            addCriterion("qualification_type >", value, "qualificationType");
            return (Criteria) this;
        }

        public Criteria andQualificationTypeGreaterThanOrEqualTo(String value) {
            addCriterion("qualification_type >=", value, "qualificationType");
            return (Criteria) this;
        }

        public Criteria andQualificationTypeLessThan(String value) {
            addCriterion("qualification_type <", value, "qualificationType");
            return (Criteria) this;
        }

        public Criteria andQualificationTypeLessThanOrEqualTo(String value) {
            addCriterion("qualification_type <=", value, "qualificationType");
            return (Criteria) this;
        }

        public Criteria andQualificationTypeLike(String value) {
            addCriterion("qualification_type like", value, "qualificationType");
            return (Criteria) this;
        }

        public Criteria andQualificationTypeNotLike(String value) {
            addCriterion("qualification_type not like", value, "qualificationType");
            return (Criteria) this;
        }

        public Criteria andQualificationTypeIn(List<String> values) {
            addCriterion("qualification_type in", values, "qualificationType");
            return (Criteria) this;
        }

        public Criteria andQualificationTypeNotIn(List<String> values) {
            addCriterion("qualification_type not in", values, "qualificationType");
            return (Criteria) this;
        }

        public Criteria andQualificationTypeBetween(String value1, String value2) {
            addCriterion("qualification_type between", value1, value2, "qualificationType");
            return (Criteria) this;
        }

        public Criteria andQualificationTypeNotBetween(String value1, String value2) {
            addCriterion("qualification_type not between", value1, value2, "qualificationType");
            return (Criteria) this;
        }

        public Criteria andRefundDtIsNull() {
            addCriterion("refund_dt is null");
            return (Criteria) this;
        }

        public Criteria andRefundDtIsNotNull() {
            addCriterion("refund_dt is not null");
            return (Criteria) this;
        }

        public Criteria andRefundDtEqualTo(String value) {
            addCriterion("refund_dt =", value, "refundDt");
            return (Criteria) this;
        }

        public Criteria andRefundDtNotEqualTo(String value) {
            addCriterion("refund_dt <>", value, "refundDt");
            return (Criteria) this;
        }

        public Criteria andRefundDtGreaterThan(String value) {
            addCriterion("refund_dt >", value, "refundDt");
            return (Criteria) this;
        }

        public Criteria andRefundDtGreaterThanOrEqualTo(String value) {
            addCriterion("refund_dt >=", value, "refundDt");
            return (Criteria) this;
        }

        public Criteria andRefundDtLessThan(String value) {
            addCriterion("refund_dt <", value, "refundDt");
            return (Criteria) this;
        }

        public Criteria andRefundDtLessThanOrEqualTo(String value) {
            addCriterion("refund_dt <=", value, "refundDt");
            return (Criteria) this;
        }

        public Criteria andRefundDtLike(String value) {
            addCriterion("refund_dt like", value, "refundDt");
            return (Criteria) this;
        }

        public Criteria andRefundDtNotLike(String value) {
            addCriterion("refund_dt not like", value, "refundDt");
            return (Criteria) this;
        }

        public Criteria andRefundDtIn(List<String> values) {
            addCriterion("refund_dt in", values, "refundDt");
            return (Criteria) this;
        }

        public Criteria andRefundDtNotIn(List<String> values) {
            addCriterion("refund_dt not in", values, "refundDt");
            return (Criteria) this;
        }

        public Criteria andRefundDtBetween(String value1, String value2) {
            addCriterion("refund_dt between", value1, value2, "refundDt");
            return (Criteria) this;
        }

        public Criteria andRefundDtNotBetween(String value1, String value2) {
            addCriterion("refund_dt not between", value1, value2, "refundDt");
            return (Criteria) this;
        }

        public Criteria andIsRedeemExpireIsNull() {
            addCriterion("is_redeem_expire is null");
            return (Criteria) this;
        }

        public Criteria andIsRedeemExpireIsNotNull() {
            addCriterion("is_redeem_expire is not null");
            return (Criteria) this;
        }

        public Criteria andIsRedeemExpireEqualTo(String value) {
            addCriterion("is_redeem_expire =", value, "isRedeemExpire");
            return (Criteria) this;
        }

        public Criteria andIsRedeemExpireNotEqualTo(String value) {
            addCriterion("is_redeem_expire <>", value, "isRedeemExpire");
            return (Criteria) this;
        }

        public Criteria andIsRedeemExpireGreaterThan(String value) {
            addCriterion("is_redeem_expire >", value, "isRedeemExpire");
            return (Criteria) this;
        }

        public Criteria andIsRedeemExpireGreaterThanOrEqualTo(String value) {
            addCriterion("is_redeem_expire >=", value, "isRedeemExpire");
            return (Criteria) this;
        }

        public Criteria andIsRedeemExpireLessThan(String value) {
            addCriterion("is_redeem_expire <", value, "isRedeemExpire");
            return (Criteria) this;
        }

        public Criteria andIsRedeemExpireLessThanOrEqualTo(String value) {
            addCriterion("is_redeem_expire <=", value, "isRedeemExpire");
            return (Criteria) this;
        }

        public Criteria andIsRedeemExpireLike(String value) {
            addCriterion("is_redeem_expire like", value, "isRedeemExpire");
            return (Criteria) this;
        }

        public Criteria andIsRedeemExpireNotLike(String value) {
            addCriterion("is_redeem_expire not like", value, "isRedeemExpire");
            return (Criteria) this;
        }

        public Criteria andIsRedeemExpireIn(List<String> values) {
            addCriterion("is_redeem_expire in", values, "isRedeemExpire");
            return (Criteria) this;
        }

        public Criteria andIsRedeemExpireNotIn(List<String> values) {
            addCriterion("is_redeem_expire not in", values, "isRedeemExpire");
            return (Criteria) this;
        }

        public Criteria andIsRedeemExpireBetween(String value1, String value2) {
            addCriterion("is_redeem_expire between", value1, value2, "isRedeemExpire");
            return (Criteria) this;
        }

        public Criteria andIsRedeemExpireNotBetween(String value1, String value2) {
            addCriterion("is_redeem_expire not between", value1, value2, "isRedeemExpire");
            return (Criteria) this;
        }

        public Criteria andPreExpireDateIsNull() {
            addCriterion("pre_expire_date is null");
            return (Criteria) this;
        }

        public Criteria andPreExpireDateIsNotNull() {
            addCriterion("pre_expire_date is not null");
            return (Criteria) this;
        }

        public Criteria andPreExpireDateEqualTo(String value) {
            addCriterion("pre_expire_date =", value, "preExpireDate");
            return (Criteria) this;
        }

        public Criteria andPreExpireDateNotEqualTo(String value) {
            addCriterion("pre_expire_date <>", value, "preExpireDate");
            return (Criteria) this;
        }

        public Criteria andPreExpireDateGreaterThan(String value) {
            addCriterion("pre_expire_date >", value, "preExpireDate");
            return (Criteria) this;
        }

        public Criteria andPreExpireDateGreaterThanOrEqualTo(String value) {
            addCriterion("pre_expire_date >=", value, "preExpireDate");
            return (Criteria) this;
        }

        public Criteria andPreExpireDateLessThan(String value) {
            addCriterion("pre_expire_date <", value, "preExpireDate");
            return (Criteria) this;
        }

        public Criteria andPreExpireDateLessThanOrEqualTo(String value) {
            addCriterion("pre_expire_date <=", value, "preExpireDate");
            return (Criteria) this;
        }

        public Criteria andPreExpireDateLike(String value) {
            addCriterion("pre_expire_date like", value, "preExpireDate");
            return (Criteria) this;
        }

        public Criteria andPreExpireDateNotLike(String value) {
            addCriterion("pre_expire_date not like", value, "preExpireDate");
            return (Criteria) this;
        }

        public Criteria andPreExpireDateIn(List<String> values) {
            addCriterion("pre_expire_date in", values, "preExpireDate");
            return (Criteria) this;
        }

        public Criteria andPreExpireDateNotIn(List<String> values) {
            addCriterion("pre_expire_date not in", values, "preExpireDate");
            return (Criteria) this;
        }

        public Criteria andPreExpireDateBetween(String value1, String value2) {
            addCriterion("pre_expire_date between", value1, value2, "preExpireDate");
            return (Criteria) this;
        }

        public Criteria andPreExpireDateNotBetween(String value1, String value2) {
            addCriterion("pre_expire_date not between", value1, value2, "preExpireDate");
            return (Criteria) this;
        }

        public Criteria andSubmitTaDtInterposeFlagIsNull() {
            addCriterion("submittadt_interpose_flag is null");
            return (Criteria) this;
        }

        public Criteria andSubmitTaDtInterposeFlagIsNotNull() {
            addCriterion("submittadt_interpose_flag is not null");
            return (Criteria) this;
        }

        public Criteria andSubmitTaDtInterposeFlagEqualTo(String value) {
            addCriterion("submittadt_interpose_flag =", value, "submitTaDtInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andSubmitTaDtInterposeFlagNotEqualTo(String value) {
            addCriterion("submittadt_interpose_flag <>", value, "submitTaDtInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andSubmitTaDtInterposeFlagGreaterThan(String value) {
            addCriterion("submittadt_interpose_flag >", value, "submitTaDtInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andSubmitTaDtInterposeFlagGreaterThanOrEqualTo(String value) {
            addCriterion("submittadt_interpose_flag >=", value, "submitTaDtInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andSubmitTaDtInterposeFlagLessThan(String value) {
            addCriterion("submittadt_interpose_flag <", value, "submitTaDtInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andSubmitTaDtInterposeFlagLessThanOrEqualTo(String value) {
            addCriterion("submittadt_interpose_flag <=", value, "submitTaDtInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andSubmitTaDtInterposeFlagLike(String value) {
            addCriterion("submittadt_interpose_flag like", value, "submitTaDtInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andSubmitTaDtInterposeFlagNotLike(String value) {
            addCriterion("submittadt_interpose_flag not like", value, "submitTaDtInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andSubmitTaDtInterposeFlagIn(List<String> values) {
            addCriterion("submittadt_interpose_flag in", values, "submitTaDtInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andSubmitTaDtInterposeFlagNotIn(List<String> values) {
            addCriterion("submittadt_interpose_flag not in", values, "submitTaDtInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andSubmitTaDtInterposeFlagBetween(String value1, String value2) {
            addCriterion("submittadt_interpose_flag between", value1, value2, "submitTaDtInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andSubmitTaDtInterposeFlagNotBetween(String value1, String value2) {
            addCriterion("submittadt_interpose_flag not between", value1, value2, "submitTaDtInterposeFlag");
            return (Criteria) this;
        }

        public Criteria andRepurchaseProtocolNoIsNull() {
            addCriterion("repurchase_protocol_no is null");
            return (Criteria) this;
        }

        public Criteria andRepurchaseProtocolNoIsNotNull() {
            addCriterion("repurchase_protocol_no is not null");
            return (Criteria) this;
        }

        public Criteria andRepurchaseProtocolNoEqualTo(String value) {
            addCriterion("repurchase_protocol_no =", value, "repurchaseProtocolNo");
            return (Criteria) this;
        }

        public Criteria andRepurchaseProtocolNoNotEqualTo(String value) {
            addCriterion("repurchase_protocol_no <>", value, "repurchaseProtocolNo");
            return (Criteria) this;
        }

        public Criteria andRepurchaseProtocolNoGreaterThan(String value) {
            addCriterion("repurchase_protocol_no >", value, "repurchaseProtocolNo");
            return (Criteria) this;
        }

        public Criteria andRepurchaseProtocolNoGreaterThanOrEqualTo(String value) {
            addCriterion("repurchase_protocol_no >=", value, "repurchaseProtocolNo");
            return (Criteria) this;
        }

        public Criteria andRepurchaseProtocolNoLessThan(String value) {
            addCriterion("repurchase_protocol_no <", value, "repurchaseProtocolNo");
            return (Criteria) this;
        }

        public Criteria andRepurchaseProtocolNoLessThanOrEqualTo(String value) {
            addCriterion("repurchase_protocol_no <=", value, "repurchaseProtocolNo");
            return (Criteria) this;
        }

        public Criteria andRepurchaseProtocolNoLike(String value) {
            addCriterion("repurchase_protocol_no like", value, "repurchaseProtocolNo");
            return (Criteria) this;
        }

        public Criteria andRepurchaseProtocolNoNotLike(String value) {
            addCriterion("repurchase_protocol_no not like", value, "repurchaseProtocolNo");
            return (Criteria) this;
        }

        public Criteria andRepurchaseProtocolNoIn(List<String> values) {
            addCriterion("repurchase_protocol_no in", values, "repurchaseProtocolNo");
            return (Criteria) this;
        }

        public Criteria andRepurchaseProtocolNoNotIn(List<String> values) {
            addCriterion("repurchase_protocol_no not in", values, "repurchaseProtocolNo");
            return (Criteria) this;
        }

        public Criteria andRepurchaseProtocolNoBetween(String value1, String value2) {
            addCriterion("repurchase_protocol_no between", value1, value2, "repurchaseProtocolNo");
            return (Criteria) this;
        }

        public Criteria andRepurchaseProtocolNoNotBetween(String value1, String value2) {
            addCriterion("repurchase_protocol_no not between", value1, value2, "repurchaseProtocolNo");
            return (Criteria) this;
        }

        public Criteria andJoinDtIsNull() {
            addCriterion("join_dt is null");
            return (Criteria) this;
        }

        public Criteria andJoinDtIsNotNull() {
            addCriterion("join_dt is not null");
            return (Criteria) this;
        }

        public Criteria andJoinDtEqualTo(String value) {
            addCriterion("join_dt =", value, "joinDt");
            return (Criteria) this;
        }

        public Criteria andJoinDtNotEqualTo(String value) {
            addCriterion("join_dt <>", value, "joinDt");
            return (Criteria) this;
        }

        public Criteria andJoinDtGreaterThan(String value) {
            addCriterion("join_dt >", value, "joinDt");
            return (Criteria) this;
        }

        public Criteria andJoinDtGreaterThanOrEqualTo(String value) {
            addCriterion("join_dt >=", value, "joinDt");
            return (Criteria) this;
        }

        public Criteria andJoinDtLessThan(String value) {
            addCriterion("join_dt <", value, "joinDt");
            return (Criteria) this;
        }

        public Criteria andJoinDtLessThanOrEqualTo(String value) {
            addCriterion("join_dt <=", value, "joinDt");
            return (Criteria) this;
        }

        public Criteria andJoinDtLike(String value) {
            addCriterion("join_dt like", value, "joinDt");
            return (Criteria) this;
        }

        public Criteria andJoinDtNotLike(String value) {
            addCriterion("join_dt not like", value, "joinDt");
            return (Criteria) this;
        }

        public Criteria andJoinDtIn(List<String> values) {
            addCriterion("join_dt in", values, "joinDt");
            return (Criteria) this;
        }

        public Criteria andJoinDtNotIn(List<String> values) {
            addCriterion("join_dt not in", values, "joinDt");
            return (Criteria) this;
        }

        public Criteria andJoinDtBetween(String value1, String value2) {
            addCriterion("join_dt between", value1, value2, "joinDt");
            return (Criteria) this;
        }

        public Criteria andJoinDtNotBetween(String value1, String value2) {
            addCriterion("join_dt not between", value1, value2, "joinDt");
            return (Criteria) this;
        }

        public Criteria andForceRedeemFlagIsNull() {
            addCriterion("force_redeem_flag is null");
            return (Criteria) this;
        }

        public Criteria andForceRedeemFlagIsNotNull() {
            addCriterion("force_redeem_flag is not null");
            return (Criteria) this;
        }

        public Criteria andForceRedeemFlagEqualTo(String value) {
            addCriterion("force_redeem_flag =", value, "forceRedeemFlag");
            return (Criteria) this;
        }

        public Criteria andForceRedeemFlagNotEqualTo(String value) {
            addCriterion("force_redeem_flag <>", value, "forceRedeemFlag");
            return (Criteria) this;
        }

        public Criteria andForceRedeemFlagGreaterThan(String value) {
            addCriterion("force_redeem_flag >", value, "forceRedeemFlag");
            return (Criteria) this;
        }

        public Criteria andForceRedeemFlagGreaterThanOrEqualTo(String value) {
            addCriterion("force_redeem_flag >=", value, "forceRedeemFlag");
            return (Criteria) this;
        }

        public Criteria andForceRedeemFlagLessThan(String value) {
            addCriterion("force_redeem_flag <", value, "forceRedeemFlag");
            return (Criteria) this;
        }

        public Criteria andForceRedeemFlagLessThanOrEqualTo(String value) {
            addCriterion("force_redeem_flag <=", value, "forceRedeemFlag");
            return (Criteria) this;
        }

        public Criteria andForceRedeemFlagLike(String value) {
            addCriterion("force_redeem_flag like", value, "forceRedeemFlag");
            return (Criteria) this;
        }

        public Criteria andForceRedeemFlagNotLike(String value) {
            addCriterion("force_redeem_flag not like", value, "forceRedeemFlag");
            return (Criteria) this;
        }

        public Criteria andForceRedeemFlagIn(List<String> values) {
            addCriterion("force_redeem_flag in", values, "forceRedeemFlag");
            return (Criteria) this;
        }

        public Criteria andForceRedeemFlagNotIn(List<String> values) {
            addCriterion("force_redeem_flag not in", values, "forceRedeemFlag");
            return (Criteria) this;
        }

        public Criteria andForceRedeemFlagBetween(String value1, String value2) {
            addCriterion("force_redeem_flag between", value1, value2, "forceRedeemFlag");
            return (Criteria) this;
        }

        public Criteria andForceRedeemFlagNotBetween(String value1, String value2) {
            addCriterion("force_redeem_flag not between", value1, value2, "forceRedeemFlag");
            return (Criteria) this;
        }

        public Criteria andPreAppVolIsNull() {
            addCriterion("pre_app_vol is null");
            return (Criteria) this;
        }

        public Criteria andPreAppVolIsNotNull() {
            addCriterion("pre_app_vol is not null");
            return (Criteria) this;
        }

        public Criteria andPreAppVolEqualTo(BigDecimal value) {
            addCriterion("pre_app_vol =", value, "preAppVol");
            return (Criteria) this;
        }

        public Criteria andPreAppVolNotEqualTo(BigDecimal value) {
            addCriterion("pre_app_vol <>", value, "preAppVol");
            return (Criteria) this;
        }

        public Criteria andPreAppVolGreaterThan(BigDecimal value) {
            addCriterion("pre_app_vol >", value, "preAppVol");
            return (Criteria) this;
        }

        public Criteria andPreAppVolGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("pre_app_vol >=", value, "preAppVol");
            return (Criteria) this;
        }

        public Criteria andPreAppVolLessThan(BigDecimal value) {
            addCriterion("pre_app_vol <", value, "preAppVol");
            return (Criteria) this;
        }

        public Criteria andPreAppVolLessThanOrEqualTo(BigDecimal value) {
            addCriterion("pre_app_vol <=", value, "preAppVol");
            return (Criteria) this;
        }

        public Criteria andPreAppVolIn(List<BigDecimal> values) {
            addCriterion("pre_app_vol in", values, "preAppVol");
            return (Criteria) this;
        }

        public Criteria andPreAppVolNotIn(List<BigDecimal> values) {
            addCriterion("pre_app_vol not in", values, "preAppVol");
            return (Criteria) this;
        }

        public Criteria andPreAppVolBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("pre_app_vol between", value1, value2, "preAppVol");
            return (Criteria) this;
        }

        public Criteria andPreAppVolNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("pre_app_vol not between", value1, value2, "preAppVol");
            return (Criteria) this;
        }

        public Criteria andForceRedeemMemoIsNull() {
            addCriterion("force_redeem_memo is null");
            return (Criteria) this;
        }

        public Criteria andForceRedeemMemoIsNotNull() {
            addCriterion("force_redeem_memo is not null");
            return (Criteria) this;
        }

        public Criteria andForceRedeemMemoEqualTo(String value) {
            addCriterion("force_redeem_memo =", value, "forceRedeemMemo");
            return (Criteria) this;
        }

        public Criteria andForceRedeemMemoNotEqualTo(String value) {
            addCriterion("force_redeem_memo <>", value, "forceRedeemMemo");
            return (Criteria) this;
        }

        public Criteria andForceRedeemMemoGreaterThan(String value) {
            addCriterion("force_redeem_memo >", value, "forceRedeemMemo");
            return (Criteria) this;
        }

        public Criteria andForceRedeemMemoGreaterThanOrEqualTo(String value) {
            addCriterion("force_redeem_memo >=", value, "forceRedeemMemo");
            return (Criteria) this;
        }

        public Criteria andForceRedeemMemoLessThan(String value) {
            addCriterion("force_redeem_memo <", value, "forceRedeemMemo");
            return (Criteria) this;
        }

        public Criteria andForceRedeemMemoLessThanOrEqualTo(String value) {
            addCriterion("force_redeem_memo <=", value, "forceRedeemMemo");
            return (Criteria) this;
        }

        public Criteria andForceRedeemMemoLike(String value) {
            addCriterion("force_redeem_memo like", value, "forceRedeemMemo");
            return (Criteria) this;
        }

        public Criteria andForceRedeemMemoNotLike(String value) {
            addCriterion("force_redeem_memo not like", value, "forceRedeemMemo");
            return (Criteria) this;
        }

        public Criteria andForceRedeemMemoIn(List<String> values) {
            addCriterion("force_redeem_memo in", values, "forceRedeemMemo");
            return (Criteria) this;
        }

        public Criteria andForceRedeemMemoNotIn(List<String> values) {
            addCriterion("force_redeem_memo not in", values, "forceRedeemMemo");
            return (Criteria) this;
        }

        public Criteria andForceRedeemMemoBetween(String value1, String value2) {
            addCriterion("force_redeem_memo between", value1, value2, "forceRedeemMemo");
            return (Criteria) this;
        }

        public Criteria andForceRedeemMemoNotBetween(String value1, String value2) {
            addCriterion("force_redeem_memo not between", value1, value2, "forceRedeemMemo");
            return (Criteria) this;
        }

        public Criteria andCxgDealNoIsNull() {
            addCriterion("cxg_deal_no is null");
            return (Criteria) this;
        }

        public Criteria andCxgDealNoIsNotNull() {
            addCriterion("cxg_deal_no is not null");
            return (Criteria) this;
        }

        public Criteria andCxgDealNoEqualTo(String value) {
            addCriterion("cxg_deal_no =", value, "cxgDealNo");
            return (Criteria) this;
        }

        public Criteria andCxgDealNoNotEqualTo(String value) {
            addCriterion("cxg_deal_no <>", value, "cxgDealNo");
            return (Criteria) this;
        }

        public Criteria andCxgDealNoGreaterThan(String value) {
            addCriterion("cxg_deal_no >", value, "cxgDealNo");
            return (Criteria) this;
        }

        public Criteria andCxgDealNoGreaterThanOrEqualTo(String value) {
            addCriterion("cxg_deal_no >=", value, "cxgDealNo");
            return (Criteria) this;
        }

        public Criteria andCxgDealNoLessThan(String value) {
            addCriterion("cxg_deal_no <", value, "cxgDealNo");
            return (Criteria) this;
        }

        public Criteria andCxgDealNoLessThanOrEqualTo(String value) {
            addCriterion("cxg_deal_no <=", value, "cxgDealNo");
            return (Criteria) this;
        }

        public Criteria andCxgDealNoLike(String value) {
            addCriterion("cxg_deal_no like", value, "cxgDealNo");
            return (Criteria) this;
        }

        public Criteria andCxgDealNoNotLike(String value) {
            addCriterion("cxg_deal_no not like", value, "cxgDealNo");
            return (Criteria) this;
        }

        public Criteria andCxgDealNoIn(List<String> values) {
            addCriterion("cxg_deal_no in", values, "cxgDealNo");
            return (Criteria) this;
        }

        public Criteria andCxgDealNoNotIn(List<String> values) {
            addCriterion("cxg_deal_no not in", values, "cxgDealNo");
            return (Criteria) this;
        }

        public Criteria andCxgDealNoBetween(String value1, String value2) {
            addCriterion("cxg_deal_no between", value1, value2, "cxgDealNo");
            return (Criteria) this;
        }

        public Criteria andCxgDealNoNotBetween(String value1, String value2) {
            addCriterion("cxg_deal_no not between", value1, value2, "cxgDealNo");
            return (Criteria) this;
        }

        public Criteria andNetAppAmtIsNull() {
            addCriterion("net_app_amt is null");
            return (Criteria) this;
        }

        public Criteria andNetAppAmtIsNotNull() {
            addCriterion("net_app_amt is not null");
            return (Criteria) this;
        }

        public Criteria andNetAppAmtEqualTo(BigDecimal value) {
            addCriterion("net_app_amt =", value, "netAppAmt");
            return (Criteria) this;
        }

        public Criteria andNetAppAmtNotEqualTo(BigDecimal value) {
            addCriterion("net_app_amt <>", value, "netAppAmt");
            return (Criteria) this;
        }

        public Criteria andNetAppAmtGreaterThan(BigDecimal value) {
            addCriterion("net_app_amt >", value, "netAppAmt");
            return (Criteria) this;
        }

        public Criteria andNetAppAmtGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("net_app_amt >=", value, "netAppAmt");
            return (Criteria) this;
        }

        public Criteria andNetAppAmtLessThan(BigDecimal value) {
            addCriterion("net_app_amt <", value, "netAppAmt");
            return (Criteria) this;
        }

        public Criteria andNetAppAmtLessThanOrEqualTo(BigDecimal value) {
            addCriterion("net_app_amt <=", value, "netAppAmt");
            return (Criteria) this;
        }

        public Criteria andNetAppAmtIn(List<BigDecimal> values) {
            addCriterion("net_app_amt in", values, "netAppAmt");
            return (Criteria) this;
        }

        public Criteria andNetAppAmtNotIn(List<BigDecimal> values) {
            addCriterion("net_app_amt not in", values, "netAppAmt");
            return (Criteria) this;
        }

        public Criteria andNetAppAmtBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("net_app_amt between", value1, value2, "netAppAmt");
            return (Criteria) this;
        }

        public Criteria andNetAppAmtNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("net_app_amt not between", value1, value2, "netAppAmt");
            return (Criteria) this;
        }

        public Criteria andAppointIdIsNull() {
            addCriterion("appoint_id is null");
            return (Criteria) this;
        }

        public Criteria andAppointIdIsNotNull() {
            addCriterion("appoint_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppointIdEqualTo(String value) {
            addCriterion("appoint_id =", value, "appointId");
            return (Criteria) this;
        }

        public Criteria andAppointIdNotEqualTo(String value) {
            addCriterion("appoint_id <>", value, "appointId");
            return (Criteria) this;
        }

        public Criteria andAppointIdGreaterThan(String value) {
            addCriterion("appoint_id >", value, "appointId");
            return (Criteria) this;
        }

        public Criteria andAppointIdGreaterThanOrEqualTo(String value) {
            addCriterion("appoint_id >=", value, "appointId");
            return (Criteria) this;
        }

        public Criteria andAppointIdLessThan(String value) {
            addCriterion("appoint_id <", value, "appointId");
            return (Criteria) this;
        }

        public Criteria andAppointIdLessThanOrEqualTo(String value) {
            addCriterion("appoint_id <=", value, "appointId");
            return (Criteria) this;
        }

        public Criteria andAppointIdLike(String value) {
            addCriterion("appoint_id like", value, "appointId");
            return (Criteria) this;
        }

        public Criteria andAppointIdNotLike(String value) {
            addCriterion("appoint_id not like", value, "appointId");
            return (Criteria) this;
        }

        public Criteria andAppointIdIn(List<String> values) {
            addCriterion("appoint_id in", values, "appointId");
            return (Criteria) this;
        }

        public Criteria andAppointIdNotIn(List<String> values) {
            addCriterion("appoint_id not in", values, "appointId");
            return (Criteria) this;
        }

        public Criteria andAppointIdBetween(String value1, String value2) {
            addCriterion("appoint_id between", value1, value2, "appointId");
            return (Criteria) this;
        }

        public Criteria andAppointIdNotBetween(String value1, String value2) {
            addCriterion("appoint_id not between", value1, value2, "appointId");
            return (Criteria) this;
        }

        public Criteria andSubsAmtIsNull() {
            addCriterion("subs_amt is null");
            return (Criteria) this;
        }

        public Criteria andSubsAmtIsNotNull() {
            addCriterion("subs_amt is not null");
            return (Criteria) this;
        }

        public Criteria andSubsAmtEqualTo(BigDecimal value) {
            addCriterion("subs_amt =", value, "subsAmt");
            return (Criteria) this;
        }

        public Criteria andSubsAmtNotEqualTo(BigDecimal value) {
            addCriterion("subs_amt <>", value, "subsAmt");
            return (Criteria) this;
        }

        public Criteria andSubsAmtGreaterThan(BigDecimal value) {
            addCriterion("subs_amt >", value, "subsAmt");
            return (Criteria) this;
        }

        public Criteria andSubsAmtGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("subs_amt >=", value, "subsAmt");
            return (Criteria) this;
        }

        public Criteria andSubsAmtLessThan(BigDecimal value) {
            addCriterion("subs_amt <", value, "subsAmt");
            return (Criteria) this;
        }

        public Criteria andSubsAmtLessThanOrEqualTo(BigDecimal value) {
            addCriterion("subs_amt <=", value, "subsAmt");
            return (Criteria) this;
        }

        public Criteria andSubsAmtIn(List<BigDecimal> values) {
            addCriterion("subs_amt in", values, "subsAmt");
            return (Criteria) this;
        }

        public Criteria andSubsAmtNotIn(List<BigDecimal> values) {
            addCriterion("subs_amt not in", values, "subsAmt");
            return (Criteria) this;
        }

        public Criteria andSubsAmtBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("subs_amt between", value1, value2, "subsAmt");
            return (Criteria) this;
        }

        public Criteria andSubsAmtNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("subs_amt not between", value1, value2, "subsAmt");
            return (Criteria) this;
        }

        public Criteria andMergeSubmitFlagIsNull() {
            addCriterion("merge_submit_flag is null");
            return (Criteria) this;
        }

        public Criteria andMergeSubmitFlagIsNotNull() {
            addCriterion("merge_submit_flag is not null");
            return (Criteria) this;
        }

        public Criteria andMergeSubmitFlagEqualTo(String value) {
            addCriterion("merge_submit_flag =", value, "mergeSubmitFlag");
            return (Criteria) this;
        }

        public Criteria andMergeSubmitFlagNotEqualTo(String value) {
            addCriterion("merge_submit_flag <>", value, "mergeSubmitFlag");
            return (Criteria) this;
        }

        public Criteria andMergeSubmitFlagGreaterThan(String value) {
            addCriterion("merge_submit_flag >", value, "mergeSubmitFlag");
            return (Criteria) this;
        }

        public Criteria andMergeSubmitFlagGreaterThanOrEqualTo(String value) {
            addCriterion("merge_submit_flag >=", value, "mergeSubmitFlag");
            return (Criteria) this;
        }

        public Criteria andMergeSubmitFlagLessThan(String value) {
            addCriterion("merge_submit_flag <", value, "mergeSubmitFlag");
            return (Criteria) this;
        }

        public Criteria andMergeSubmitFlagLessThanOrEqualTo(String value) {
            addCriterion("merge_submit_flag <=", value, "mergeSubmitFlag");
            return (Criteria) this;
        }

        public Criteria andMergeSubmitFlagLike(String value) {
            addCriterion("merge_submit_flag like", value, "mergeSubmitFlag");
            return (Criteria) this;
        }

        public Criteria andMergeSubmitFlagNotLike(String value) {
            addCriterion("merge_submit_flag not like", value, "mergeSubmitFlag");
            return (Criteria) this;
        }

        public Criteria andMergeSubmitFlagIn(List<String> values) {
            addCriterion("merge_submit_flag in", values, "mergeSubmitFlag");
            return (Criteria) this;
        }

        public Criteria andMergeSubmitFlagNotIn(List<String> values) {
            addCriterion("merge_submit_flag not in", values, "mergeSubmitFlag");
            return (Criteria) this;
        }

        public Criteria andMergeSubmitFlagBetween(String value1, String value2) {
            addCriterion("merge_submit_flag between", value1, value2, "mergeSubmitFlag");
            return (Criteria) this;
        }

        public Criteria andMergeSubmitFlagNotBetween(String value1, String value2) {
            addCriterion("merge_submit_flag not between", value1, value2, "mergeSubmitFlag");
            return (Criteria) this;
        }

        public Criteria andMainDealOrderNoIsNull() {
            addCriterion("main_deal_order_no is null");
            return (Criteria) this;
        }

        public Criteria andMainDealOrderNoIsNotNull() {
            addCriterion("main_deal_order_no is not null");
            return (Criteria) this;
        }

        public Criteria andMainDealOrderNoEqualTo(String value) {
            addCriterion("main_deal_order_no =", value, "mainDealOrderNo");
            return (Criteria) this;
        }

        public Criteria andMainDealOrderNoNotEqualTo(String value) {
            addCriterion("main_deal_order_no <>", value, "mainDealOrderNo");
            return (Criteria) this;
        }

        public Criteria andMainDealOrderNoGreaterThan(String value) {
            addCriterion("main_deal_order_no >", value, "mainDealOrderNo");
            return (Criteria) this;
        }

        public Criteria andMainDealOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("main_deal_order_no >=", value, "mainDealOrderNo");
            return (Criteria) this;
        }

        public Criteria andMainDealOrderNoLessThan(String value) {
            addCriterion("main_deal_order_no <", value, "mainDealOrderNo");
            return (Criteria) this;
        }

        public Criteria andMainDealOrderNoLessThanOrEqualTo(String value) {
            addCriterion("main_deal_order_no <=", value, "mainDealOrderNo");
            return (Criteria) this;
        }

        public Criteria andMainDealOrderNoLike(String value) {
            addCriterion("main_deal_order_no like", value, "mainDealOrderNo");
            return (Criteria) this;
        }

        public Criteria andMainDealOrderNoNotLike(String value) {
            addCriterion("main_deal_order_no not like", value, "mainDealOrderNo");
            return (Criteria) this;
        }

        public Criteria andMainDealOrderNoIn(List<String> values) {
            addCriterion("main_deal_order_no in", values, "mainDealOrderNo");
            return (Criteria) this;
        }

        public Criteria andMainDealOrderNoNotIn(List<String> values) {
            addCriterion("main_deal_order_no not in", values, "mainDealOrderNo");
            return (Criteria) this;
        }

        public Criteria andMainDealOrderNoBetween(String value1, String value2) {
            addCriterion("main_deal_order_no between", value1, value2, "mainDealOrderNo");
            return (Criteria) this;
        }

        public Criteria andMainDealOrderNoNotBetween(String value1, String value2) {
            addCriterion("main_deal_order_no not between", value1, value2, "mainDealOrderNo");
            return (Criteria) this;
        }

        public Criteria andContractVersionIsNull() {
            addCriterion("contract_version is null");
            return (Criteria) this;
        }

        public Criteria andContractVersionIsNotNull() {
            addCriterion("contract_version is not null");
            return (Criteria) this;
        }

        public Criteria andContractVersionEqualTo(String value) {
            addCriterion("contract_version =", value, "contractVersion");
            return (Criteria) this;
        }

        public Criteria andContractVersionNotEqualTo(String value) {
            addCriterion("contract_version <>", value, "contractVersion");
            return (Criteria) this;
        }

        public Criteria andContractVersionGreaterThan(String value) {
            addCriterion("contract_version >", value, "contractVersion");
            return (Criteria) this;
        }

        public Criteria andContractVersionGreaterThanOrEqualTo(String value) {
            addCriterion("contract_version >=", value, "contractVersion");
            return (Criteria) this;
        }

        public Criteria andContractVersionLessThan(String value) {
            addCriterion("contract_version <", value, "contractVersion");
            return (Criteria) this;
        }

        public Criteria andContractVersionLessThanOrEqualTo(String value) {
            addCriterion("contract_version <=", value, "contractVersion");
            return (Criteria) this;
        }

        public Criteria andContractVersionLike(String value) {
            addCriterion("contract_version like", value, "contractVersion");
            return (Criteria) this;
        }

        public Criteria andContractVersionNotLike(String value) {
            addCriterion("contract_version not like", value, "contractVersion");
            return (Criteria) this;
        }

        public Criteria andContractVersionIn(List<String> values) {
            addCriterion("contract_version in", values, "contractVersion");
            return (Criteria) this;
        }

        public Criteria andContractVersionNotIn(List<String> values) {
            addCriterion("contract_version not in", values, "contractVersion");
            return (Criteria) this;
        }

        public Criteria andContractVersionBetween(String value1, String value2) {
            addCriterion("contract_version between", value1, value2, "contractVersion");
            return (Criteria) this;
        }

        public Criteria andContractVersionNotBetween(String value1, String value2) {
            addCriterion("contract_version not between", value1, value2, "contractVersion");
            return (Criteria) this;
        }

        public Criteria andHighFundInvPlanFlagIsNull() {
            addCriterion("high_fund_inv_plan_flag is null");
            return (Criteria) this;
        }

        public Criteria andHighFundInvPlanFlagIsNotNull() {
            addCriterion("high_fund_inv_plan_flag is not null");
            return (Criteria) this;
        }

        public Criteria andHighFundInvPlanFlagEqualTo(String value) {
            addCriterion("high_fund_inv_plan_flag =", value, "highFundInvPlanFlag");
            return (Criteria) this;
        }

        public Criteria andHighFundInvPlanFlagNotEqualTo(String value) {
            addCriterion("high_fund_inv_plan_flag <>", value, "highFundInvPlanFlag");
            return (Criteria) this;
        }

        public Criteria andHighFundInvPlanFlagGreaterThan(String value) {
            addCriterion("high_fund_inv_plan_flag >", value, "highFundInvPlanFlag");
            return (Criteria) this;
        }

        public Criteria andHighFundInvPlanFlagGreaterThanOrEqualTo(String value) {
            addCriterion("high_fund_inv_plan_flag >=", value, "highFundInvPlanFlag");
            return (Criteria) this;
        }

        public Criteria andHighFundInvPlanFlagLessThan(String value) {
            addCriterion("high_fund_inv_plan_flag <", value, "highFundInvPlanFlag");
            return (Criteria) this;
        }

        public Criteria andHighFundInvPlanFlagLessThanOrEqualTo(String value) {
            addCriterion("high_fund_inv_plan_flag <=", value, "highFundInvPlanFlag");
            return (Criteria) this;
        }

        public Criteria andHighFundInvPlanFlagLike(String value) {
            addCriterion("high_fund_inv_plan_flag like", value, "highFundInvPlanFlag");
            return (Criteria) this;
        }

        public Criteria andHighFundInvPlanFlagNotLike(String value) {
            addCriterion("high_fund_inv_plan_flag not like", value, "highFundInvPlanFlag");
            return (Criteria) this;
        }

        public Criteria andHighFundInvPlanFlagIn(List<String> values) {
            addCriterion("high_fund_inv_plan_flag in", values, "highFundInvPlanFlag");
            return (Criteria) this;
        }

        public Criteria andHighFundInvPlanFlagNotIn(List<String> values) {
            addCriterion("high_fund_inv_plan_flag not in", values, "highFundInvPlanFlag");
            return (Criteria) this;
        }

        public Criteria andHighFundInvPlanFlagBetween(String value1, String value2) {
            addCriterion("high_fund_inv_plan_flag between", value1, value2, "highFundInvPlanFlag");
            return (Criteria) this;
        }

        public Criteria andHighFundInvPlanFlagNotBetween(String value1, String value2) {
            addCriterion("high_fund_inv_plan_flag not between", value1, value2, "highFundInvPlanFlag");
            return (Criteria) this;
        }

        public Criteria andAgencyFeeIsNull() {
            addCriterion("agency_fee is null");
            return (Criteria) this;
        }

        public Criteria andAgencyFeeIsNotNull() {
            addCriterion("agency_fee is not null");
            return (Criteria) this;
        }

        public Criteria andAgencyFeeEqualTo(BigDecimal value) {
            addCriterion("agency_fee =", value, "agencyFee");
            return (Criteria) this;
        }

        public Criteria andAgencyFeeNotEqualTo(BigDecimal value) {
            addCriterion("agency_fee <>", value, "agencyFee");
            return (Criteria) this;
        }

        public Criteria andAgencyFeeGreaterThan(BigDecimal value) {
            addCriterion("agency_fee >", value, "agencyFee");
            return (Criteria) this;
        }

        public Criteria andAgencyFeeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("agency_fee >=", value, "agencyFee");
            return (Criteria) this;
        }

        public Criteria andAgencyFeeLessThan(BigDecimal value) {
            addCriterion("agency_fee <", value, "agencyFee");
            return (Criteria) this;
        }

        public Criteria andAgencyFeeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("agency_fee <=", value, "agencyFee");
            return (Criteria) this;
        }

        public Criteria andAgencyFeeIn(List<BigDecimal> values) {
            addCriterion("agency_fee in", values, "agencyFee");
            return (Criteria) this;
        }

        public Criteria andAgencyFeeNotIn(List<BigDecimal> values) {
            addCriterion("agency_fee not in", values, "agencyFee");
            return (Criteria) this;
        }

        public Criteria andAgencyFeeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("agency_fee between", value1, value2, "agencyFee");
            return (Criteria) this;
        }

        public Criteria andAgencyFeeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("agency_fee not between", value1, value2, "agencyFee");
            return (Criteria) this;
        }

        public Criteria andOtherFee1IsNull() {
            addCriterion("other_fee1 is null");
            return (Criteria) this;
        }

        public Criteria andOtherFee1IsNotNull() {
            addCriterion("other_fee1 is not null");
            return (Criteria) this;
        }

        public Criteria andOtherFee1EqualTo(BigDecimal value) {
            addCriterion("other_fee1 =", value, "otherFee1");
            return (Criteria) this;
        }

        public Criteria andOtherFee1NotEqualTo(BigDecimal value) {
            addCriterion("other_fee1 <>", value, "otherFee1");
            return (Criteria) this;
        }

        public Criteria andOtherFee1GreaterThan(BigDecimal value) {
            addCriterion("other_fee1 >", value, "otherFee1");
            return (Criteria) this;
        }

        public Criteria andOtherFee1GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("other_fee1 >=", value, "otherFee1");
            return (Criteria) this;
        }

        public Criteria andOtherFee1LessThan(BigDecimal value) {
            addCriterion("other_fee1 <", value, "otherFee1");
            return (Criteria) this;
        }

        public Criteria andOtherFee1LessThanOrEqualTo(BigDecimal value) {
            addCriterion("other_fee1 <=", value, "otherFee1");
            return (Criteria) this;
        }

        public Criteria andOtherFee1In(List<BigDecimal> values) {
            addCriterion("other_fee1 in", values, "otherFee1");
            return (Criteria) this;
        }

        public Criteria andOtherFee1NotIn(List<BigDecimal> values) {
            addCriterion("other_fee1 not in", values, "otherFee1");
            return (Criteria) this;
        }

        public Criteria andOtherFee1Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("other_fee1 between", value1, value2, "otherFee1");
            return (Criteria) this;
        }

        public Criteria andOtherFee1NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("other_fee1 not between", value1, value2, "otherFee1");
            return (Criteria) this;
        }

        public Criteria andTransferFeeIsNull() {
            addCriterion("transfer_fee is null");
            return (Criteria) this;
        }

        public Criteria andTransferFeeIsNotNull() {
            addCriterion("transfer_fee is not null");
            return (Criteria) this;
        }

        public Criteria andTransferFeeEqualTo(BigDecimal value) {
            addCriterion("transfer_fee =", value, "transferFee");
            return (Criteria) this;
        }

        public Criteria andTransferFeeNotEqualTo(BigDecimal value) {
            addCriterion("transfer_fee <>", value, "transferFee");
            return (Criteria) this;
        }

        public Criteria andTransferFeeGreaterThan(BigDecimal value) {
            addCriterion("transfer_fee >", value, "transferFee");
            return (Criteria) this;
        }

        public Criteria andTransferFeeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("transfer_fee >=", value, "transferFee");
            return (Criteria) this;
        }

        public Criteria andTransferFeeLessThan(BigDecimal value) {
            addCriterion("transfer_fee <", value, "transferFee");
            return (Criteria) this;
        }

        public Criteria andTransferFeeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("transfer_fee <=", value, "transferFee");
            return (Criteria) this;
        }

        public Criteria andTransferFeeIn(List<BigDecimal> values) {
            addCriterion("transfer_fee in", values, "transferFee");
            return (Criteria) this;
        }

        public Criteria andTransferFeeNotIn(List<BigDecimal> values) {
            addCriterion("transfer_fee not in", values, "transferFee");
            return (Criteria) this;
        }

        public Criteria andTransferFeeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("transfer_fee between", value1, value2, "transferFee");
            return (Criteria) this;
        }

        public Criteria andTransferFeeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("transfer_fee not between", value1, value2, "transferFee");
            return (Criteria) this;
        }

        public Criteria andAchievPayIsNull() {
            addCriterion("achiev_pay is null");
            return (Criteria) this;
        }

        public Criteria andAchievPayIsNotNull() {
            addCriterion("achiev_pay is not null");
            return (Criteria) this;
        }

        public Criteria andAchievPayEqualTo(BigDecimal value) {
            addCriterion("achiev_pay =", value, "achievPay");
            return (Criteria) this;
        }

        public Criteria andAchievPayNotEqualTo(BigDecimal value) {
            addCriterion("achiev_pay <>", value, "achievPay");
            return (Criteria) this;
        }

        public Criteria andAchievPayGreaterThan(BigDecimal value) {
            addCriterion("achiev_pay >", value, "achievPay");
            return (Criteria) this;
        }

        public Criteria andAchievPayGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("achiev_pay >=", value, "achievPay");
            return (Criteria) this;
        }

        public Criteria andAchievPayLessThan(BigDecimal value) {
            addCriterion("achiev_pay <", value, "achievPay");
            return (Criteria) this;
        }

        public Criteria andAchievPayLessThanOrEqualTo(BigDecimal value) {
            addCriterion("achiev_pay <=", value, "achievPay");
            return (Criteria) this;
        }

        public Criteria andAchievPayIn(List<BigDecimal> values) {
            addCriterion("achiev_pay in", values, "achievPay");
            return (Criteria) this;
        }

        public Criteria andAchievPayNotIn(List<BigDecimal> values) {
            addCriterion("achiev_pay not in", values, "achievPay");
            return (Criteria) this;
        }

        public Criteria andAchievPayBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("achiev_pay between", value1, value2, "achievPay");
            return (Criteria) this;
        }

        public Criteria andAchievPayNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("achiev_pay not between", value1, value2, "achievPay");
            return (Criteria) this;
        }

        public Criteria andTotalTransFeeIsNull() {
            addCriterion("total_trans_fee is null");
            return (Criteria) this;
        }

        public Criteria andTotalTransFeeIsNotNull() {
            addCriterion("total_trans_fee is not null");
            return (Criteria) this;
        }

        public Criteria andTotalTransFeeEqualTo(BigDecimal value) {
            addCriterion("total_trans_fee =", value, "totalTransFee");
            return (Criteria) this;
        }

        public Criteria andTotalTransFeeNotEqualTo(BigDecimal value) {
            addCriterion("total_trans_fee <>", value, "totalTransFee");
            return (Criteria) this;
        }

        public Criteria andTotalTransFeeGreaterThan(BigDecimal value) {
            addCriterion("total_trans_fee >", value, "totalTransFee");
            return (Criteria) this;
        }

        public Criteria andTotalTransFeeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_trans_fee >=", value, "totalTransFee");
            return (Criteria) this;
        }

        public Criteria andTotalTransFeeLessThan(BigDecimal value) {
            addCriterion("total_trans_fee <", value, "totalTransFee");
            return (Criteria) this;
        }

        public Criteria andTotalTransFeeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_trans_fee <=", value, "totalTransFee");
            return (Criteria) this;
        }

        public Criteria andTotalTransFeeIn(List<BigDecimal> values) {
            addCriterion("total_trans_fee in", values, "totalTransFee");
            return (Criteria) this;
        }

        public Criteria andTotalTransFeeNotIn(List<BigDecimal> values) {
            addCriterion("total_trans_fee not in", values, "totalTransFee");
            return (Criteria) this;
        }

        public Criteria andTotalTransFeeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_trans_fee between", value1, value2, "totalTransFee");
            return (Criteria) this;
        }

        public Criteria andTotalTransFeeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_trans_fee not between", value1, value2, "totalTransFee");
            return (Criteria) this;
        }

        public Criteria andAdjustFlagIsNull() {
            addCriterion("adjust_flag is null");
            return (Criteria) this;
        }

        public Criteria andAdjustFlagIsNotNull() {
            addCriterion("adjust_flag is not null");
            return (Criteria) this;
        }

        public Criteria andAdjustFlagEqualTo(String value) {
            addCriterion("adjust_flag =", value, "adjustFlag");
            return (Criteria) this;
        }

        public Criteria andAdjustFlagNotEqualTo(String value) {
            addCriterion("adjust_flag <>", value, "adjustFlag");
            return (Criteria) this;
        }

        public Criteria andAdjustFlagGreaterThan(String value) {
            addCriterion("adjust_flag >", value, "adjustFlag");
            return (Criteria) this;
        }

        public Criteria andAdjustFlagGreaterThanOrEqualTo(String value) {
            addCriterion("adjust_flag >=", value, "adjustFlag");
            return (Criteria) this;
        }

        public Criteria andAdjustFlagLessThan(String value) {
            addCriterion("adjust_flag <", value, "adjustFlag");
            return (Criteria) this;
        }

        public Criteria andAdjustFlagLessThanOrEqualTo(String value) {
            addCriterion("adjust_flag <=", value, "adjustFlag");
            return (Criteria) this;
        }

        public Criteria andAdjustFlagLike(String value) {
            addCriterion("adjust_flag like", value, "adjustFlag");
            return (Criteria) this;
        }

        public Criteria andAdjustFlagNotLike(String value) {
            addCriterion("adjust_flag not like", value, "adjustFlag");
            return (Criteria) this;
        }

        public Criteria andAdjustFlagIn(List<String> values) {
            addCriterion("adjust_flag in", values, "adjustFlag");
            return (Criteria) this;
        }

        public Criteria andAdjustFlagNotIn(List<String> values) {
            addCriterion("adjust_flag not in", values, "adjustFlag");
            return (Criteria) this;
        }

        public Criteria andAdjustFlagBetween(String value1, String value2) {
            addCriterion("adjust_flag between", value1, value2, "adjustFlag");
            return (Criteria) this;
        }

        public Criteria andAdjustFlagNotBetween(String value1, String value2) {
            addCriterion("adjust_flag not between", value1, value2, "adjustFlag");
            return (Criteria) this;
        }

        public Criteria andTransDirectIsNull() {
            addCriterion("trans_direct is null");
            return (Criteria) this;
        }

        public Criteria andTransDirectIsNotNull() {
            addCriterion("trans_direct is not null");
            return (Criteria) this;
        }

        public Criteria andTransDirectEqualTo(String value) {
            addCriterion("trans_direct =", value, "transDirect");
            return (Criteria) this;
        }

        public Criteria andTransDirectNotEqualTo(String value) {
            addCriterion("trans_direct <>", value, "transDirect");
            return (Criteria) this;
        }

        public Criteria andTransDirectGreaterThan(String value) {
            addCriterion("trans_direct >", value, "transDirect");
            return (Criteria) this;
        }

        public Criteria andTransDirectGreaterThanOrEqualTo(String value) {
            addCriterion("trans_direct >=", value, "transDirect");
            return (Criteria) this;
        }

        public Criteria andTransDirectLessThan(String value) {
            addCriterion("trans_direct <", value, "transDirect");
            return (Criteria) this;
        }

        public Criteria andTransDirectLessThanOrEqualTo(String value) {
            addCriterion("trans_direct <=", value, "transDirect");
            return (Criteria) this;
        }

        public Criteria andTransDirectLike(String value) {
            addCriterion("trans_direct like", value, "transDirect");
            return (Criteria) this;
        }

        public Criteria andTransDirectNotLike(String value) {
            addCriterion("trans_direct not like", value, "transDirect");
            return (Criteria) this;
        }

        public Criteria andTransDirectIn(List<String> values) {
            addCriterion("trans_direct in", values, "transDirect");
            return (Criteria) this;
        }

        public Criteria andTransDirectNotIn(List<String> values) {
            addCriterion("trans_direct not in", values, "transDirect");
            return (Criteria) this;
        }

        public Criteria andTransDirectBetween(String value1, String value2) {
            addCriterion("trans_direct between", value1, value2, "transDirect");
            return (Criteria) this;
        }

        public Criteria andTransDirectNotBetween(String value1, String value2) {
            addCriterion("trans_direct not between", value1, value2, "transDirect");
            return (Criteria) this;
        }

        public Criteria andTransferReasonIsNull() {
            addCriterion("transfer_reason is null");
            return (Criteria) this;
        }

        public Criteria andTransferReasonIsNotNull() {
            addCriterion("transfer_reason is not null");
            return (Criteria) this;
        }

        public Criteria andTransferReasonEqualTo(String value) {
            addCriterion("transfer_reason =", value, "transferReason");
            return (Criteria) this;
        }

        public Criteria andTransferReasonNotEqualTo(String value) {
            addCriterion("transfer_reason <>", value, "transferReason");
            return (Criteria) this;
        }

        public Criteria andTransferReasonGreaterThan(String value) {
            addCriterion("transfer_reason >", value, "transferReason");
            return (Criteria) this;
        }

        public Criteria andTransferReasonGreaterThanOrEqualTo(String value) {
            addCriterion("transfer_reason >=", value, "transferReason");
            return (Criteria) this;
        }

        public Criteria andTransferReasonLessThan(String value) {
            addCriterion("transfer_reason <", value, "transferReason");
            return (Criteria) this;
        }

        public Criteria andTransferReasonLessThanOrEqualTo(String value) {
            addCriterion("transfer_reason <=", value, "transferReason");
            return (Criteria) this;
        }

        public Criteria andTransferReasonLike(String value) {
            addCriterion("transfer_reason like", value, "transferReason");
            return (Criteria) this;
        }

        public Criteria andTransferReasonNotLike(String value) {
            addCriterion("transfer_reason not like", value, "transferReason");
            return (Criteria) this;
        }

        public Criteria andTransferReasonIn(List<String> values) {
            addCriterion("transfer_reason in", values, "transferReason");
            return (Criteria) this;
        }

        public Criteria andTransferReasonNotIn(List<String> values) {
            addCriterion("transfer_reason not in", values, "transferReason");
            return (Criteria) this;
        }

        public Criteria andTransferReasonBetween(String value1, String value2) {
            addCriterion("transfer_reason between", value1, value2, "transferReason");
            return (Criteria) this;
        }

        public Criteria andTransferReasonNotBetween(String value1, String value2) {
            addCriterion("transfer_reason not between", value1, value2, "transferReason");
            return (Criteria) this;
        }

        public Criteria andDivDtIsNull() {
            addCriterion("div_dt is null");
            return (Criteria) this;
        }

        public Criteria andDivDtIsNotNull() {
            addCriterion("div_dt is not null");
            return (Criteria) this;
        }

        public Criteria andDivDtEqualTo(String value) {
            addCriterion("div_dt =", value, "divDt");
            return (Criteria) this;
        }

        public Criteria andDivDtNotEqualTo(String value) {
            addCriterion("div_dt <>", value, "divDt");
            return (Criteria) this;
        }

        public Criteria andDivDtGreaterThan(String value) {
            addCriterion("div_dt >", value, "divDt");
            return (Criteria) this;
        }

        public Criteria andDivDtGreaterThanOrEqualTo(String value) {
            addCriterion("div_dt >=", value, "divDt");
            return (Criteria) this;
        }

        public Criteria andDivDtLessThan(String value) {
            addCriterion("div_dt <", value, "divDt");
            return (Criteria) this;
        }

        public Criteria andDivDtLessThanOrEqualTo(String value) {
            addCriterion("div_dt <=", value, "divDt");
            return (Criteria) this;
        }

        public Criteria andDivDtLike(String value) {
            addCriterion("div_dt like", value, "divDt");
            return (Criteria) this;
        }

        public Criteria andDivDtNotLike(String value) {
            addCriterion("div_dt not like", value, "divDt");
            return (Criteria) this;
        }

        public Criteria andDivDtIn(List<String> values) {
            addCriterion("div_dt in", values, "divDt");
            return (Criteria) this;
        }

        public Criteria andDivDtNotIn(List<String> values) {
            addCriterion("div_dt not in", values, "divDt");
            return (Criteria) this;
        }

        public Criteria andDivDtBetween(String value1, String value2) {
            addCriterion("div_dt between", value1, value2, "divDt");
            return (Criteria) this;
        }

        public Criteria andDivDtNotBetween(String value1, String value2) {
            addCriterion("div_dt not between", value1, value2, "divDt");
            return (Criteria) this;
        }

        public Criteria andOriginSerialNoIsNull() {
            addCriterion("origin_serial_no is null");
            return (Criteria) this;
        }

        public Criteria andOriginSerialNoIsNotNull() {
            addCriterion("origin_serial_no is not null");
            return (Criteria) this;
        }

        public Criteria andOriginSerialNoEqualTo(String value) {
            addCriterion("origin_serial_no =", value, "originSerialNo");
            return (Criteria) this;
        }

        public Criteria andOriginSerialNoNotEqualTo(String value) {
            addCriterion("origin_serial_no <>", value, "originSerialNo");
            return (Criteria) this;
        }

        public Criteria andOriginSerialNoGreaterThan(String value) {
            addCriterion("origin_serial_no >", value, "originSerialNo");
            return (Criteria) this;
        }

        public Criteria andOriginSerialNoGreaterThanOrEqualTo(String value) {
            addCriterion("origin_serial_no >=", value, "originSerialNo");
            return (Criteria) this;
        }

        public Criteria andOriginSerialNoLessThan(String value) {
            addCriterion("origin_serial_no <", value, "originSerialNo");
            return (Criteria) this;
        }

        public Criteria andOriginSerialNoLessThanOrEqualTo(String value) {
            addCriterion("origin_serial_no <=", value, "originSerialNo");
            return (Criteria) this;
        }

        public Criteria andOriginSerialNoLike(String value) {
            addCriterion("origin_serial_no like", value, "originSerialNo");
            return (Criteria) this;
        }

        public Criteria andOriginSerialNoNotLike(String value) {
            addCriterion("origin_serial_no not like", value, "originSerialNo");
            return (Criteria) this;
        }

        public Criteria andOriginSerialNoIn(List<String> values) {
            addCriterion("origin_serial_no in", values, "originSerialNo");
            return (Criteria) this;
        }

        public Criteria andOriginSerialNoNotIn(List<String> values) {
            addCriterion("origin_serial_no not in", values, "originSerialNo");
            return (Criteria) this;
        }

        public Criteria andOriginSerialNoBetween(String value1, String value2) {
            addCriterion("origin_serial_no between", value1, value2, "originSerialNo");
            return (Criteria) this;
        }

        public Criteria andOriginSerialNoNotBetween(String value1, String value2) {
            addCriterion("origin_serial_no not between", value1, value2, "originSerialNo");
            return (Criteria) this;
        }

        public Criteria andPayOutStatusIsNull() {
            addCriterion("pay_out_status is null");
            return (Criteria) this;
        }

        public Criteria andPayOutStatusIsNotNull() {
            addCriterion("pay_out_status is not null");
            return (Criteria) this;
        }

        public Criteria andPayOutStatusEqualTo(String value) {
            addCriterion("pay_out_status =", value, "payOutStatus");
            return (Criteria) this;
        }

        public Criteria andPayOutStatusNotEqualTo(String value) {
            addCriterion("pay_out_status <>", value, "payOutStatus");
            return (Criteria) this;
        }

        public Criteria andPayOutStatusGreaterThan(String value) {
            addCriterion("pay_out_status >", value, "payOutStatus");
            return (Criteria) this;
        }

        public Criteria andPayOutStatusGreaterThanOrEqualTo(String value) {
            addCriterion("pay_out_status >=", value, "payOutStatus");
            return (Criteria) this;
        }

        public Criteria andPayOutStatusLessThan(String value) {
            addCriterion("pay_out_status <", value, "payOutStatus");
            return (Criteria) this;
        }

        public Criteria andPayOutStatusLessThanOrEqualTo(String value) {
            addCriterion("pay_out_status <=", value, "payOutStatus");
            return (Criteria) this;
        }

        public Criteria andPayOutStatusLike(String value) {
            addCriterion("pay_out_status like", value, "payOutStatus");
            return (Criteria) this;
        }

        public Criteria andPayOutStatusNotLike(String value) {
            addCriterion("pay_out_status not like", value, "payOutStatus");
            return (Criteria) this;
        }

        public Criteria andPayOutStatusIn(List<String> values) {
            addCriterion("pay_out_status in", values, "payOutStatus");
            return (Criteria) this;
        }

        public Criteria andPayOutStatusNotIn(List<String> values) {
            addCriterion("pay_out_status not in", values, "payOutStatus");
            return (Criteria) this;
        }

        public Criteria andPayOutStatusBetween(String value1, String value2) {
            addCriterion("pay_out_status between", value1, value2, "payOutStatus");
            return (Criteria) this;
        }

        public Criteria andPayOutStatusNotBetween(String value1, String value2) {
            addCriterion("pay_out_status not between", value1, value2, "payOutStatus");
            return (Criteria) this;
        }

        public Criteria andPayOutDtIsNull() {
            addCriterion("pay_out_dt is null");
            return (Criteria) this;
        }

        public Criteria andPayOutDtIsNotNull() {
            addCriterion("pay_out_dt is not null");
            return (Criteria) this;
        }

        public Criteria andPayOutDtEqualTo(String value) {
            addCriterion("pay_out_dt =", value, "payOutDt");
            return (Criteria) this;
        }

        public Criteria andPayOutDtNotEqualTo(String value) {
            addCriterion("pay_out_dt <>", value, "payOutDt");
            return (Criteria) this;
        }

        public Criteria andPayOutDtGreaterThan(String value) {
            addCriterion("pay_out_dt >", value, "payOutDt");
            return (Criteria) this;
        }

        public Criteria andPayOutDtGreaterThanOrEqualTo(String value) {
            addCriterion("pay_out_dt >=", value, "payOutDt");
            return (Criteria) this;
        }

        public Criteria andPayOutDtLessThan(String value) {
            addCriterion("pay_out_dt <", value, "payOutDt");
            return (Criteria) this;
        }

        public Criteria andPayOutDtLessThanOrEqualTo(String value) {
            addCriterion("pay_out_dt <=", value, "payOutDt");
            return (Criteria) this;
        }

        public Criteria andPayOutDtLike(String value) {
            addCriterion("pay_out_dt like", value, "payOutDt");
            return (Criteria) this;
        }

        public Criteria andPayOutDtNotLike(String value) {
            addCriterion("pay_out_dt not like", value, "payOutDt");
            return (Criteria) this;
        }

        public Criteria andPayOutDtIn(List<String> values) {
            addCriterion("pay_out_dt in", values, "payOutDt");
            return (Criteria) this;
        }

        public Criteria andPayOutDtNotIn(List<String> values) {
            addCriterion("pay_out_dt not in", values, "payOutDt");
            return (Criteria) this;
        }

        public Criteria andPayOutDtBetween(String value1, String value2) {
            addCriterion("pay_out_dt between", value1, value2, "payOutDt");
            return (Criteria) this;
        }

        public Criteria andPayOutDtNotBetween(String value1, String value2) {
            addCriterion("pay_out_dt not between", value1, value2, "payOutDt");
            return (Criteria) this;
        }

        public Criteria andContinuanceFlagIsNull() {
            addCriterion("continuance_flag is null");
            return (Criteria) this;
        }

        public Criteria andContinuanceFlagIsNotNull() {
            addCriterion("continuance_flag is not null");
            return (Criteria) this;
        }

        public Criteria andContinuanceFlagEqualTo(String value) {
            addCriterion("continuance_flag =", value, "continuanceFlag");
            return (Criteria) this;
        }

        public Criteria andContinuanceFlagNotEqualTo(String value) {
            addCriterion("continuance_flag <>", value, "continuanceFlag");
            return (Criteria) this;
        }

        public Criteria andContinuanceFlagGreaterThan(String value) {
            addCriterion("continuance_flag >", value, "continuanceFlag");
            return (Criteria) this;
        }

        public Criteria andContinuanceFlagGreaterThanOrEqualTo(String value) {
            addCriterion("continuance_flag >=", value, "continuanceFlag");
            return (Criteria) this;
        }

        public Criteria andContinuanceFlagLessThan(String value) {
            addCriterion("continuance_flag <", value, "continuanceFlag");
            return (Criteria) this;
        }

        public Criteria andContinuanceFlagLessThanOrEqualTo(String value) {
            addCriterion("continuance_flag <=", value, "continuanceFlag");
            return (Criteria) this;
        }

        public Criteria andContinuanceFlagLike(String value) {
            addCriterion("continuance_flag like", value, "continuanceFlag");
            return (Criteria) this;
        }

        public Criteria andContinuanceFlagNotLike(String value) {
            addCriterion("continuance_flag not like", value, "continuanceFlag");
            return (Criteria) this;
        }

        public Criteria andContinuanceFlagIn(List<String> values) {
            addCriterion("continuance_flag in", values, "continuanceFlag");
            return (Criteria) this;
        }

        public Criteria andContinuanceFlagNotIn(List<String> values) {
            addCriterion("continuance_flag not in", values, "continuanceFlag");
            return (Criteria) this;
        }

        public Criteria andContinuanceFlagBetween(String value1, String value2) {
            addCriterion("continuance_flag between", value1, value2, "continuanceFlag");
            return (Criteria) this;
        }

        public Criteria andContinuanceFlagNotBetween(String value1, String value2) {
            addCriterion("continuance_flag not between", value1, value2, "continuanceFlag");
            return (Criteria) this;
        }

        public Criteria andStageFlagIsNull() {
            addCriterion("stage_flag is null");
            return (Criteria) this;
        }

        public Criteria andStageFlagIsNotNull() {
            addCriterion("stage_flag is not null");
            return (Criteria) this;
        }

        public Criteria andStageFlagEqualTo(String value) {
            addCriterion("stage_flag =", value, "stageFlag");
            return (Criteria) this;
        }

        public Criteria andStageFlagNotEqualTo(String value) {
            addCriterion("stage_flag <>", value, "stageFlag");
            return (Criteria) this;
        }

        public Criteria andStageFlagGreaterThan(String value) {
            addCriterion("stage_flag >", value, "stageFlag");
            return (Criteria) this;
        }

        public Criteria andStageFlagGreaterThanOrEqualTo(String value) {
            addCriterion("stage_flag >=", value, "stageFlag");
            return (Criteria) this;
        }

        public Criteria andStageFlagLessThan(String value) {
            addCriterion("stage_flag <", value, "stageFlag");
            return (Criteria) this;
        }

        public Criteria andStageFlagLessThanOrEqualTo(String value) {
            addCriterion("stage_flag <=", value, "stageFlag");
            return (Criteria) this;
        }

        public Criteria andStageFlagLike(String value) {
            addCriterion("stage_flag like", value, "stageFlag");
            return (Criteria) this;
        }

        public Criteria andStageFlagNotLike(String value) {
            addCriterion("stage_flag not like", value, "stageFlag");
            return (Criteria) this;
        }

        public Criteria andStageFlagIn(List<String> values) {
            addCriterion("stage_flag in", values, "stageFlag");
            return (Criteria) this;
        }

        public Criteria andStageFlagNotIn(List<String> values) {
            addCriterion("stage_flag not in", values, "stageFlag");
            return (Criteria) this;
        }

        public Criteria andStageFlagBetween(String value1, String value2) {
            addCriterion("stage_flag between", value1, value2, "stageFlag");
            return (Criteria) this;
        }

        public Criteria andStageFlagNotBetween(String value1, String value2) {
            addCriterion("stage_flag not between", value1, value2, "stageFlag");
            return (Criteria) this;
        }

        public Criteria andTransferPriceIsNull() {
            addCriterion("transfer_price is null");
            return (Criteria) this;
        }

        public Criteria andTransferPriceIsNotNull() {
            addCriterion("transfer_price is not null");
            return (Criteria) this;
        }

        public Criteria andTransferPriceEqualTo(BigDecimal value) {
            addCriterion("transfer_price =", value, "transferPrice");
            return (Criteria) this;
        }

        public Criteria andTransferPriceNotEqualTo(BigDecimal value) {
            addCriterion("transfer_price <>", value, "transferPrice");
            return (Criteria) this;
        }

        public Criteria andTransferPriceGreaterThan(BigDecimal value) {
            addCriterion("transfer_price >", value, "transferPrice");
            return (Criteria) this;
        }

        public Criteria andTransferPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("transfer_price >=", value, "transferPrice");
            return (Criteria) this;
        }

        public Criteria andTransferPriceLessThan(BigDecimal value) {
            addCriterion("transfer_price <", value, "transferPrice");
            return (Criteria) this;
        }

        public Criteria andTransferPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("transfer_price <=", value, "transferPrice");
            return (Criteria) this;
        }

        public Criteria andTransferPriceIn(List<BigDecimal> values) {
            addCriterion("transfer_price in", values, "transferPrice");
            return (Criteria) this;
        }

        public Criteria andTransferPriceNotIn(List<BigDecimal> values) {
            addCriterion("transfer_price not in", values, "transferPrice");
            return (Criteria) this;
        }

        public Criteria andTransferPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("transfer_price between", value1, value2, "transferPrice");
            return (Criteria) this;
        }

        public Criteria andTransferPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("transfer_price not between", value1, value2, "transferPrice");
            return (Criteria) this;
        }

        public Criteria andIsNoTradeTransferIsNull() {
            addCriterion("IS_NO_TRADE_TRANSFER is null");
            return (Criteria) this;
        }

        public Criteria andIsNoTradeTransferIsNotNull() {
            addCriterion("IS_NO_TRADE_TRANSFER is not null");
            return (Criteria) this;
        }

        public Criteria andIsNoTradeTransferEqualTo(String value) {
            addCriterion("IS_NO_TRADE_TRANSFER =", value, "isNoTradeTransfer");
            return (Criteria) this;
        }

        public Criteria andIsNoTradeTransferNotEqualTo(String value) {
            addCriterion("IS_NO_TRADE_TRANSFER <>", value, "isNoTradeTransfer");
            return (Criteria) this;
        }

        public Criteria andIsNoTradeTransferGreaterThan(String value) {
            addCriterion("IS_NO_TRADE_TRANSFER >", value, "isNoTradeTransfer");
            return (Criteria) this;
        }

        public Criteria andIsNoTradeTransferGreaterThanOrEqualTo(String value) {
            addCriterion("IS_NO_TRADE_TRANSFER >=", value, "isNoTradeTransfer");
            return (Criteria) this;
        }

        public Criteria andIsNoTradeTransferLessThan(String value) {
            addCriterion("IS_NO_TRADE_TRANSFER <", value, "isNoTradeTransfer");
            return (Criteria) this;
        }

        public Criteria andIsNoTradeTransferLessThanOrEqualTo(String value) {
            addCriterion("IS_NO_TRADE_TRANSFER <=", value, "isNoTradeTransfer");
            return (Criteria) this;
        }

        public Criteria andIsNoTradeTransferLike(String value) {
            addCriterion("IS_NO_TRADE_TRANSFER like", value, "isNoTradeTransfer");
            return (Criteria) this;
        }

        public Criteria andIsNoTradeTransferNotLike(String value) {
            addCriterion("IS_NO_TRADE_TRANSFER not like", value, "isNoTradeTransfer");
            return (Criteria) this;
        }

        public Criteria andIsNoTradeTransferIn(List<String> values) {
            addCriterion("IS_NO_TRADE_TRANSFER in", values, "isNoTradeTransfer");
            return (Criteria) this;
        }

        public Criteria andIsNoTradeTransferNotIn(List<String> values) {
            addCriterion("IS_NO_TRADE_TRANSFER not in", values, "isNoTradeTransfer");
            return (Criteria) this;
        }

        public Criteria andIsNoTradeTransferBetween(String value1, String value2) {
            addCriterion("IS_NO_TRADE_TRANSFER between", value1, value2, "isNoTradeTransfer");
            return (Criteria) this;
        }

        public Criteria andIsNoTradeTransferNotBetween(String value1, String value2) {
            addCriterion("IS_NO_TRADE_TRANSFER not between", value1, value2, "isNoTradeTransfer");
            return (Criteria) this;
        }

        public Criteria andTaAckNoIsNull() {
            addCriterion("ta_ack_no is null");
            return (Criteria) this;
        }

        public Criteria andTaAckNoIsNotNull() {
            addCriterion("ta_ack_no is not null");
            return (Criteria) this;
        }

        public Criteria andTaAckNoEqualTo(String value) {
            addCriterion("ta_ack_no =", value, "taAckNo");
            return (Criteria) this;
        }

        public Criteria andTaAckNoNotEqualTo(String value) {
            addCriterion("ta_ack_no <>", value, "taAckNo");
            return (Criteria) this;
        }

        public Criteria andTaAckNoGreaterThan(String value) {
            addCriterion("ta_ack_no >", value, "taAckNo");
            return (Criteria) this;
        }

        public Criteria andTaAckNoGreaterThanOrEqualTo(String value) {
            addCriterion("ta_ack_no >=", value, "taAckNo");
            return (Criteria) this;
        }

        public Criteria andTaAckNoLessThan(String value) {
            addCriterion("ta_ack_no <", value, "taAckNo");
            return (Criteria) this;
        }

        public Criteria andTaAckNoLessThanOrEqualTo(String value) {
            addCriterion("ta_ack_no <=", value, "taAckNo");
            return (Criteria) this;
        }

        public Criteria andTaAckNoLike(String value) {
            addCriterion("ta_ack_no like", value, "taAckNo");
            return (Criteria) this;
        }

        public Criteria andTaAckNoNotLike(String value) {
            addCriterion("ta_ack_no not like", value, "taAckNo");
            return (Criteria) this;
        }

        public Criteria andTaAckNoIn(List<String> values) {
            addCriterion("ta_ack_no in", values, "taAckNo");
            return (Criteria) this;
        }

        public Criteria andTaAckNoNotIn(List<String> values) {
            addCriterion("ta_ack_no not in", values, "taAckNo");
            return (Criteria) this;
        }

        public Criteria andTaAckNoBetween(String value1, String value2) {
            addCriterion("ta_ack_no between", value1, value2, "taAckNo");
            return (Criteria) this;
        }

        public Criteria andTaAckNoNotBetween(String value1, String value2) {
            addCriterion("ta_ack_no not between", value1, value2, "taAckNo");
            return (Criteria) this;
        }

        public Criteria andDiscountAmtIsNull() {
            addCriterion("discount_amt is null");
            return (Criteria) this;
        }

        public Criteria andDiscountAmtIsNotNull() {
            addCriterion("discount_amt is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountAmtEqualTo(BigDecimal value) {
            addCriterion("discount_amt =", value, "discountAmt");
            return (Criteria) this;
        }

        public Criteria andDiscountAmtNotEqualTo(BigDecimal value) {
            addCriterion("discount_amt <>", value, "discountAmt");
            return (Criteria) this;
        }

        public Criteria andDiscountAmtGreaterThan(BigDecimal value) {
            addCriterion("discount_amt >", value, "discountAmt");
            return (Criteria) this;
        }

        public Criteria andDiscountAmtGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("discount_amt >=", value, "discountAmt");
            return (Criteria) this;
        }

        public Criteria andDiscountAmtLessThan(BigDecimal value) {
            addCriterion("discount_amt <", value, "discountAmt");
            return (Criteria) this;
        }

        public Criteria andDiscountAmtLessThanOrEqualTo(BigDecimal value) {
            addCriterion("discount_amt <=", value, "discountAmt");
            return (Criteria) this;
        }

        public Criteria andDiscountAmtIn(List<BigDecimal> values) {
            addCriterion("discount_amt in", values, "discountAmt");
            return (Criteria) this;
        }

        public Criteria andDiscountAmtNotIn(List<BigDecimal> values) {
            addCriterion("discount_amt not in", values, "discountAmt");
            return (Criteria) this;
        }

        public Criteria andDiscountAmtBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("discount_amt between", value1, value2, "discountAmt");
            return (Criteria) this;
        }

        public Criteria andDiscountAmtNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("discount_amt not between", value1, value2, "discountAmt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}